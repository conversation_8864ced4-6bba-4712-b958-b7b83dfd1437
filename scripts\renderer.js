const selectAndScanBtn = document.getElementById('select-and-scan-btn')
const trackList = document.getElementById('track-list')
const themeToggle = document.getElementById('theme-toggle')
const trackCountSpan = document.getElementById('track-count');
const lastUpdateSpan = document.getElementById('last-update');
const clearLibraryBtn = document.getElementById('clear-library-btn');
const searchInput = document.getElementById('search-input');
const clearSearchBtn = document.getElementById('clear-search-btn');
const searchResultsCount = document.getElementById('search-results-count');
const resultsCountSpan = document.getElementById('results-count');
const openDbBtn = document.getElementById('open-db-btn');
const folderStatus = document.getElementById('folder-status');
const statusFolderName = document.getElementById('status-folder-name');

// Playlist elements
const createPlaylistBtn = document.getElementById('create-playlist-btn');
const playlistsContainer = document.getElementById('playlists-container');
const playlistTracksContainer = document.getElementById('playlist-tracks-container');
const currentPlaylistName = document.getElementById('current-playlist-name');
const playlistTracks = document.getElementById('playlist-tracks');
const backToPlaylistsBtn = document.getElementById('back-to-playlists-btn');
const addTracksToPlaylistBtn = document.getElementById('add-tracks-to-playlist-btn');
const swapTracksBtn = document.getElementById('swap-tracks-btn');
const renamePlaylistBtn = document.getElementById('rename-playlist-btn');
const deletePlaylistBtn = document.getElementById('delete-playlist-btn');
const exportPlaylistM3u8Btn = document.getElementById('export-playlist-m3u8-btn');

// Playlist popup elements
const createPlaylistPopup = document.getElementById('create-playlist-popup');
const closePlaylistPopupBtn = document.getElementById('close-playlist-popup-btn');
const createPlaylistForm = document.getElementById('create-playlist-form');
const playlistNameInput = document.getElementById('playlist-name');
const cancelPlaylistBtn = document.getElementById('cancel-playlist-btn');
const savePlaylistBtn = document.getElementById('save-playlist-btn');

// Add to playlist popup elements
const addToPlaylistPopup = document.getElementById('add-to-playlist-popup');
const closeAddToPlaylistBtn = document.getElementById('close-add-to-playlist-btn');
const playlistOptions = document.getElementById('playlist-options');
const cancelAddToPlaylistBtn = document.getElementById('cancel-add-to-playlist-btn');
const createNewPlaylistBtn = document.getElementById('create-new-playlist-btn');

let currentFolderPath = null
let allTracks = [] // Memorizza tutte le tracce caricate
let allPlaylists = [] // Memorizza tutte le playlist
let currentPlaylist = null // Playlist attualmente visualizzata
let selectedTracksForPlaylist = [] // Tracce selezionate per aggiungere a una playlist
let selectedTracks = [] // Tracce selezionate con checkbox per drag and drop multiplo

// Function to get playlists for a track
async function getTrackPlaylists(trackId) {
    if (!trackId) return [];

    try {
        const result = await window.track.getPlaylists({ trackId });
        if (result.success) {
            return result.playlists || [];
        }
        return [];
    } catch (error) {
        console.error(`Failed to get playlists for track ${trackId}:`, error);
        return [];
    }
}

function renderTrackList(files, searchTerm = '') {
    console.log('renderTrackList called with', files ? files.length : 0, 'files');

    if (!files || files.length === 0) {
        console.log('No files to render, showing empty state');
        trackList.innerHTML = `
            <div class="empty-state">
                ${searchTerm ? `Nessun risultato trovato per "${searchTerm}"` : 'No music files found or library is empty.'}
            </div>
        `
        return
    }

    console.log('Rendering track list with', files.length, 'files');

    // Log the first few files for debugging
    if (files.length > 0) {
        console.log('First 3 files:', files.slice(0, 3));
    }

    // Funzione per evidenziare il testo cercato
    function highlightText(text, term) {
        if (!term || !text) return text;

        // Escape caratteri speciali nelle regex
        const escapedTerm = term.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        const regex = new RegExp(`(${escapedTerm})`, 'gi');
        return text.replace(regex, '<span class="highlight">$1</span>');
    }

    // Render track items
    const renderTrackItems = async () => {
        const trackItemsPromises = files.map(async file => {
            const title = file.title || file.name;
            const artist = file.artist || '';
            const showArtist = artist && artist !== 'Unknown Artist';
            const tags = file.tags ? file.tags.split(',').map(tag => tag.trim()).filter(tag => tag) : [];

            // Format duration if available
            let durationText = '';
            if (file.duration) {
                const minutes = Math.floor(file.duration / 60);
                const seconds = Math.floor(file.duration % 60);
                durationText = `${minutes}:${seconds.toString().padStart(2, '0')}`;
            }

            // Check if track is new (created within the last 3 months)
            let isNew = false;
            let newBadgeHtml = '';
            if (file.updatedAt) {
                const updatedDate = new Date(file.updatedAt);
                const currentDate = new Date();
                const threeMonthsAgo = new Date();
                threeMonthsAgo.setMonth(currentDate.getMonth() - 3);

                // if (updatedDate > threeMonthsAgo) {
                //     isNew = true;
                //     newBadgeHtml = `<span class="new-badge">new</span>`;
                // }
            }

            // Evidenzia il testo cercato se presente
            const highlightedTitle = searchTerm ? highlightText(title, searchTerm) : title;
            const highlightedArtist = searchTerm && showArtist ? highlightText(artist, searchTerm) : artist;

            // Controlla se la ricerca nel percorso è abilitata
            const includePathInSearch = document.getElementById('search-include-path').checked;
            const path = file.path || '';
            const highlightedPath = searchTerm && includePathInSearch ? highlightText(path, searchTerm) : path;
            // Mostra sempre il percorso, non solo durante la ricerca
            const showPath = true;

            // Create tags HTML if tags exist
            const tagsHtml = tags.length > 0
                ? `<div class="track-tags">${tags.map(tag =>
                    `<span class="tag">${searchTerm ? highlightText(tag, searchTerm) : tag}</span>`
                ).join('')}</div>`
                : '';

            // Get playlists for this track
            let playlistsHtml = '';
            if (file.id) {
                const playlists = await getTrackPlaylists(file.id);
                if (playlists.length > 0) {
                    playlistsHtml = `
                        <div class="track-playlists">
                            <span class="track-playlists-label">Playlist:</span>
                            <div class="track-playlists-list">
                                ${playlists.map(playlist =>
                        `<span class="track-playlist" data-playlist-id="${playlist.id}">${playlist.name}</span>`
                    ).join('')}
                            </div>
                        </div>
                    `;
                }
            }

            return `
            <div class="track-item ${selectedTracks.includes(file.id) ? 'selected' : ''}" data-track-id="${file.id || ''}" data-path="${file.path}" data-title="${title}" data-artist="${artist}" draggable="true">
                <div class="track-select">
                    <input type="checkbox" class="track-checkbox" ${selectedTracks.includes(file.id) ? 'checked' : ''}>
                </div>
                <div class="drag-handle">
                    <svg class="icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="8" y1="6" x2="16" y2="6"></line>
                        <line x1="8" y1="12" x2="16" y2="12"></line>
                        <line x1="8" y1="18" x2="16" y2="18"></line>
                    </svg>
                </div>
                <div class="track-info">
                    <div class="track-main-info">
                        <div class="track-title-container">
                            <div class="track-title">${highlightedTitle}</div>
                        </div>
                        ${showArtist ? `<div class="track-artist">${highlightedArtist}</div>` : ''}
                        ${durationText ? `<span class="track-duration">${durationText} ${isNew ? newBadgeHtml : ''}</span>` : `${isNew ? newBadgeHtml : ''}`}
                    </div>
                    ${tagsHtml}
                    ${showPath ? `
                    <div class="track-details">
                        <span class="track-path">${highlightedPath}</span>
                    </div>
                    ` : ''}
                    ${playlistsHtml}
                </div>
                <div class="track-actions">
                    <button class="track-button play-button" data-path="${file.path}" data-track-id="${file.id || ''}">
                        <svg class="icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <polygon points="5 3 19 12 5 21 5 3"></polygon>
                        </svg>
                    </button>
                    <button class="track-button edit-button" data-path="${file.path}" data-title="${title}" data-artist="${artist}" data-tags="${file.tags || ''}">
                        <svg class="icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                            <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                        </svg>
                    </button>
                </div>
            </div>
            `;
        });

        // Wait for all track items to be rendered with their playlists
        const trackItems = await Promise.all(trackItemsPromises);
        trackList.innerHTML = trackItems.join('');

        // Add event listeners to playlist names to open the playlist when clicked
        document.querySelectorAll('.track-playlist').forEach(playlistElement => {
            playlistElement.addEventListener('click', (event) => {
                event.stopPropagation();
                const playlistId = parseInt(playlistElement.dataset.playlistId);
                if (!isNaN(playlistId)) {
                    openPlaylist(playlistId);
                }
            });
        });

        console.log('Track list rendered successfully');
    };

    // Execute the async rendering function
    renderTrackItems();
}

function updateStats(stats) {
    // Aggiorna il conteggio delle tracce nella barra di stato
    trackCountSpan.textContent = stats.totalTracks || 0;

    // Aggiorna la data dell'ultimo aggiornamento nella barra di stato formattandola come dd/mm/yyyy
    if (stats.lastUpdate) {
        const date = new Date(stats.lastUpdate);
        const day = date.getDate().toString().padStart(2, '0');
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const year = date.getFullYear();
        lastUpdateSpan.textContent = `${day}/${month}/${year}`;
    } else {
        lastUpdateSpan.textContent = 'Never';
    }
}

// Utility function to normalize text by removing accents and diacritics
function normalizeText(text) {
    if (typeof text !== 'string') {
        return '';
    }

    // Convert to lowercase and normalize Unicode characters
    // NFD (Normalization Form Decomposed) separates base characters from combining marks
    return text.toLowerCase()
        .normalize('NFD')
        // Remove combining diacritical marks (Unicode category Mn)
        .replace(/[\u0300-\u036f]/g, '')
        // Additional replacements for characters that don't decompose properly
        .replace(/[àáâãäåæ]/g, 'a')
        .replace(/[çć]/g, 'c')
        .replace(/[èéêë]/g, 'e')
        .replace(/[ìíîï]/g, 'i')
        .replace(/[ñń]/g, 'n')
        .replace(/[òóôõöø]/g, 'o')
        .replace(/[ùúûü]/g, 'u')
        .replace(/[ýÿ]/g, 'y')
        .replace(/[ß]/g, 'ss')
        .replace(/[đ]/g, 'd')
        .replace(/[ł]/g, 'l')
        .replace(/[ř]/g, 'r')
        .replace(/[š]/g, 's')
        .replace(/[ť]/g, 't')
        .replace(/[ž]/g, 'z');
}

// Funzione per filtrare le tracce in base al testo di ricerca e ai tag attivi
function filterTracks(searchTerm) {
    // Inizia con tutte le tracce o solo quelle con i tag attivi
    let tracksToFilter = allTracks;

    // Se ci sono tag attivi, filtra prima per tag
    if (activeTagFilters.length > 0) {
        tracksToFilter = allTracks.filter(track => {
            if (!track.tags) return false;

            // Ensure case-insensitive comparison by converting all tags to lowercase
            const trackTags = track.tags.split(',').map(tag => tag.trim().toLowerCase());

            // Track should have ALL selected tags (AND logic)
            return activeTagFilters.every(tag => {
                const tagLower = tag.toLowerCase();
                return trackTags.some(trackTag => trackTag === tagLower); // trackTag is already lowercase
            });
        });
    }

    // Se non c'è testo di ricerca, restituisci le tracce filtrate per tag (o tutte se non ci sono tag attivi)
    if (!searchTerm) {
        return tracksToFilter;
    }

    // Normalize the search term for accent-insensitive search
    const normalizedTerm = normalizeText(searchTerm);

    // Controlla se la ricerca nel percorso è abilitata
    const includePathInSearch = document.getElementById('search-include-path').checked;

    // Filtra le tracce che contengono il termine di ricerca nel titolo, artista, tags o percorso (se abilitato)
    return tracksToFilter.filter(track => {
        const normalizedTitle = normalizeText(track.title || track.name || '');
        const normalizedArtist = normalizeText(track.artist || 'Unknown Artist');
        const normalizedTags = normalizeText(track.tags || '');
        const normalizedPath = includePathInSearch ? normalizeText(track.path || '') : '';

        return normalizedTitle.includes(normalizedTerm) ||
            normalizedArtist.includes(normalizedTerm) ||
            normalizedTags.includes(normalizedTerm) ||
            normalizedPath.includes(normalizedTerm);
    });
}

// Funzione per aggiornare l'interfaccia in base ai risultati della ricerca
function updateSearchResults(searchTerm) {
    const filteredTracks = filterTracks(searchTerm);

    // Aggiorna il contatore dei risultati
    if (searchTerm) {
        resultsCountSpan.textContent = filteredTracks.length;
        searchResultsCount.classList.remove('hidden');
    } else {
        searchResultsCount.classList.add('hidden');
    }

    // Renderizza le tracce filtrate
    renderTrackList(filteredTracks, searchTerm);
}

async function loadLibrary() {
    try {
        console.log('Loading library from DB...');

        // The 'library:load' handler now returns an object { files: [...], totalTracks: N, lastUpdate: ... }
        console.log('Calling window.library.load()...');
        const result = await window.library.load();
        console.log(`Loaded ${result.files ? result.files.length : 0} tracks. Stats:`, result);

        // Salva tutte le tracce nella variabile globale
        allTracks = result.files || [];

        // Get the last selected directory after loading tracks
        console.log('Retrieving last selected directory...');
        const lastDirectory = await window.library.getLastDirectory();
        console.log('Last directory from main process:', lastDirectory);

        if (lastDirectory) {
            currentFolderPath = lastDirectory;
            // Aggiorna la barra di stato con la cartella selezionata
            statusFolderName.textContent = lastDirectory.split('\\').pop();
            statusFolderName.title = lastDirectory; // Aggiungi tooltip con il percorso completo
            folderStatus.classList.remove('hidden');
            console.log('UI updated with last selected directory:', lastDirectory);
        } else {
            console.log('No last directory found');
        }

        if (allTracks.length > 0) {
            console.log('Rendering track list...');
            renderTrackList(allTracks); // Render the tracks
            console.log('Track list rendered');
        } else {
            console.log('No tracks to render');
            trackList.innerHTML = `
                <div class="empty-state">
                    No music files found or library is empty.
                </div>
            `;
        }

        console.log('Updating stats...');
        updateStats(result); // Update stats using the received data
        console.log('Stats updated');

    } catch (error) {
        console.error('Failed to load library:', error);
        trackList.innerHTML = `<div class="empty-state error">Failed to load library: ${error.message}</div>`;
        updateStats({ totalTracks: 0, lastUpdate: null });
    }
}

selectAndScanBtn.addEventListener('click', async () => {
    // Disable the button and show loading state
    selectAndScanBtn.disabled = true;
    selectAndScanBtn.innerHTML = `
        <svg class="icon spin" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <line x1="12" y1="2" x2="12" y2="6"></line>
            <line x1="12" y1="18" x2="12" y2="22"></line>
            <line x1="4.93" y1="4.93" x2="7.76" y2="7.76"></line>
            <line x1="16.24" y1="16.24" x2="19.07" y2="19.07"></line>
            <line x1="2" y1="12" x2="6" y2="12"></line>
            <line x1="18" y1="12" x2="22" y2="12"></line>
            <line x1="4.93" y1="19.07" x2="7.76" y2="16.24"></line>
            <line x1="16.24" y1="7.76" x2="19.07" y2="4.93"></line>
        </svg>
        Selecting & Scanning...
    `;

    trackList.innerHTML = `<div class="empty-state">Selecting folder and scanning library...</div>`;

    try {
        // Call the new combined function that selects a folder and scans it in one operation
        const result = await window.library.selectAndScanFolder();
        console.log('Select and scan complete, result:', result);

        // If the user canceled the folder selection dialog
        if (!result.success && result.canceled) {
            console.log('Folder selection canceled by user');
            trackList.innerHTML = allTracks.length > 0
                ? '' // Keep existing tracks if any
                : `<div class="empty-state">Select a folder and scan to view your music library</div>`;
            return;
        }

        // If the operation was successful
        if (result.success && result.path) {
            const folderPath = result.path;
            currentFolderPath = folderPath;

            // Extract folder name from path
            let folderName = folderPath;
            if (typeof folderPath === 'string') {
                // Handle both Windows and Unix paths
                folderName = folderPath.split(/[\\\/]/).pop() || folderPath;
            }

            // Update status bar with selected folder
            statusFolderName.textContent = folderName;
            statusFolderName.title = folderPath; // Add tooltip with full path
            folderStatus.classList.remove('hidden');

            // Update global tracks variable
            allTracks = result.files || [];

            // Reset search field
            searchInput.value = '';
            clearSearchBtn.classList.add('hidden');
            searchResultsCount.classList.add('hidden');

            // Render tracks and update stats
            renderTrackList(allTracks);
            updateStats(result);

            // Load and display tags immediately after scan
            console.log('Loading tags after scan...');
            await loadAllTags();
            console.log('Tags loaded and displayed');

            // Show import completion report
            showImportCompletionReport(result.stats, result.failedFiles || []);

            // Ask user if they want to add the scanned tracks to a playlist
            if (allTracks.length > 0) {
                const trackIds = allTracks.map(track => track.id).filter(id => id); // Filter out any undefined IDs
                if (trackIds.length > 0) {
                    const addToPlaylist = confirm(`Vuoi aggiungere le ${trackIds.length} tracce scansionate a una playlist?`);
                    if (addToPlaylist) {
                        openAddToPlaylistPopup(trackIds);
                    }
                }
            }
        } else {
            // Handle error case
            console.error('Select and scan failed:', result.error || 'Unknown error');
            trackList.innerHTML = `<div class="empty-state error">Select and scan failed: ${result.error || 'Unknown error'}</div>`;
            updateStats({ totalTracks: 0, lastUpdate: null }); // Reset stats on error
        }
    } catch (error) {
        console.error('Select and scan failed:', error);
        trackList.innerHTML = `<div class="empty-state error">Select and scan failed: ${error.message}</div>`;
        updateStats({ totalTracks: 0, lastUpdate: null }); // Reset stats on error
    } finally {
        // Reset button state
        selectAndScanBtn.disabled = false;
        selectAndScanBtn.innerHTML = `
            <svg class="icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"></path>
                <circle cx="17" cy="17" r="3"></circle>
                <line x1="21" y1="21" x2="19" y2="19"></line>
            </svg>
            <span>Import</span>
        `;
    }
})

// Add event listener for the clear library button
clearLibraryBtn.addEventListener('click', async () => {
    const confirmation = confirm('Are you sure you want to clear the entire music library? This action cannot be undone. This will delete all tracks, playlists, and playlist generators, but will preserve default tags.');
    if (confirmation) {
        try {
            console.log('Clearing library...');
            const result = await window.library.clear();
            console.log(`Library cleared, ${result.count} total items removed.`);

            // Reset global variables
            allTracks = [];
            currentFolderPath = null;
            allPlaylists = []; // Reset playlists as well
            currentPlaylist = null;
            allTags = []; // Reset tags
            activeTagFilters = []; // Clear active tag filters

            // Reset search field
            searchInput.value = '';
            clearSearchBtn.classList.add('hidden');
            searchResultsCount.classList.add('hidden');

            // Hide folder in status bar
            folderStatus.classList.add('hidden');

            // Clear UI
            renderTrackList([]); // Clear the track list display
            renderPlaylists(); // Clear the playlists display
            renderTagFilters(); // Clear the tag filters display
            updateStats({ totalTracks: 0, lastUpdate: null }); // Reset stats

            // Hide playlist tracks container if visible
            playlistTracksContainer.classList.add('hidden');

            // Reload tags to ensure the UI is updated (this should return an empty array since all tracks are deleted)
            await loadAllTags();

            alert(`Library cleared successfully. ${result.count} items were removed while preserving default tags.`);
        } catch (error) {
            console.error('Failed to clear library:', error);
            alert(`Failed to clear library: ${error.message}`);
        }
    }
});

// Function to update theme based on isDark status
function updateTheme(isDark) {
    document.body.classList.toggle('dark-theme', isDark);
    themeToggle.setAttribute('aria-pressed', isDark);
    // You might need to inform the main process if the theme preference should be saved
    // window.darkMode.set(isDark); // Example if you have a 'set' method exposed
}

// Theme toggle logic (existing)
themeToggle.addEventListener('click', async () => {
    const isDark = await window.darkMode.toggle()
    updateTheme(isDark)
})

// Inizializza il tema in base alle preferenze di sistema
const prefersDark = window.matchMedia('(prefers-color-scheme: dark)')
updateTheme(prefersDark.matches)

prefersDark.addEventListener('change', (e) => {
    updateTheme(e.matches)
})

// Initialize theme based on system preferences or saved preference
async function initializeTheme() {
    const isDark = await window.darkMode.isDark();
    updateTheme(isDark);

    // Listen for theme updates from main process
    window.darkMode.onUpdate((_, isSystemDark) => {
        updateTheme(isSystemDark);
    });
}

// Event listener per il campo di ricerca
searchInput.addEventListener('input', () => {
    const searchTerm = searchInput.value.trim();

    // Mostra/nascondi il pulsante di cancellazione
    if (searchTerm) {
        clearSearchBtn.classList.remove('hidden');
    } else {
        clearSearchBtn.classList.add('hidden');
    }

    // Aggiorna i risultati della ricerca
    updateSearchResults(searchTerm);
});

// Event listener per il pulsante di cancellazione della ricerca
clearSearchBtn.addEventListener('click', () => {
    // Cancella il campo di ricerca
    searchInput.value = '';
    clearSearchBtn.classList.add('hidden');
    searchResultsCount.classList.add('hidden');

    // Mostra tutte le tracce
    renderTrackList(allTracks);
});

// Event listener per la checkbox di inclusione del percorso nella ricerca
document.getElementById('search-include-path').addEventListener('change', () => {
    // Aggiorna i risultati della ricerca quando la checkbox viene modificata
    const searchTerm = searchInput.value.trim();
    if (searchTerm) {
        updateSearchResults(searchTerm);
    }
});

// Event listeners per i pulsanti di selezione
document.getElementById('select-all-btn').addEventListener('click', selectAllTracks);
document.getElementById('deselect-all-btn').addEventListener('click', deselectAllTracks);
document.getElementById('add-selected-to-playlist-btn').addEventListener('click', addSelectedTracksToPlaylist);

// Event listener per il pulsante "Open Database"
openDbBtn.addEventListener('click', async () => {
    try {
        console.log('Opening database...');
        const result = await window.database.open();

        if (result.success) {
            console.log('Database opened successfully at:', result.path);
        } else {
            console.error('Failed to open database:', result.error);
            alert(`Failed to open database: ${result.error}`);
        }
    } catch (error) {
        console.error('Error opening database:', error);
        alert(`Error opening database: ${error.message}`);
    }
});

// Playlist-related functions

// Function to load all playlists
async function loadPlaylists() {
    try {
        console.log('Loading playlists...');
        allPlaylists = await window.playlist.getAll();
        console.log(`Loaded ${allPlaylists.length} playlists:`, allPlaylists);

        // Log information about generated playlists
        const generatedPlaylists = allPlaylists.filter(playlist => playlist.is_generated);
        console.log(`Found ${generatedPlaylists.length} generated playlists`);

        if (generatedPlaylists.length > 0) {
            generatedPlaylists.forEach(playlist => {
                console.log(`Generated playlist: id=${playlist.id}, name=${playlist.name}, trackCount=${playlist.trackCount}`);
            });
        }

        renderPlaylists();

        // Assicuriamoci che gli event listener per il drag and drop vengano aggiunti
        // anche dopo il caricamento delle playlist
        setTimeout(() => {
            addDragAndDropListeners();
            console.log('Added drag and drop listeners after loading playlists');
        }, 100);
    } catch (error) {
        console.error('Failed to load playlists:', error);
        playlistsContainer.innerHTML = `
            <div class="empty-state error">
                Failed to load playlists: ${error.message}
            </div>
        `;
    }
}

// Function to render playlists
function renderPlaylists() {
    if (!allPlaylists || allPlaylists.length === 0) {
        playlistsContainer.innerHTML = `
            <div class="empty-state">
                No playlists yet. Create a new playlist to get started.
            </div>
        `;
        return;
    }

    // Dividiamo le playlist in due gruppi: manuali e generate
    const manualPlaylists = allPlaylists.filter(playlist => !playlist.is_generated);
    const generatedPlaylists = allPlaylists.filter(playlist => playlist.is_generated);

    // Funzione per creare l'HTML di una playlist
    const createPlaylistItem = (playlist) => {
        let createdDate = 'Unknown date';
        if (playlist.createdAt) {
            const date = new Date(playlist.createdAt);
            const day = date.getDate().toString().padStart(2, '0');
            const month = (date.getMonth() + 1).toString().padStart(2, '0');
            const year = date.getFullYear();
            createdDate = `${day}/${month}/${year}`;
        }
        const trackCount = playlist.trackCount || 0;
        const isGenerated = playlist.is_generated ? 'generated' : '';

        return `
            <div class="playlist-item ${isGenerated}" data-playlist-id="${playlist.id}" draggable="true">
                <div class="playlist-info">
                    <div class="playlist-name">${playlist.name}</div>
                    <div class="playlist-meta">
                        <span class="playlist-date">Created: ${createdDate}</span>
                    </div>
                </div>
                <div class="playlist-badge">${trackCount}</div>
            </div>
        `;
    };

    // Creiamo l'HTML per entrambi i gruppi
    const manualPlaylistsHtml = manualPlaylists.map(createPlaylistItem).join('');
    const generatedPlaylistsHtml = generatedPlaylists.length > 0
        ? `<div class="playlists-separator">Playlist Generate</div>${generatedPlaylists.map(createPlaylistItem).join('')}`
        : '';

    // Combiniamo i due gruppi
    const playlistItems = manualPlaylistsHtml + generatedPlaylistsHtml;

    playlistsContainer.innerHTML = playlistItems;

    // Add event listeners to playlist items
    document.querySelectorAll('.playlist-item').forEach(item => {
        item.addEventListener('click', () => {
            const playlistId = parseInt(item.dataset.playlistId);
            openPlaylist(playlistId);
        });

        // Add drag and drop event listeners
        item.addEventListener('dragover', handlePlaylistDragOver);
        item.addEventListener('dragleave', handlePlaylistDragLeave);
        item.addEventListener('drop', handlePlaylistDrop);
    });
}

// Function to open a playlist and show its tracks
async function openPlaylist(playlistId) {
    try {
        console.log(`Opening playlist with ID: ${playlistId}`);
        currentPlaylist = await window.playlist.get({ playlistId });
        console.log('Playlist data:', currentPlaylist);

        // Verifica che i dati della playlist siano stati recuperati correttamente
        if (!currentPlaylist) {
            console.error(`No playlist data returned for ID: ${playlistId}`);
            alert(`Failed to open playlist: No data returned`);
            return;
        }

        // Verifica che la playlist contenga tracce
        if (!currentPlaylist.tracks) {
            console.warn(`Playlist ${playlistId} has no tracks property`);
            currentPlaylist.tracks = [];
        }

        // Log dettagliato delle tracce
        console.log(`Playlist ${playlistId} has ${currentPlaylist.tracks.length} tracks:`, currentPlaylist.tracks);

        // For generated playlists, log cycle information
        if (currentPlaylist.is_generated) {
            console.log(`This is a generated playlist with is_generated=${currentPlaylist.is_generated}`);

            // Count tracks by cycle
            const tracksByCycle = {};
            currentPlaylist.tracks.forEach(track => {
                const cycle = track.cycle || 1;
                if (!tracksByCycle[cycle]) {
                    tracksByCycle[cycle] = [];
                }
                tracksByCycle[cycle].push(track);
            });

            // Log cycle counts
            console.log(`Tracks by cycle in currentPlaylist:`,
                Object.keys(tracksByCycle).map(cycle => ({
                    cycle,
                    count: tracksByCycle[cycle].length
                }))
            );

            // Check if repetitions property exists
            if (currentPlaylist.repetitions) {
                console.log(`Playlist has ${currentPlaylist.repetitions} repetitions defined`);
            } else {
                console.warn(`Playlist is generated but has no repetitions property`);
                // Calculate repetitions from the tracks
                const maxCycle = Math.max(...currentPlaylist.tracks.map(track => track.cycle || 1));
                console.log(`Calculated max cycle: ${maxCycle}`);
                currentPlaylist.repetitions = maxCycle;
            }
        }

        // Update the UI
        const trackCount = currentPlaylist.tracks.length;
        currentPlaylistName.textContent = `${currentPlaylist.name} (${trackCount})`;

        // Renderizza le tracce
        console.log('Rendering playlist tracks...');
        renderPlaylistTracks(currentPlaylist.tracks);
        console.log('Playlist tracks rendered');

        // Show the playlist tracks container (keep playlists container visible)
        playlistTracksContainer.classList.remove('hidden');

        // Aggiungi i listener per il drag and drop dopo aver renderizzato le tracce
        setTimeout(() => {
            addDragAndDropListeners();
            console.log('Added drag and drop listeners after opening playlist');
        }, 100);
    } catch (error) {
        console.error(`Failed to open playlist ${playlistId}:`, error);
        alert(`Failed to open playlist: ${error.message}`);
    }
}

// Function to render tracks in a playlist
function renderPlaylistTracks(tracks) {
    console.log('renderPlaylistTracks called with tracks:', tracks);

    if (!tracks || tracks.length === 0) {
        console.log('No tracks to render, showing empty state');
        playlistTracks.innerHTML = `
            <div class="empty-state">
                This playlist is empty. Add tracks from your library.
            </div>
        `;
        return;
    }

    console.log(`Rendering ${tracks.length} tracks in playlist`);

    // Verifica che playlistTracks esista
    if (!playlistTracks) {
        console.error('playlistTracks element not found');
        return;
    }

    // For generated playlists, group tracks by cycle
    if (currentPlaylist && currentPlaylist.is_generated) {
        // Group tracks by their cycle from the database
        const tracksBySourceAndPosition = {};
        let maxCycle = 0;

        // Sort tracks by position
        const sortedTracks = [...tracks].sort((a, b) => a.position - b.position);

        // Group tracks by their cycle number from the database
        console.log('Grouping tracks by cycle...');
        console.log('Raw tracks data:', JSON.stringify(tracks.slice(0, 5), null, 2)); // Log first 5 tracks for debugging

        // Log cycle information for debugging
        const cycleCounts = {};
        let hasCycleInfo = false;

        sortedTracks.forEach(track => {
            if (track.cycle !== undefined && track.cycle !== null) {
                hasCycleInfo = true;
            }
            const cycle = track.cycle || 1;
            cycleCounts[cycle] = (cycleCounts[cycle] || 0) + 1;
        });

        console.log('Has cycle information in tracks:', hasCycleInfo);
        console.log('Cycle counts in tracks array:', cycleCounts);

        // If we don't have cycle information, ensure all tracks have a cycle property
        if (!hasCycleInfo) {
            console.log('No cycle information found in tracks. Setting default cycle values...');

            // Set all tracks to cycle 1 if no cycle information is available
            sortedTracks.forEach(track => {
                track.cycle = 1;
            });

            // Update cycle counts
            cycleCounts[1] = sortedTracks.length;
            hasCycleInfo = true;

            console.log('Updated cycle counts after setting defaults:', cycleCounts);
        }

        // If we only have one cycle but the playlist should have more (based on repetitions property),
        // log a warning but continue with what we have
        if (Object.keys(cycleCounts).length <= 1 && currentPlaylist.repetitions > 1) {
            console.warn(`Cycle information may be incomplete. Found ${Object.keys(cycleCounts).length} cycle(s) but playlist should have ${currentPlaylist.repetitions} cycles!`);
        }

        sortedTracks.forEach((track, index) => {
            // Use the cycle from the database, or default to 1 if not present
            const cycleNumber = track.cycle || 1;
            console.log(`Track ${track.id} (${track.title || track.name}) is in cycle ${cycleNumber}`);

            // Keep track of the maximum cycle number
            if (cycleNumber > maxCycle) {
                maxCycle = cycleNumber;
            }

            // Initialize the array for this cycle if it doesn't exist
            if (!tracksBySourceAndPosition[cycleNumber]) {
                tracksBySourceAndPosition[cycleNumber] = [];
            }

            // Add the track to its cycle group
            tracksBySourceAndPosition[cycleNumber].push({
                track,
                index,
                sourceId: track.source_playlist_id
            });
        });

        console.log(`Found ${maxCycle} cycles in total`);
        for (let cycle = 1; cycle <= maxCycle; cycle++) {
            const tracksInCycle = tracksBySourceAndPosition[cycle] || [];
            console.log(`Cycle ${cycle} has ${tracksInCycle.length} tracks`);
        }

        // If we only have one cycle but should have more, log a warning
        if (maxCycle === 1 && currentPlaylist.repetitions && currentPlaylist.repetitions > 1) {
            console.warn(`WARNING: Only found 1 cycle but playlist should have ${currentPlaylist.repetitions} cycles!`);
        }

        // Sort tracks within each cycle by their original position
        for (const cycle in tracksBySourceAndPosition) {
            tracksBySourceAndPosition[cycle].sort((a, b) => a.track.position - b.track.position);
        }

        // Create HTML for each cycle
        let html = '';

        for (let cycle = 1; cycle <= maxCycle; cycle++) {
            const cycleTracksData = tracksBySourceAndPosition[cycle] || [];

            if (cycleTracksData.length === 0) continue;

            // Create subset header with up/down arrow buttons
            // First cycle should not have an up arrow, last cycle should not have a down arrow
            const isFirstCycle = cycle === 1;
            const isLastCycle = cycle === maxCycle;

            html += `
                <div class="track-subset" data-cycle="${cycle}" draggable="true">
                    <div class="track-subset-header" data-cycle="${cycle}">
                        <div class="track-subset-title">
                            <span class="cycle-number">${cycle}</span>
                            <span>Ciclo ${cycle}</span>
                        </div>
                        <div class="cycle-controls">
                            ${!isFirstCycle ? `
                            <button class="cycle-button cycle-up-button" data-cycle="${cycle}" title="Move cycle up">
                                <svg class="icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <polyline points="18 15 12 9 6 15"></polyline>
                                </svg>
                            </button>
                            ` : ''}
                            ${!isLastCycle ? `
                            <button class="cycle-button cycle-down-button" data-cycle="${cycle}" title="Move cycle down">
                                <svg class="icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <polyline points="6 9 12 15 18 9"></polyline>
                                </svg>
                            </button>
                            ` : ''}
                        </div>
                    </div>
                    <div class="track-subset-tracks">
            `;

            // Create tracks for this cycle
            const cycleTrackItems = cycleTracksData.map((trackData) => {
                const { track, index } = trackData;
                const title = track.title || track.name;
                const artist = track.artist || '';
                const showArtist = artist && artist !== 'Unknown Artist';
                let durationText = '';
                const trackNumber = index + 1; // Numero progressivo
                const path = track.path || '';
                const tags = track.tags || '';

                // Format duration
                if (track.duration) {
                    const minutes = Math.floor(track.duration / 60);
                    const seconds = Math.floor(track.duration % 60);
                    durationText = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                }

                // Check if track is new (created within the last 3 months)
                let isNew = false;
                let newBadgeHtml = '';
                if (track.updatedAt) {
                    const updatedDate = new Date(track.updatedAt);
                    const currentDate = new Date();
                    const threeMonthsAgo = new Date();
                    threeMonthsAgo.setMonth(currentDate.getMonth() - 3);

                    // if (updatedDate > threeMonthsAgo) {
                    //     isNew = true;
                    //     newBadgeHtml = `<span class="new-badge">new</span>`;
                    // }
                }

                // Prepare source playlist info if available
                let sourcePlaylistHtml = '';
                if (track.source_playlist_id && track.source_playlist_name) {
                    sourcePlaylistHtml = `
                        <div class="playlist-track-source">
                            <span class="source-label">Da:</span>
                            <span class="source-playlist-name" data-playlist-id="${track.source_playlist_id}">${track.source_playlist_name}</span>
                        </div>
                    `;
                }

                // Format tags as badges
                let tagsHtml = '';
                if (tags) {
                    const tagsList = tags.split(',').map(tag => tag.trim()).filter(tag => tag);
                    if (tagsList.length > 0) {
                        tagsHtml = `
                            <div class="playlist-track-tags">
                                <span class="tags-label">Tags:</span>
                                <div class="tags-container">
                                    ${tagsList.map(tag => `<span class="playlist-track-tag">${tag}</span>`).join('')}
                                </div>
                            </div>
                        `;
                    }
                }

                // Format file path
                let pathHtml = '';
                if (path) {
                    pathHtml = `
                        <div class="playlist-track-path" title="${path}">
                            <span class="path-label">Path:</span>
                            <span class="path-value">${path}</span>
                        </div>
                    `;
                }

                // Determine if we should show the replace button (for all generated playlists)
                const showReplaceButton = currentPlaylist && currentPlaylist.is_generated;

                // Determine if this track is the first or last in its cycle
                const tracksInThisCycle = tracksBySourceAndPosition[cycle] || [];
                const isFirstInCycle = tracksInThisCycle.findIndex(t => t.track.id === track.id) === 0;
                const isLastInCycle = tracksInThisCycle.findIndex(t => t.track.id === track.id) === tracksInThisCycle.length - 1;

                return `
                    <div class="playlist-track-item" data-track-id="${track.id}" data-path="${track.path}" data-title="${title}" data-artist="${artist}" data-source-playlist-id="${track.source_playlist_id || ''}" data-position="${trackNumber}" data-cycle="${cycle}" draggable="true">
                        <div class="track-number">${trackNumber}</div>
                        <div class="playlist-track-info">
                            <div class="playlist-track-main-info">
                                <div class="playlist-track-title">${title}</div>
                                ${showArtist ? `<div class="playlist-track-artist">${artist}</div>` : ''}
                                ${durationText ? `<span class="playlist-track-duration">${durationText} ${isNew ? newBadgeHtml : ''}</span>` : `${isNew ? newBadgeHtml : ''}`}
                            </div>
                            ${sourcePlaylistHtml}
                            ${tagsHtml}
                            ${pathHtml}
                        </div>
                        <div class="playlist-track-actions">
                            <button class="track-button play-button" data-path="${track.path}">
                                <svg class="icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <polygon points="5 3 19 12 5 21 5 3"></polygon>
                                </svg>
                            </button>
                            ${showReplaceButton ? `
                            <button class="track-button replace-track-button" data-track-id="${track.id}" data-source-playlist-id="${track.source_playlist_id || ''}" title="Replace with another track">
                                <svg class="icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M1 4v6h6"></path>
                                    <path d="M3.51 15a9 9 0 1 0 2.13-9.36L1 10"></path>
                                </svg>
                            </button>
                            ` : ''}
                            ${!isFirstInCycle ? `
                            <button class="track-button track-up-button" data-track-id="${track.id}" data-cycle="${cycle}" title="Move track up">
                                <svg class="icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <polyline points="18 15 12 9 6 15"></polyline>
                                </svg>
                            </button>
                            ` : ''}
                            ${!isLastInCycle ? `
                            <button class="track-button track-down-button" data-track-id="${track.id}" data-cycle="${cycle}" title="Move track down">
                                <svg class="icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <polyline points="6 9 12 15 18 9"></polyline>
                                </svg>
                            </button>
                            ` : ''}
                            <button class="track-button remove-from-playlist-button" data-track-id="${track.id}">
                                <svg class="icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <line x1="18" y1="6" x2="6" y2="18"></line>
                                    <line x1="6" y1="6" x2="18" y2="18"></line>
                                </svg>
                            </button>
                        </div>
                    </div>
                `;
            }).join('');

            html += cycleTrackItems;
            html += `
                    </div>
                </div>
            `;
        }

        playlistTracks.innerHTML = html;
    } else {
        // For regular playlists, render tracks normally
        const trackItems = tracks.map((track, index) => {
            const title = track.title || track.name;
            const artist = track.artist || '';
            const showArtist = artist && artist !== 'Unknown Artist';
            let durationText = '';
            const trackNumber = index + 1; // Numero progressivo
            const path = track.path || '';
            const tags = track.tags || '';

            // Format duration
            if (track.duration) {
                const minutes = Math.floor(track.duration / 60);
                const seconds = Math.floor(track.duration % 60);
                durationText = `${minutes}:${seconds.toString().padStart(2, '0')}`;
            }

            // Check if track is new (created within the last 3 months)
            let isNew = false;
            let newBadgeHtml = '';
            if (track.updatedAt) {
                const updatedDate = new Date(track.updatedAt);
                const currentDate = new Date();
                const threeMonthsAgo = new Date();
                threeMonthsAgo.setMonth(currentDate.getMonth() - 3);

                // if (updatedDate > threeMonthsAgo) {
                //     isNew = true;
                //     newBadgeHtml = `<span class="new-badge">new</span>`;
                // }
            }

            // Prepare source playlist info if available
            let sourcePlaylistHtml = '';
            if (currentPlaylist && currentPlaylist.is_generated && track.source_playlist_id && track.source_playlist_name) {
                sourcePlaylistHtml = `
                    <div class="playlist-track-source">
                        <span class="source-label">Da:</span>
                        <span class="source-playlist-name" data-playlist-id="${track.source_playlist_id}">${track.source_playlist_name}</span>
                    </div>
                `;
            }

            // Format tags as badges
            let tagsHtml = '';
            if (tags) {
                const tagsList = tags.split(',').map(tag => tag.trim()).filter(tag => tag);
                if (tagsList.length > 0) {
                    tagsHtml = `
                        <div class="playlist-track-tags">
                            <span class="tags-label">Tags:</span>
                            <div class="tags-container">
                                ${tagsList.map(tag => `<span class="playlist-track-tag">${tag}</span>`).join('')}
                            </div>
                        </div>
                    `;
                }
            }

            // Format file path
            let pathHtml = '';
            if (path) {
                pathHtml = `
                    <div class="playlist-track-path" title="${path}">
                        <span class="path-label">Path:</span>
                        <span class="path-value">${path}</span>
                    </div>
                `;
            }

            // Determine if we should show the replace button (for all generated playlists)
            const showReplaceButton = currentPlaylist && currentPlaylist.is_generated;

            // Determine if this track is the first or last in the playlist
            const isFirstTrack = index === 0;
            const isLastTrack = index === tracks.length - 1;

            return `
                <div class="playlist-track-item ${selectedTracks.includes(track.id) ? 'selected' : ''}" data-track-id="${track.id}" data-path="${track.path}" data-title="${title}" data-artist="${artist}" data-source-playlist-id="${track.source_playlist_id || ''}" data-position="${trackNumber}" draggable="true">
                    <div class="track-select">
                        <input type="checkbox" class="track-checkbox" ${selectedTracks.includes(track.id) ? 'checked' : ''}>
                    </div>
                    <div class="track-number">${trackNumber}</div>
                    <div class="playlist-track-info">
                        <div class="playlist-track-main-info">
                            <div class="playlist-track-title">${title}</div>
                            ${showArtist ? `<div class="playlist-track-artist">${artist}</div>` : ''}
                            ${durationText ? `<span class="playlist-track-duration">${durationText} ${isNew ? newBadgeHtml : ''}</span>` : `${isNew ? newBadgeHtml : ''}`}
                        </div>
                        ${sourcePlaylistHtml}
                        ${tagsHtml}
                        ${pathHtml}
                    </div>
                    <div class="playlist-track-actions">
                        <button class="track-button play-button" data-path="${track.path}">
                            <svg class="icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polygon points="5 3 19 12 5 21 5 3"></polygon>
                            </svg>
                        </button>
                        ${showReplaceButton ? `
                        <button class="track-button replace-track-button" data-track-id="${track.id}" data-source-playlist-id="${track.source_playlist_id || ''}" title="Replace with another track">
                            <svg class="icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M1 4v6h6"></path>
                                <path d="M3.51 15a9 9 0 1 0 2.13-9.36L1 10"></path>
                            </svg>
                        </button>
                        ` : ''}
                        ${!isFirstTrack ? `
                        <button class="track-button track-up-button" data-track-id="${track.id}" title="Move track up">
                            <svg class="icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="18 15 12 9 6 15"></polyline>
                            </svg>
                        </button>
                        ` : ''}
                        ${!isLastTrack ? `
                        <button class="track-button track-down-button" data-track-id="${track.id}" title="Move track down">
                            <svg class="icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="6 9 12 15 18 9"></polyline>
                            </svg>
                        </button>
                        ` : ''}
                        <button class="track-button remove-from-playlist-button" data-track-id="${track.id}">
                            <svg class="icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <line x1="18" y1="6" x2="6" y2="18"></line>
                                <line x1="6" y1="6" x2="18" y2="18"></line>
                            </svg>
                        </button>
                    </div>
                </div>
            `;
        }).join('');

        playlistTracks.innerHTML = trackItems;
    }

    // Add event listeners to track buttons
    playlistTracks.querySelectorAll('.play-button').forEach(button => {
        button.addEventListener('click', (event) => {
            event.stopPropagation();
            const path = button.dataset.path;
            if (path) {
                window.playTrack(path);
            }
        });
    });

    playlistTracks.querySelectorAll('.remove-from-playlist-button').forEach(button => {
        button.addEventListener('click', async (event) => {
            event.stopPropagation();
            if (!currentPlaylist) return;

            const trackId = parseInt(button.dataset.trackId);
            if (isNaN(trackId)) return;

            try {
                await window.playlist.removeTracks({
                    playlistId: currentPlaylist.id,
                    trackIds: [trackId]
                });

                // Refresh the playlist
                openPlaylist(currentPlaylist.id);
            } catch (error) {
                console.error('Failed to remove track from playlist:', error);
                alert(`Failed to remove track: ${error.message}`);
            }
        });
    });

    // Add event listeners to replace buttons
    playlistTracks.querySelectorAll('.replace-track-button').forEach(button => {
        button.addEventListener('click', async (event) => {
            event.stopPropagation();
            if (!currentPlaylist) return;

            const trackId = parseInt(button.dataset.trackId);
            const sourcePlaylistId = parseInt(button.dataset.sourcePlaylistId);

            if (isNaN(trackId)) return;

            // If this is a generated playlist with a source playlist, use the standard replacement
            if (!isNaN(sourcePlaylistId)) {
                try {
                    // Show loading state
                    button.disabled = true;
                    button.innerHTML = `
                        <svg class="icon spin" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <line x1="12" y1="2" x2="12" y2="6"></line>
                            <line x1="12" y1="18" x2="12" y2="22"></line>
                            <line x1="4.93" y1="4.93" x2="7.76" y2="7.76"></line>
                            <line x1="16.24" y1="16.24" x2="19.07" y2="19.07"></line>
                            <line x1="2" y1="12" x2="6" y2="12"></line>
                            <line x1="18" y1="12" x2="22" y2="12"></line>
                            <line x1="4.93" y1="19.07" x2="7.76" y2="16.24"></line>
                            <line x1="16.24" y1="7.76" x2="19.07" y2="4.93"></line>
                        </svg>
                    `;

                    // Get a random replacement track from the same source playlist
                    await window.playlist.replaceTrack({
                        playlistId: currentPlaylist.id,
                        trackId: trackId,
                        sourcePlaylistId: sourcePlaylistId
                    });

                    // Refresh the playlist view
                    await openPlaylist(currentPlaylist.id);
                } catch (error) {
                    console.error('Failed to replace track:', error);
                    alert(`Failed to replace track: ${error.message}`);

                    // Reset button state
                    button.disabled = false;
                    button.innerHTML = `
                        <svg class="icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M1 4v6h6"></path>
                            <path d="M3.51 15a9 9 0 1 0 2.13-9.36L1 10"></path>
                        </svg>
                    `;
                }
            } else {
                // For tag-based replacement, open the replacement options popup
                openTrackReplacementPopup(trackId);
            }
        });
    });

    // Add event listeners to source playlist names
    playlistTracks.querySelectorAll('.source-playlist-name').forEach(element => {
        element.addEventListener('click', (event) => {
            event.stopPropagation();
            const playlistId = parseInt(element.dataset.playlistId);
            if (!isNaN(playlistId)) {
                openPlaylist(playlistId);
            }
        });
    });

    // Add drag and drop listeners to playlist tracks
    addDragAndDropListeners();
}

// Function to create a new playlist
async function createNewPlaylist(name) {
    try {
        console.log(`Creating new playlist: ${name}`);
        const newPlaylist = await window.playlist.create({ name });
        console.log('New playlist created:', newPlaylist);

        // Refresh the playlists
        await loadPlaylists();

        return newPlaylist;
    } catch (error) {
        console.error('Failed to create playlist:', error);
        alert(`Failed to create playlist: ${error.message}`);
        return null;
    }
}

// Function to add tracks to a playlist
async function addTracksToPlaylist(playlistId, trackIds) {
    try {
        console.log(`Adding tracks to playlist ${playlistId}:`, trackIds);
        const result = await window.playlist.addTracks({ playlistId, trackIds });
        console.log('Tracks added to playlist:', result);

        // If no tracks were added (count is 0), it means they were already in the playlist
        // The ON CONFLICT DO NOTHING in the SQL prevents duplicates
        if (result.success && result.count === 0) {
            console.log('No tracks added - they were already in the playlist');
        } else if (result.success && result.count > 0) {
            // Update the playlist count in the allPlaylists array
            const playlistIndex = allPlaylists.findIndex(p => p.id === playlistId);
            if (playlistIndex !== -1) {
                // Increment the track count
                allPlaylists[playlistIndex].trackCount = (allPlaylists[playlistIndex].trackCount || 0) + result.count;

                // Update the UI to reflect the new count
                const playlistElement = document.querySelector(`.playlist-item[data-playlist-id="${playlistId}"] .playlist-badge`);
                if (playlistElement) {
                    playlistElement.textContent = allPlaylists[playlistIndex].trackCount;
                }

                console.log(`Updated playlist ${playlistId} track count to ${allPlaylists[playlistIndex].trackCount}`);

                // Load tags after adding tracks to ensure they're displayed
                console.log('Loading tags after adding tracks to playlist...');
                await loadAllTags();
                console.log('Tags loaded and displayed after adding tracks to playlist');
            }
        }

        return result;
    } catch (error) {
        console.error('Failed to add tracks to playlist:', error);
        alert(`Failed to add tracks to playlist: ${error.message}`);
        return null;
    }
}

// Function to delete a playlist
async function deletePlaylist(playlistId) {
    try {
        console.log(`Deleting playlist ${playlistId}`);
        const result = await window.playlist.delete({ playlistId });
        console.log('Playlist deleted:', result);

        // Refresh the playlists
        await loadPlaylists();

        // Hide the playlist tracks container
        playlistTracksContainer.classList.add('hidden');

        currentPlaylist = null;

        return result;
    } catch (error) {
        console.error('Failed to delete playlist:', error);
        alert(`Failed to delete playlist: ${error.message}`);
        return null;
    }
}

// Function to open the create playlist popup
function openCreatePlaylistPopup() {
    createPlaylistForm.reset();
    createPlaylistPopup.classList.remove('hidden');
    createPlaylistPopup.classList.remove('fully-hidden');
    playlistNameInput.focus();
}

// Function to close the create playlist popup
function closeCreatePlaylistPopup() {
    createPlaylistPopup.classList.add('hidden');
    setTimeout(() => {
        createPlaylistPopup.classList.add('fully-hidden');
    }, 300);
}

// Function to open the add to playlist popup
function openAddToPlaylistPopup(trackIds) {
    selectedTracksForPlaylist = trackIds;

    // Populate the playlist options
    renderPlaylistOptions();

    addToPlaylistPopup.classList.remove('hidden');
    addToPlaylistPopup.classList.remove('fully-hidden');
}

// Function to close the add to playlist popup
function closeAddToPlaylistPopup() {
    addToPlaylistPopup.classList.add('hidden');
    setTimeout(() => {
        addToPlaylistPopup.classList.add('fully-hidden');
    }, 300);

    selectedTracksForPlaylist = [];
}

// Function to render playlist options in the add to playlist popup
function renderPlaylistOptions() {
    if (!allPlaylists || allPlaylists.length === 0) {
        playlistOptions.innerHTML = `
            <div class="empty-state">
                No playlists available. Create a new playlist first.
            </div>
        `;
        return;
    }

    const options = allPlaylists.map(playlist => {
        return `
            <div class="playlist-option" data-playlist-id="${playlist.id}">
                <span class="playlist-option-name">${playlist.name}</span>
            </div>
        `;
    }).join('');

    playlistOptions.innerHTML = options;

    // Add event listeners to playlist options
    playlistOptions.querySelectorAll('.playlist-option').forEach(option => {
        option.addEventListener('click', async () => {
            const playlistId = parseInt(option.dataset.playlistId);
            if (isNaN(playlistId) || selectedTracksForPlaylist.length === 0) return;

            // Add the selected tracks to the playlist
            const result = await addTracksToPlaylist(playlistId, selectedTracksForPlaylist);
            if (result && result.success) {
                closeAddToPlaylistPopup();

                if (result.count > 0) {
                    alert(`Added ${result.count} tracks to the playlist.`);
                } else {
                    // All tracks were already in the playlist
                    alert(`No tracks added. All selected tracks are already in the playlist.`);
                }
            }
        });
    });
}

// Function to update the track list to include "Add to Playlist" buttons
function updateTrackListWithPlaylistButtons() {
    // Add "Add to Playlist" button to each track
    document.querySelectorAll('.track-actions').forEach(actionsDiv => {
        // Check if the button already exists
        if (actionsDiv.querySelector('.add-to-playlist-button')) return;

        const trackItem = actionsDiv.closest('.track-item');
        if (!trackItem) return;

        const trackId = trackItem.querySelector('.play-button').dataset.trackId;
        if (!trackId) return;

        // Create the "Add to Playlist" button
        const addToPlaylistButton = document.createElement('button');
        addToPlaylistButton.className = 'track-button add-to-playlist-button';
        addToPlaylistButton.dataset.trackId = trackId;
        addToPlaylistButton.innerHTML = `
            <svg class="icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <line x1="12" y1="5" x2="12" y2="19"></line>
                <line x1="5" y1="12" x2="19" y2="12"></line>
            </svg>
        `;

        // Add event listener to the button
        addToPlaylistButton.addEventListener('click', (event) => {
            event.stopPropagation();
            openAddToPlaylistPopup([parseInt(trackId)]);
        });

        // Add the button to the actions div
        actionsDiv.appendChild(addToPlaylistButton);
    });
}

// Function to scan directory and add tracks to current playlist
async function scanAndAddToPlaylist(playlistId) {
    console.log(`scanAndAddToPlaylist called with playlistId: ${playlistId}`);

    if (!playlistId) {
        console.error('No playlistId provided');
        return;
    }

    // First, select a folder to scan using the combined function
    console.log('Selecting folder to scan...');
    const folderResult = await window.library.selectAndScanFolder();
    console.log('Select and scan result:', folderResult);

    if (!folderResult || !folderResult.success || folderResult.canceled) {
        console.log('Folder selection canceled or failed');
        return;
    }

    // Extract the actual path from the result object
    const folderPath = folderResult.path;
    console.log('Selected folder path:', folderPath);

    if (!folderPath) {
        console.error('No folder path in result');
        return;
    }

    // Update UI to show scanning is in progress
    const addTracksBtn = document.getElementById('add-tracks-to-playlist-btn');
    const originalBtnContent = addTracksBtn.innerHTML;

    addTracksBtn.disabled = true;
    addTracksBtn.innerHTML = `
        <svg class="icon spin" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <line x1="12" y1="2" x2="12" y2="6"></line>
            <line x1="12" y1="18" x2="12" y2="22"></line>
            <line x1="4.93" y1="4.93" x2="7.76" y2="7.76"></line>
            <line x1="16.24" y1="16.24" x2="19.07" y2="19.07"></line>
            <line x1="2" y1="12" x2="6" y2="12"></line>
            <line x1="18" y1="12" x2="22" y2="12"></line>
            <line x1="4.93" y1="19.07" x2="7.76" y2="16.24"></line>
            <line x1="16.24" y1="7.76" x2="19.07" y2="4.93"></line>
        </svg>
        Scanning...
    `;

    playlistTracks.innerHTML = `<div class="empty-state">Scanning directory for tracks to add to playlist...</div>`;

    try {
        // Update status bar with selected folder
        const folderName = folderPath.split('\\').pop() || folderPath.split('/').pop() || folderPath;
        statusFolderName.textContent = folderName;
        statusFolderName.title = folderPath;
        folderStatus.classList.remove('hidden');

        // Since we already scanned the directory with selectAndScanFolder, we can use the result
        console.log('Using tracks from the select and scan operation');

        // Get the newly added track IDs - these are all tracks in the scanned directory
        // We'll filter them later to only add tracks that aren't already in the playlist
        const allScannedTracks = folderResult.files || [];
        const newTrackIds = allScannedTracks.map(track => track.id).filter(id => id);
        console.log(`Found ${newTrackIds.length} tracks:`, newTrackIds);

        if (newTrackIds.length > 0) {
            console.log(`Adding ${newTrackIds.length} tracks to playlist ${playlistId}`);
            // Add only the newly added tracks to the current playlist
            const addResult = await addTracksToPlaylist(playlistId, newTrackIds);
            console.log('Add tracks to playlist result:', addResult);

            if (addResult && addResult.success) {
                if (addResult.count > 0) {
                    console.log(`Successfully added ${addResult.count} tracks to playlist ${playlistId}`);
                    // Refresh the playlist view only if tracks were actually added
                    await openPlaylist(playlistId);
                    alert(`Aggiunte ${addResult.count} nuove tracce alla playlist.`);
                } else {
                    console.log('No tracks added - they were already in the playlist');
                    alert('Nessuna nuova traccia è stata aggiunta alla playlist. Tutte le tracce erano già presenti.');
                }
            } else {
                console.error('Failed to add tracks to playlist:', addResult);
                alert('Nessuna nuova traccia è stata aggiunta alla playlist.');
            }
        } else {
            console.log('No new tracks found in the selected directory');
            alert('Nessuna nuova traccia trovata nella cartella selezionata. Potrebbero essere già presenti nel database.');
        }

        // Update stats using the folderResult which already contains the stats
        updateStats(folderResult);

        // Load and display tags immediately after scan
        console.log('Loading tags after adding tracks to playlist...');
        await loadAllTags();
        console.log('Tags loaded and displayed after playlist update');

    } catch (error) {
        console.error('Scan and add to playlist failed:', error);
        playlistTracks.innerHTML = `<div class="empty-state error">Failed to scan and add tracks: ${error.message}</div>`;
        alert(`Errore durante la scansione: ${error.message}`);
    } finally {
        // Restore button state
        addTracksBtn.disabled = false;
        addTracksBtn.innerHTML = originalBtnContent;

        // If the playlist is empty, show appropriate message
        if (!currentPlaylist || !currentPlaylist.tracks || currentPlaylist.tracks.length === 0) {
            playlistTracks.innerHTML = `
                <div class="empty-state">
                    This playlist is empty. Add tracks from your library.
                </div>
            `;
        }
    }
}

// Event listeners for playlist-related buttons
createPlaylistBtn.addEventListener('click', openCreatePlaylistPopup);
closePlaylistPopupBtn.addEventListener('click', closeCreatePlaylistPopup);
cancelPlaylistBtn.addEventListener('click', closeCreatePlaylistPopup);

createPlaylistForm.addEventListener('submit', async (event) => {
    event.preventDefault();
    const name = playlistNameInput.value.trim();
    if (!name) return;

    const newPlaylist = await createNewPlaylist(name);
    if (newPlaylist) {
        closeCreatePlaylistPopup();

        // If there are selected tracks, add them to the new playlist
        if (selectedTracksForPlaylist.length > 0) {
            await addTracksToPlaylist(newPlaylist.id, selectedTracksForPlaylist);
            closeAddToPlaylistPopup();
        }
    }
});

closeAddToPlaylistBtn.addEventListener('click', closeAddToPlaylistPopup);
cancelAddToPlaylistBtn.addEventListener('click', closeAddToPlaylistPopup);
createNewPlaylistBtn.addEventListener('click', () => {
    closeAddToPlaylistPopup();
    openCreatePlaylistPopup();
});

backToPlaylistsBtn.addEventListener('click', () => {
    playlistTracksContainer.classList.add('hidden');
    currentPlaylist = null;
});

// Add event listener for the "Add Tracks to Playlist" button
document.getElementById('add-tracks-to-playlist-btn').addEventListener('click', () => {
    if (!currentPlaylist) return;
    scanAndAddToPlaylist(currentPlaylist.id);
});

deletePlaylistBtn.addEventListener('click', async () => {
    if (!currentPlaylist) return;

    const confirmation = confirm(`Are you sure you want to delete the playlist "${currentPlaylist.name}"? This action cannot be undone.`);
    if (confirmation) {
        await deletePlaylist(currentPlaylist.id);
    }
});

// Event listener for export playlist as .m3u8 button
exportPlaylistM3u8Btn.addEventListener('click', async () => {
    if (!currentPlaylist) return;

    await exportPlaylistAsM3u8(currentPlaylist.id, currentPlaylist.name);
});

// Event listener for swap tracks button
swapTracksBtn.addEventListener('click', () => {
    if (!currentPlaylist) return;
    openSwapTracksPopup();
});

// Event listener for rename playlist button
renamePlaylistBtn.addEventListener('click', () => {
    if (!currentPlaylist) return;
    openRenamePlaylistPopup();
});

// Drag and drop functionality

// Variables to store drag state
let draggedItem = null;
let draggedItemType = null; // 'track' or 'playlist-track'

// Function to handle drag start for tracks
function handleDragStart(e) {
    draggedItem = this;
    draggedItemType = this.classList.contains('track-item') ? 'track' : 'playlist-track';

    // Check if we have selected tracks
    if (selectedTracks.length > 0 && selectedTracks.includes(parseInt(this.dataset.trackId))) {
        // We're dragging a selected track, so we'll transfer all selected tracks
        const tracksData = selectedTracks.map(trackId => {
            // Find the track element
            const trackElement = document.querySelector(`.track-item[data-track-id="${trackId}"], .playlist-track-item[data-track-id="${trackId}"]`);
            if (!trackElement) return null;

            return {
                id: trackId,
                path: trackElement.dataset.path,
                title: trackElement.dataset.title,
                artist: trackElement.dataset.artist
            };
        }).filter(track => track !== null); // Remove any null entries

        // Prepara i dati da trasferire
        const transferData = {
            tracks: tracksData,
            type: 'multiple-tracks'
        };

        console.log('Drag start with multiple tracks:', transferData);

        // Imposta i dati nel dataTransfer
        e.dataTransfer.setData('text/plain', JSON.stringify(transferData));

        // Add visual feedback to all selected tracks
        selectedTracks.forEach(trackId => {
            const trackElement = document.querySelector(`.track-item[data-track-id="${trackId}"], .playlist-track-item[data-track-id="${trackId}"]`);
            if (trackElement) {
                setTimeout(() => {
                    trackElement.classList.add('dragging');
                }, 0);
            }
        });
    } else {
        // Regular single track drag
        // Set the data to be transferred
        const trackId = this.dataset.trackId;
        const trackPath = this.dataset.path;
        const trackTitle = this.dataset.title;
        const trackArtist = this.dataset.artist;

        // Prepara i dati da trasferire
        const transferData = {
            id: trackId,
            path: trackPath,
            title: trackTitle,
            artist: trackArtist,
            type: draggedItemType
        };

        console.log('Drag start with data:', transferData);

        // Imposta i dati nel dataTransfer
        e.dataTransfer.setData('text/plain', JSON.stringify(transferData));

        // Add dragging class for visual feedback
        setTimeout(() => {
            this.classList.add('dragging');
        }, 0);
    }

    // Imposta l'effetto di copia
    e.dataTransfer.effectAllowed = 'copyMove';
}

// Function to handle drag start for playlists
function handlePlaylistDragStart(e) {
    draggedItem = this;
    draggedItemType = 'playlist';

    // Set the data to be transferred
    const playlistId = this.dataset.playlistId;

    // Prepara i dati da trasferire
    const transferData = {
        id: playlistId,
        type: 'playlist'
    };

    console.log('Playlist drag start with data:', transferData);

    // Imposta i dati nel dataTransfer
    e.dataTransfer.setData('text/plain', JSON.stringify(transferData));

    // Imposta l'effetto di copia
    e.dataTransfer.effectAllowed = 'move';

    // Add dragging class for visual feedback
    setTimeout(() => {
        this.classList.add('dragging');
    }, 0);
}

// Function to handle drag end for tracks
function handleDragEnd() {
    // If we have selected tracks, remove dragging class from all of them
    if (selectedTracks.length > 0 && selectedTracks.includes(parseInt(this.dataset.trackId))) {
        selectedTracks.forEach(trackId => {
            const trackElement = document.querySelector(`.track-item[data-track-id="${trackId}"], .playlist-track-item[data-track-id="${trackId}"]`);
            if (trackElement) {
                trackElement.classList.remove('dragging');
            }
        });
    } else {
        // Just remove from this track
        this.classList.remove('dragging');
    }

    draggedItem = null;
    draggedItemType = null;

    // Remove all drag-over classes
    document.querySelectorAll('.drag-over').forEach(item => {
        item.classList.remove('drag-over');
    });
}

// Function to handle drag end for playlists
function handlePlaylistDragEnd() {
    this.classList.remove('dragging');
    draggedItem = null;
    draggedItemType = null;

    // Remove all drag-over classes
    document.querySelectorAll('.drag-over').forEach(item => {
        item.classList.remove('drag-over');
    });
}

// Function to handle drag over a playlist
function handlePlaylistDragOver(e) {
    e.preventDefault();
    // Sempre accetta il drag over per qualsiasi tipo di traccia
    if (draggedItemType === 'track' || draggedItemType === 'playlist-track') {
        this.classList.add('drag-over');
    }

    // Assicuriamoci che il dataTransfer sia impostato correttamente
    e.dataTransfer.dropEffect = 'copy';
}

// Function to handle drag leave from a playlist
function handlePlaylistDragLeave() {
    this.classList.remove('drag-over');
}

// Function to handle drop on a playlist
function handlePlaylistDrop(e) {
    e.preventDefault();
    this.classList.remove('drag-over');

    try {
        // Log dell'evento drop per diagnostica
        console.log('Drop event on playlist:', e);

        // Get the data transfer content, check if it's empty
        const dataTransfer = e.dataTransfer.getData('text/plain');
        if (!dataTransfer) {
            console.log('No data received in drop event');

            // Se non ci sono dati ma abbiamo un elemento trascinato, proviamo a usare quello
            if (draggedItem && draggedItem.dataset.trackId) {
                const trackId = parseInt(draggedItem.dataset.trackId);
                const playlistId = parseInt(this.dataset.playlistId);

                console.log('Using draggedItem as fallback:', { trackId, playlistId });

                if (trackId && playlistId) {
                    // Add the track to the playlist
                    addTracksToPlaylist(playlistId, [trackId]).then(result => {
                        if (result && result.success) {
                            if (result.count > 0) {
                                // Show success message only if tracks were actually added
                                alert(`Traccia aggiunta alla playlist con successo.`);
                            } else {
                                // Track was already in the playlist
                                console.log('Track already exists in playlist');
                            }
                        }
                    });
                }
            }
            return;
        }

        console.log('Data received in drop event:', dataTransfer);
        const data = JSON.parse(dataTransfer);
        const playlistId = parseInt(this.dataset.playlistId);

        console.log('Parsed drop data:', { data, playlistId });

        if (data.type === 'multiple-tracks' && data.tracks && data.tracks.length > 0) {
            // Handle multiple tracks
            const trackIds = data.tracks.map(track => parseInt(track.id)).filter(id => !isNaN(id));
            console.log('Adding multiple tracks to playlist:', { trackIds, playlistId });

            // Add the tracks to the playlist
            addTracksToPlaylist(playlistId, trackIds).then(result => {
                if (result && result.success) {
                    if (result.count > 0) {
                        // Show success message only if tracks were actually added
                        alert(`${result.count} tracce aggiunte alla playlist con successo.`);
                    } else {
                        // Tracks were already in the playlist
                        console.log('All tracks already exist in playlist');
                    }
                }
            });
        } else if (data.id && playlistId) {
            // Handle single track
            const trackId = parseInt(data.id);
            console.log('Adding track to playlist:', { trackId, playlistId });

            // Add the track to the playlist
            addTracksToPlaylist(playlistId, [trackId]).then(result => {
                if (result && result.success) {
                    if (result.count > 0) {
                        // Show success message only if tracks were actually added
                        alert(`Traccia aggiunta alla playlist con successo.`);
                    } else {
                        // Track was already in the playlist
                        console.log('Track already exists in playlist');
                    }
                }
            });
        }
    } catch (error) {
        console.error('Error handling drop:', error);

        // In caso di errore, prova a usare l'elemento trascinato come fallback
        if (draggedItem && draggedItem.dataset.trackId) {
            try {
                const trackId = parseInt(draggedItem.dataset.trackId);
                const playlistId = parseInt(this.dataset.playlistId);

                console.log('Using draggedItem as fallback after error:', { trackId, playlistId });

                if (trackId && playlistId) {
                    // Add the track to the playlist
                    addTracksToPlaylist(playlistId, [trackId]).then(result => {
                        if (result && result.success) {
                            if (result.count > 0) {
                                // Show success message only if tracks were actually added
                                alert(`Traccia aggiunta alla playlist con successo.`);
                            } else {
                                // Track was already in the playlist
                                console.log('Track already exists in playlist');
                            }
                        }
                    });
                }
            } catch (fallbackError) {
                console.error('Error in fallback handling:', fallbackError);
            }
        }
    }
}

// Function to handle drag over a playlist track (for reordering)
function handlePlaylistTrackDragOver(e) {
    e.preventDefault();
    if (draggedItemType === 'playlist-track' && this !== draggedItem) {
        this.classList.add('drag-over');
    }
}

// Function to handle drag leave from a playlist track
function handlePlaylistTrackDragLeave() {
    this.classList.remove('drag-over');
}

// Function to handle drop on a playlist track (for reordering)
function handlePlaylistTrackDrop(e) {
    e.preventDefault();
    e.stopPropagation(); // Prevent the event from bubbling up to the playlist tracks container
    this.classList.remove('drag-over');

    if (draggedItemType !== 'playlist-track' || !currentPlaylist) return;

    try {
        const data = JSON.parse(e.dataTransfer.getData('text/plain'));
        const sourceTrackId = parseInt(data.id);
        const targetTrackId = parseInt(this.dataset.trackId);

        if (sourceTrackId && targetTrackId && sourceTrackId !== targetTrackId) {
            // Get the current tracks in the playlist
            const tracks = Array.from(playlistTracks.querySelectorAll('.playlist-track-item'))
                .map(item => parseInt(item.dataset.trackId));

            // Find the indices of the source and target tracks
            const sourceIndex = tracks.indexOf(sourceTrackId);
            const targetIndex = tracks.indexOf(targetTrackId);

            if (sourceIndex !== -1 && targetIndex !== -1) {
                // Remove the source track from its current position
                tracks.splice(sourceIndex, 1);

                // Insert it at the target position
                tracks.splice(targetIndex, 0, sourceTrackId);

                // Update the positions in the database
                updatePlaylistTrackPositions(currentPlaylist.id, tracks).then(() => {
                    // Refresh the playlist view
                    openPlaylist(currentPlaylist.id);
                });
            }
        }
    } catch (error) {
        console.error('Error handling drop for reordering:', error);
    }
}

// Function to handle drag over the playlist tracks container (for empty playlists)
function handlePlaylistTracksDragOver(e) {
    e.preventDefault();
    if ((draggedItemType === 'track' || draggedItemType === 'playlist-track') &&
        playlistTracks.querySelector('.empty-state')) {
        this.classList.add('drag-over');
    }
}

// Function to handle drag leave from the playlist tracks container
function handlePlaylistTracksDragLeave() {
    this.classList.remove('drag-over');
}

// Function to handle drop on the playlist tracks container (for empty playlists)
function handlePlaylistTracksDrop(e) {
    e.preventDefault();
    this.classList.remove('drag-over');

    if (!currentPlaylist) return;

    try {
        // Log dell'evento drop per diagnostica
        console.log('Drop event on playlist tracks container:', e);

        // Get the data transfer content, check if it's empty
        const dataTransfer = e.dataTransfer.getData('text/plain');
        if (!dataTransfer) {
            console.log('No data received in drop event on playlist tracks container');

            // Se non ci sono dati ma abbiamo un elemento trascinato, proviamo a usare quello
            if (draggedItem && draggedItem.dataset.trackId) {
                const trackId = parseInt(draggedItem.dataset.trackId);

                console.log('Using draggedItem as fallback for playlist tracks container:', { trackId, playlistId: currentPlaylist.id });

                if (trackId) {
                    // Add the track to the playlist
                    addTracksToPlaylist(currentPlaylist.id, [trackId]).then(result => {
                        if (result && result.success) {
                            // Refresh the playlist view only if tracks were actually added
                            if (result.count > 0) {
                                openPlaylist(currentPlaylist.id);
                            } else {
                                console.log('Track already exists in playlist');
                            }
                        }
                    });
                }
            }
            return;
        }

        console.log('Data received in drop event on playlist tracks container:', dataTransfer);
        const data = JSON.parse(dataTransfer);

        console.log('Parsed drop data for playlist tracks container:', { data, playlistId: currentPlaylist.id });

        if (data.type === 'multiple-tracks' && data.tracks && data.tracks.length > 0) {
            // Handle multiple tracks
            const trackIds = data.tracks.map(track => parseInt(track.id)).filter(id => !isNaN(id));
            console.log('Adding multiple tracks to current playlist:', { trackIds, playlistId: currentPlaylist.id });

            // Add the tracks to the playlist
            addTracksToPlaylist(currentPlaylist.id, trackIds).then(result => {
                if (result && result.success) {
                    // Refresh the playlist view only if tracks were actually added
                    if (result.count > 0) {
                        openPlaylist(currentPlaylist.id);
                        alert(`${result.count} tracce aggiunte alla playlist con successo.`);
                    } else {
                        console.log('All tracks already exist in playlist');
                    }
                }
            });
        } else if (data.id) {
            // Handle single track
            const trackId = parseInt(data.id);
            console.log('Adding track to current playlist:', { trackId, playlistId: currentPlaylist.id });

            // Add the track to the playlist
            addTracksToPlaylist(currentPlaylist.id, [trackId]).then(result => {
                if (result && result.success) {
                    // Refresh the playlist view only if tracks were actually added
                    if (result.count > 0) {
                        openPlaylist(currentPlaylist.id);
                    } else {
                        console.log('Track already exists in playlist');
                    }
                }
            });
        }
    } catch (error) {
        console.error('Error handling drop on playlist tracks container:', error);

        // In caso di errore, prova a usare l'elemento trascinato come fallback
        if (draggedItem && draggedItem.dataset.trackId) {
            try {
                const trackId = parseInt(draggedItem.dataset.trackId);

                console.log('Using draggedItem as fallback after error for playlist tracks container:', { trackId, playlistId: currentPlaylist.id });

                if (trackId) {
                    // Add the track to the playlist
                    addTracksToPlaylist(currentPlaylist.id, [trackId]).then(result => {
                        if (result && result.success) {
                            // Refresh the playlist view only if tracks were actually added
                            if (result.count > 0) {
                                openPlaylist(currentPlaylist.id);
                            } else {
                                console.log('Track already exists in playlist');
                            }
                        }
                    });
                }
            } catch (fallbackError) {
                console.error('Error in fallback handling for playlist tracks container:', fallbackError);
            }
        }
    }
}

// Function to update playlist track positions in the database
async function updatePlaylistTrackPositions(playlistId, trackIds) {
    try {
        // Use the new reorderTracks handler that handles everything in a single transaction
        return await window.playlist.reorderTracks({
            playlistId,
            trackIds
        });
    } catch (error) {
        console.error('Failed to update track positions:', error);
        alert(`Failed to update track order: ${error.message}`);
        return null;
    }
}

// Variables for subset drag and drop
let draggedSubset = null;

// Function to handle subset drag start
function handleSubsetDragStart(e) {
    draggedSubset = this;

    // Set the data to be transferred
    const cycleNumber = this.dataset.cycle;

    // Prepare the data to transfer
    const transferData = {
        type: 'subset',
        cycle: cycleNumber
    };

    console.log('Subset drag start with data:', transferData);

    // Set the data in dataTransfer
    e.dataTransfer.setData('text/plain', JSON.stringify(transferData));

    // Set the effect
    e.dataTransfer.effectAllowed = 'move';

    // Add dragging class for visual feedback
    setTimeout(() => {
        this.classList.add('dragging');
    }, 0);
}

// Function to handle subset drag end
function handleSubsetDragEnd() {
    this.classList.remove('dragging');
    draggedSubset = null;

    // Remove all drag-over classes
    document.querySelectorAll('.drag-over').forEach(item => {
        item.classList.remove('drag-over');
    });
}

// Function to handle drag over a subset
function handleSubsetDragOver(e) {
    e.preventDefault();

    // Only accept if the dragged item is a subset
    if (draggedSubset && this !== draggedSubset) {
        this.classList.add('drag-over');
    }

    // Set the drop effect
    e.dataTransfer.dropEffect = 'move';
}

// Function to handle drag leave from a subset
function handleSubsetDragLeave() {
    this.classList.remove('drag-over');
}

// Function to handle drop on a subset
function handleSubsetDrop(e) {
    e.preventDefault();
    e.stopPropagation(); // Prevent the event from bubbling up
    this.classList.remove('drag-over');

    if (!draggedSubset || !currentPlaylist || !currentPlaylist.is_generated) return;

    try {
        const data = JSON.parse(e.dataTransfer.getData('text/plain'));

        if (data.type !== 'subset') return;

        const sourceCycle = parseInt(data.cycle);
        const targetCycle = parseInt(this.dataset.cycle);

        if (sourceCycle === targetCycle) return;

        console.log(`Reordering subset from cycle ${sourceCycle} to cycle ${targetCycle}`);

        // Get all cycles in the current playlist
        const allCycles = Array.from(
            new Set(
                Array.from(playlistTracks.querySelectorAll('.playlist-track-item'))
                    .map(item => parseInt(item.dataset.cycle))
            )
        ).sort((a, b) => a - b); // Sort cycles numerically

        console.log('Current cycle order:', allCycles);

        // Create a new cycle order by swapping the source and target cycles
        const newCycleOrder = [...allCycles];

        // Find the indices of the source and target cycles
        const sourceIndex = newCycleOrder.indexOf(sourceCycle);
        const targetIndex = newCycleOrder.indexOf(targetCycle);

        if (sourceIndex === -1 || targetIndex === -1) {
            console.error('Source or target cycle not found in cycle list');
            return;
        }

        // Remove the source cycle from its current position
        newCycleOrder.splice(sourceIndex, 1);

        // Determine where to insert the source cycle
        let insertIndex;
        if (sourceIndex < targetIndex) {
            // If moving down, insert after the target
            insertIndex = targetIndex - 1;
        } else {
            // If moving up, insert before the target
            insertIndex = targetIndex;
        }

        // Insert the source cycle at the new position
        newCycleOrder.splice(insertIndex, 0, sourceCycle);

        console.log('New cycle order:', newCycleOrder);

        // Use our new IPC handler to reorder the cycles
        window.playlist.reorderCycles({
            playlistId: currentPlaylist.id,
            cycleOrder: newCycleOrder
        }).then(result => {
            if (result.success) {
                console.log(result.message);
                // Refresh the playlist view
                openPlaylist(currentPlaylist.id);
            } else {
                console.error('Failed to reorder cycles:', result.error);
                alert(`Failed to reorder cycles: ${result.error}`);
            }
        }).catch(error => {
            console.error('Error reordering cycles:', error);
            alert(`Error reordering cycles: ${error.message}`);
        });
    } catch (error) {
        console.error('Error handling subset drop:', error);
    }
}

// Function to handle cycle up button click
function handleCycleUpClick(e) {
    e.preventDefault();
    e.stopPropagation(); // Prevent the event from bubbling up

    if (!currentPlaylist || !currentPlaylist.is_generated) return;

    try {
        const cycleNumber = parseInt(this.dataset.cycle);
        console.log(`Moving cycle ${cycleNumber} up`);

        // Get all cycles in the current playlist
        const allCycles = Array.from(
            new Set(
                Array.from(playlistTracks.querySelectorAll('.playlist-track-item'))
                    .map(item => parseInt(item.dataset.cycle))
            )
        ).sort((a, b) => a - b); // Sort cycles numerically

        console.log('Current cycle order:', allCycles);

        // Find the index of the current cycle
        const currentIndex = allCycles.indexOf(cycleNumber);

        // If it's already at the top or not found, do nothing
        if (currentIndex <= 0) {
            console.log('Cycle is already at the top or not found');
            return;
        }

        // Create a new cycle order by moving the current cycle up
        const newCycleOrder = [...allCycles];

        // Swap the current cycle with the one above it
        [newCycleOrder[currentIndex], newCycleOrder[currentIndex - 1]] =
            [newCycleOrder[currentIndex - 1], newCycleOrder[currentIndex]];

        console.log('New cycle order:', newCycleOrder);

        // Use the IPC handler to reorder the cycles
        window.playlist.reorderCycles({
            playlistId: currentPlaylist.id,
            cycleOrder: newCycleOrder
        }).then(result => {
            if (result.success) {
                console.log(result.message);
                // Refresh the playlist view
                openPlaylist(currentPlaylist.id);
            } else {
                console.error('Failed to reorder cycles:', result.error);
                alert(`Failed to reorder cycles: ${result.error}`);
            }
        }).catch(error => {
            console.error('Error reordering cycles:', error);
            alert(`Error reordering cycles: ${error.message}`);
        });
    } catch (error) {
        console.error('Error handling cycle up click:', error);
    }
}

// Function to handle cycle down button click
function handleCycleDownClick(e) {
    e.preventDefault();
    e.stopPropagation(); // Prevent the event from bubbling up

    if (!currentPlaylist || !currentPlaylist.is_generated) return;

    try {
        const cycleNumber = parseInt(this.dataset.cycle);
        console.log(`Moving cycle ${cycleNumber} down`);

        // Get all cycles in the current playlist
        const allCycles = Array.from(
            new Set(
                Array.from(playlistTracks.querySelectorAll('.playlist-track-item'))
                    .map(item => parseInt(item.dataset.cycle))
            )
        ).sort((a, b) => a - b); // Sort cycles numerically

        console.log('Current cycle order:', allCycles);

        // Find the index of the current cycle
        const currentIndex = allCycles.indexOf(cycleNumber);

        // If it's already at the bottom or not found, do nothing
        if (currentIndex === -1 || currentIndex >= allCycles.length - 1) {
            console.log('Cycle is already at the bottom or not found');
            return;
        }

        // Create a new cycle order by moving the current cycle down
        const newCycleOrder = [...allCycles];

        // Swap the current cycle with the one below it
        [newCycleOrder[currentIndex], newCycleOrder[currentIndex + 1]] =
            [newCycleOrder[currentIndex + 1], newCycleOrder[currentIndex]];

        console.log('New cycle order:', newCycleOrder);

        // Use the IPC handler to reorder the cycles
        window.playlist.reorderCycles({
            playlistId: currentPlaylist.id,
            cycleOrder: newCycleOrder
        }).then(result => {
            if (result.success) {
                console.log(result.message);
                // Refresh the playlist view
                openPlaylist(currentPlaylist.id);
            } else {
                console.error('Failed to reorder cycles:', result.error);
                alert(`Failed to reorder cycles: ${result.error}`);
            }
        }).catch(error => {
            console.error('Error reordering cycles:', error);
            alert(`Error reordering cycles: ${error.message}`);
        });
    } catch (error) {
        console.error('Error handling cycle down click:', error);
    }
}

// Function to add drag and drop event listeners to track items
function addDragAndDropListeners() {
    // Add listeners to track items in the main track list
    document.querySelectorAll('.track-item').forEach(item => {
        item.addEventListener('dragstart', handleDragStart);
        item.addEventListener('dragend', handleDragEnd);
    });

    // Add listeners to track items in the playlist
    document.querySelectorAll('.playlist-track-item').forEach(item => {
        item.addEventListener('dragstart', handleDragStart);
        item.addEventListener('dragend', handleDragEnd);
        item.addEventListener('dragover', handlePlaylistTrackDragOver);
        item.addEventListener('dragleave', handlePlaylistTrackDragLeave);
        item.addEventListener('drop', handlePlaylistTrackDrop);
    });

    // Add listeners to track subsets in generated playlists
    document.querySelectorAll('.track-subset').forEach(subset => {
        subset.addEventListener('dragstart', handleSubsetDragStart);
        subset.addEventListener('dragend', handleSubsetDragEnd);
        subset.addEventListener('dragover', handleSubsetDragOver);
        subset.addEventListener('dragleave', handleSubsetDragLeave);
        subset.addEventListener('drop', handleSubsetDrop);
    });

    // Add listeners to subset headers (for better drag handle)
    document.querySelectorAll('.track-subset-header').forEach(header => {
        header.addEventListener('mousedown', () => {
            // Find the parent subset and make it draggable
            const subset = header.closest('.track-subset');
            if (subset) {
                subset.draggable = true;
            }
        });

        header.addEventListener('mouseup', () => {
            // Find the parent subset and make it not draggable
            const subset = header.closest('.track-subset');
            if (subset) {
                subset.draggable = false;
            }
        });
    });

    // Add listeners to cycle up/down buttons
    document.querySelectorAll('.cycle-up-button').forEach(button => {
        button.addEventListener('click', handleCycleUpClick);
    });

    document.querySelectorAll('.cycle-down-button').forEach(button => {
        button.addEventListener('click', handleCycleDownClick);
    });

    // Add listeners to track up/down buttons
    document.querySelectorAll('.track-up-button').forEach(button => {
        button.addEventListener('click', handleTrackUpClick);
    });

    document.querySelectorAll('.track-down-button').forEach(button => {
        button.addEventListener('click', handleTrackDownClick);
    });

    // Add listeners to the playlist tracks container
    if (playlistTracks) {
        playlistTracks.addEventListener('dragover', handlePlaylistTracksDragOver);
        playlistTracks.addEventListener('dragleave', handlePlaylistTracksDragLeave);
        playlistTracks.addEventListener('drop', handlePlaylistTracksDrop);
    }

    // Assicuriamoci che tutte le playlist abbiano gli event listener per il drag and drop
    document.querySelectorAll('.playlist-item').forEach(item => {
        // Rimuovi prima gli event listener esistenti per evitare duplicati
        item.removeEventListener('dragstart', handlePlaylistDragStart);
        item.removeEventListener('dragend', handlePlaylistDragEnd);
        item.removeEventListener('dragover', handlePlaylistDragOver);
        item.removeEventListener('dragleave', handlePlaylistDragLeave);
        item.removeEventListener('drop', handlePlaylistDrop);

        // Aggiungi gli event listener
        item.addEventListener('dragstart', handlePlaylistDragStart);
        item.addEventListener('dragend', handlePlaylistDragEnd);
        item.addEventListener('dragover', handlePlaylistDragOver);
        item.addEventListener('dragleave', handlePlaylistDragLeave);
        item.addEventListener('drop', handlePlaylistDrop);

        console.log('Added drag and drop listeners to playlist:', item.dataset.playlistId);
    });
}

// Update the track list rendering function to include "Add to Playlist" button and drag and drop
const originalRenderTrackList = renderTrackList;
renderTrackList = function (files, searchTerm = '') {
    originalRenderTrackList(files, searchTerm);
    updateTrackListWithPlaylistButtons();
    addDragAndDropListeners();
};

// Initial theme setup and library load
document.addEventListener('DOMContentLoaded', async () => {
    console.log('DOMContentLoaded event fired.'); // Added log

    // Assicuriamoci che i popup siano nascosti all'avvio
    const popups = [
        document.getElementById('edit-tags-popup'),
        document.getElementById('create-playlist-popup'),
        document.getElementById('add-to-playlist-popup'),
        document.getElementById('manage-default-tags-popup'),
        document.getElementById('edit-default-tag-popup'),
        document.getElementById('tag-playlist-generator-popup'),
        document.getElementById('track-replacement-popup')
    ];

    popups.forEach(popup => {
        if (popup) {
            popup.classList.add('hidden');
            popup.classList.add('fully-hidden');
        }
    });

    console.log('Popups hidden at startup');

    await initializeTheme(); // Initialize theme
    // Add a small delay to potentially allow the main process to be fully ready
    await new Promise(resolve => setTimeout(resolve, 500)); // 500ms delay
    await loadLibrary(); // Load library on startup
    await loadPlaylists(); // Load playlists on startup
    await loadAllTags(); // Load all tags for filtering
    await loadDefaultTags(); // Load default tags on startup

    console.log('Initial library, playlists and default tags load attempted.'); // Added log

    // Set up event listener for tag-based playlist generator button
    const generateTagPlaylistBtn = document.getElementById('generate-tag-playlist-btn');
    if (generateTagPlaylistBtn) {
        generateTagPlaylistBtn.addEventListener('click', openTagPlaylistGeneratorPopup);
    }

    // Set up event listener for step builder button
    const stepBuilderBtn = document.getElementById('step-builder-btn');
    if (stepBuilderBtn) {
        stepBuilderBtn.addEventListener('click', async () => {
            await openStepBuilderPopup();
        });
    }

    // Set up track replacement popup event listeners
    setupTrackReplacementPopupListeners();

    // Test to verify our changes are working
    console.log('DOMContentLoaded: Testing track replacement popup elements...');
    console.log('trackReplacementPopup:', document.getElementById('track-replacement-popup'));
    console.log('closeTrackReplacementBtn:', document.getElementById('close-track-replacement-btn'));
    console.log('cancelTrackReplacementBtn:', document.getElementById('cancel-track-replacement-btn'));
    console.log('selectTagsBtn:', document.querySelector('.select-tags-btn'));
    console.log('replaceCustomBtn:', document.querySelector('.replace-custom-btn'));
    console.log('customTagSelection:', document.querySelector('.custom-tag-selection'));
    console.log('availableTagsContainer:', document.getElementById('available-tags-for-replacement'));

    // Get the last selected directory but don't scan automatically
    const lastDirectory = await window.library.getLastDirectory();
    if (lastDirectory) {
        console.log('Last selected directory:', lastDirectory);
        // We don't need to scan automatically, the tracks are already loaded from DB
        // If you want to enable auto-scanning, uncomment the next line:
        // await window.library.scan();
    }
});

// Add event listener for track list clicks (event delegation)
trackList.addEventListener('click', (event) => {
    // Handle checkbox clicks
    if (event.target.classList.contains('track-checkbox')) {
        const trackItem = event.target.closest('.track-item');
        const trackId = parseInt(trackItem.dataset.trackId);

        if (event.target.checked) {
            // Add to selected tracks if not already there
            if (!selectedTracks.includes(trackId)) {
                selectedTracks.push(trackId);
                trackItem.classList.add('selected');
            }
        } else {
            // Remove from selected tracks
            const index = selectedTracks.indexOf(trackId);
            if (index !== -1) {
                selectedTracks.splice(index, 1);
                trackItem.classList.remove('selected');
            }
        }

        // Update UI to show how many tracks are selected
        updateSelectedTracksCount();
        return;
    }

    const target = event.target.closest('.track-button'); // Find the closest button ancestor
    if (!target) return;

    const path = target.dataset.path;
    if (!path) return;

    if (target.classList.contains('play-button')) {
        // Play the track
        window.playTrack(path);
    } else if (target.classList.contains('edit-button')) {
        // Open the edit popup
        const title = target.dataset.title;
        const artist = target.dataset.artist;
        const tags = target.dataset.tags;
        openEditTagsPopup(path, title, artist, tags);
    }
});

// Add event listener for playlist tracks clicks (event delegation)
playlistTracks.addEventListener('click', (event) => {
    // Handle checkbox clicks
    if (event.target.classList.contains('track-checkbox')) {
        const trackItem = event.target.closest('.playlist-track-item');
        const trackId = parseInt(trackItem.dataset.trackId);

        if (event.target.checked) {
            // Add to selected tracks if not already there
            if (!selectedTracks.includes(trackId)) {
                selectedTracks.push(trackId);
                trackItem.classList.add('selected');
            }
        } else {
            // Remove from selected tracks
            const index = selectedTracks.indexOf(trackId);
            if (index !== -1) {
                selectedTracks.splice(index, 1);
                trackItem.classList.remove('selected');
            }
        }

        // Update UI to show how many tracks are selected
        updateSelectedTracksCount();
    }
});

// Function to update the selected tracks count in the UI
function updateSelectedTracksCount() {
    const selectedCount = selectedTracks.length;
    const selectedTracksStatus = document.getElementById('selected-tracks-status');
    const selectedCountSpan = document.getElementById('selected-count');

    // Update UI to show how many tracks are selected
    if (selectedCount > 0) {
        // Update the status bar
        selectedCountSpan.textContent = selectedCount;
        selectedTracksStatus.classList.remove('hidden');

        // Enable any buttons that require track selection
        document.querySelectorAll('.requires-selection').forEach(btn => {
            btn.disabled = false;
        });
    } else {
        // Hide the selected tracks count in the status bar
        selectedTracksStatus.classList.add('hidden');

        // Disable any buttons that require track selection
        document.querySelectorAll('.requires-selection').forEach(btn => {
            btn.disabled = true;
        });
    }
}

// Function to select all visible tracks
function selectAllTracks() {
    // Get all visible track items
    const trackItems = document.querySelectorAll('.track-item:not(.hidden), .playlist-track-item:not(.hidden)');

    // Clear the current selection
    selectedTracks = [];

    // Add all visible tracks to the selection
    trackItems.forEach(item => {
        const trackId = parseInt(item.dataset.trackId);
        if (!isNaN(trackId)) {
            selectedTracks.push(trackId);
            item.classList.add('selected');

            // Check the checkbox
            const checkbox = item.querySelector('.track-checkbox');
            if (checkbox) {
                checkbox.checked = true;
            }
        }
    });

    // Update the UI
    updateSelectedTracksCount();
}

// Function to deselect all tracks
function deselectAllTracks() {
    // Get all selected track items
    const selectedItems = document.querySelectorAll('.track-item.selected, .playlist-track-item.selected');

    // Remove the selected class and uncheck checkboxes
    selectedItems.forEach(item => {
        item.classList.remove('selected');

        // Uncheck the checkbox
        const checkbox = item.querySelector('.track-checkbox');
        if (checkbox) {
            checkbox.checked = false;
        }
    });

    // Clear the selection array
    selectedTracks = [];

    // Update the UI
    updateSelectedTracksCount();
}

// Function to add selected tracks to a playlist
function addSelectedTracksToPlaylist() {
    if (selectedTracks.length === 0) return;

    // Open the add to playlist popup with the selected tracks
    openAddToPlaylistPopup(selectedTracks);
}

// Variabili per i tag predefiniti
// Utilizziamo window per evitare duplicazioni di variabili
window.defaultTagsData = window.defaultTagsData || [];
const manageTagsBtn = document.getElementById('manage-tags-btn');
const defaultTagsContainer = document.getElementById('default-tags-container');
const manageDefaultTagsPopup = document.getElementById('manage-default-tags-popup');
const closeDefaultTagsPopupBtn = document.getElementById('close-default-tags-popup-btn');
const defaultTagsList = document.getElementById('default-tags-list');
const addDefaultTagBtn = document.getElementById('add-default-tag-btn');
const closeManageTagsBtn = document.getElementById('close-manage-tags-btn');
const editDefaultTagPopup = document.getElementById('edit-default-tag-popup');
const editDefaultTagForm = document.getElementById('edit-default-tag-form');
const defaultTagIdInput = document.getElementById('default-tag-id');
const defaultTagNameInput = document.getElementById('default-tag-name');
const defaultTagColorInput = document.getElementById('default-tag-color');
const editDefaultTagTitle = document.getElementById('edit-default-tag-title');
const closeEditDefaultTagBtn = document.getElementById('close-edit-default-tag-btn');
const cancelDefaultTagBtn = document.getElementById('cancel-default-tag-btn');
const saveDefaultTagBtn = document.getElementById('save-default-tag-btn');

// Funzione per caricare i tag predefiniti
async function loadDefaultTags() {
    try {
        window.defaultTagsData = await window.defaultTags.getAll();
        console.log('Loaded default tags:', window.defaultTagsData);
        return window.defaultTagsData;
    } catch (error) {
        console.error('Failed to load default tags:', error);
        return [];
    }
}

// Funzione per ottenere la classe CSS per un colore
function getColorClass(color) {
    if (!color) return '';

    // Mappa dei colori comuni alle classi CSS
    const colorMap = {
        '#ef4444': 'tag-color-red',
        '#f97316': 'tag-color-orange',
        '#eab308': 'tag-color-yellow',
        '#22c55e': 'tag-color-green',
        '#3b82f6': 'tag-color-blue',
        '#a855f7': 'tag-color-purple',
        '#ec4899': 'tag-color-pink',
        '#6b7280': 'tag-color-gray',
        '#1f2937': 'tag-color-black'
    };

    // Trova il colore più vicino nella mappa
    let closestColor = '';
    let minDistance = Number.MAX_VALUE;

    // Converti il colore in RGB
    const hexToRgb = (hex) => {
        const r = parseInt(hex.slice(1, 3), 16);
        const g = parseInt(hex.slice(3, 5), 16);
        const b = parseInt(hex.slice(5, 7), 16);
        return [r, g, b];
    };

    // Calcola la distanza euclidea tra due colori RGB
    const colorDistance = (rgb1, rgb2) => {
        return Math.sqrt(
            Math.pow(rgb1[0] - rgb2[0], 2) +
            Math.pow(rgb1[1] - rgb2[1], 2) +
            Math.pow(rgb1[2] - rgb2[2], 2)
        );
    };

    try {
        const targetRgb = hexToRgb(color.toLowerCase());

        for (const [hex, className] of Object.entries(colorMap)) {
            const currentRgb = hexToRgb(hex);
            const distance = colorDistance(targetRgb, currentRgb);

            if (distance < minDistance) {
                minDistance = distance;
                closestColor = className;
            }
        }

        return closestColor || 'tag-color-blue'; // Default a blu se non troviamo una corrispondenza
    } catch (e) {
        console.error('Errore nel calcolo della classe di colore:', e);
        return 'tag-color-blue'; // Default a blu in caso di errore
    }
}

// Funzione per ottenere la classe CSS per un colore del punto
function getDotColorClass(color) {
    if (!color) return '';

    // Sostituisci 'tag-color-' con 'tag-dot-'
    return getColorClass(color).replace('tag-color-', 'tag-dot-');
}

// Funzione per renderizzare i tag predefiniti nel popup di modifica
function renderDefaultTagsInEditPopup() {
    if (!defaultTagsContainer) return;

    if (window.defaultTagsData.length === 0) {
        defaultTagsContainer.innerHTML = `<div class="empty-state">Nessun tag predefinito disponibile</div>`;
        return;
    }

    defaultTagsContainer.innerHTML = window.defaultTagsData.map(tag => {
        const colorClass = tag.color ? `${getColorClass(tag.color)} tag-color` : '';
        return `<div class="default-tag-item ${colorClass}" data-tag-id="${tag.id}" data-tag-name="${tag.name}">${tag.name}</div>`;
    }).join('');

    // Aggiungi event listener per i tag predefiniti
    document.querySelectorAll('.default-tag-item').forEach(tagItem => {
        tagItem.addEventListener('click', handleDefaultTagClick);
    });
}

// Funzione per renderizzare i tag predefiniti nel popup di gestione
function renderDefaultTagsInManagePopup() {
    if (!defaultTagsList) return;

    if (window.defaultTagsData.length === 0) {
        defaultTagsList.innerHTML = `<div class="empty-state">Nessun tag predefinito disponibile. Aggiungi un nuovo tag.</div>`;
        return;
    }

    defaultTagsList.innerHTML = window.defaultTagsData.map(tag => {
        const dotColorClass = tag.color ? getDotColorClass(tag.color) : '';
        return `
            <div class="default-tag-list-item" data-tag-id="${tag.id}">
                <div class="default-tag-name">
                    <div class="default-tag-color ${dotColorClass}"></div>
                    ${tag.name}
                </div>
                <div class="default-tag-actions">
                    <button class="tag-action-btn edit" data-tag-id="${tag.id}" data-tag-name="${tag.name}" data-tag-color="${tag.color || ''}">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                            <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                        </svg>
                    </button>
                    <button class="tag-action-btn delete" data-tag-id="${tag.id}">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <polyline points="3 6 5 6 21 6"></polyline>
                            <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                        </svg>
                    </button>
                </div>
            </div>
        `;
    }).join('');

    // Aggiungi event listener per i pulsanti di modifica e cancellazione
    document.querySelectorAll('.tag-action-btn.edit').forEach(btn => {
        btn.addEventListener('click', handleEditDefaultTag);
    });

    document.querySelectorAll('.tag-action-btn.delete').forEach(btn => {
        btn.addEventListener('click', handleDeleteDefaultTag);
    });
}

// Funzione per gestire il click su un tag predefinito
function handleDefaultTagClick(event) {
    const tagName = event.currentTarget.dataset.tagName;
    const tagsInput = document.getElementById('edit-tags');

    if (!tagsInput) return;

    // Ottieni i tag attuali
    let currentTags = tagsInput.value.trim();
    let tagsArray = currentTags ? currentTags.split(',').map(tag => tag.trim()).filter(tag => tag) : [];

    // Controlla se il tag è già presente
    const tagIndex = tagsArray.findIndex(tag => tag.toLowerCase() === tagName.toLowerCase());

    if (tagIndex !== -1) {
        // Rimuovi il tag se già presente
        tagsArray.splice(tagIndex, 1);
        event.currentTarget.classList.remove('active');
    } else {
        // Aggiungi il tag se non è presente
        tagsArray.push(tagName);
        event.currentTarget.classList.add('active');
    }

    // Aggiorna il campo di input
    tagsInput.value = tagsArray.join(', ');
}

// Funzione per aprire il popup di gestione dei tag predefiniti
function openManageTagsPopup() {
    loadDefaultTags().then(() => {
        renderDefaultTagsInManagePopup();

        manageDefaultTagsPopup.classList.remove('fully-hidden');
        manageDefaultTagsPopup.classList.remove('hidden');

        // Aggiungi event listeners
        closeDefaultTagsPopupBtn.addEventListener('click', closeManageTagsPopup);
        closeManageTagsBtn.addEventListener('click', closeManageTagsPopup);
        addDefaultTagBtn.addEventListener('click', openAddDefaultTagPopup);

        // Event listener per il click fuori dal popup
        manageDefaultTagsPopup.addEventListener('click', handleManageTagsPopupOutsideClick);
    });
}

// Funzione per chiudere il popup di gestione dei tag predefiniti
function closeManageTagsPopup() {
    // Rimuovi event listeners
    closeDefaultTagsPopupBtn.removeEventListener('click', closeManageTagsPopup);
    closeManageTagsBtn.removeEventListener('click', closeManageTagsPopup);
    addDefaultTagBtn.removeEventListener('click', openAddDefaultTagPopup);
    manageDefaultTagsPopup.removeEventListener('click', handleManageTagsPopupOutsideClick);

    // Nascondi il popup
    manageDefaultTagsPopup.classList.add('hidden');
    setTimeout(() => {
        manageDefaultTagsPopup.classList.add('fully-hidden');
    }, 300);

    // Aggiorna i tag predefiniti nel popup di modifica
    renderDefaultTagsInEditPopup();
}

// Funzione per gestire il click fuori dal popup di gestione
function handleManageTagsPopupOutsideClick(event) {
    if (event.target === manageDefaultTagsPopup) {
        closeManageTagsPopup();
    }
}

// Funzione per aprire il popup di aggiunta/modifica di un tag predefinito
function openAddDefaultTagPopup(tagId = null, tagName = '', tagColor = '') {
    // Imposta il titolo del popup
    editDefaultTagTitle.textContent = tagId ? 'Modifica Tag Predefinito' : 'Aggiungi Tag Predefinito';

    // Resetta il form
    editDefaultTagForm.reset();

    // Imposta i valori del form
    defaultTagIdInput.value = tagId || '';
    defaultTagNameInput.value = tagName || '';
    defaultTagColorInput.value = tagColor || '#4a90e2';

    // Mostra il popup
    editDefaultTagPopup.classList.remove('fully-hidden');
    editDefaultTagPopup.classList.remove('hidden');

    // Aggiungi event listeners
    editDefaultTagForm.addEventListener('submit', handleSaveDefaultTag);
    closeEditDefaultTagBtn.addEventListener('click', closeEditDefaultTagPopup);
    cancelDefaultTagBtn.addEventListener('click', closeEditDefaultTagPopup);

    // Event listener per il click fuori dal popup
    editDefaultTagPopup.addEventListener('click', handleEditDefaultTagPopupOutsideClick);

    // Focus sul campo del nome
    setTimeout(() => {
        defaultTagNameInput.focus();
    }, 50);
}

// Funzione per chiudere il popup di aggiunta/modifica di un tag predefinito
function closeEditDefaultTagPopup() {
    // Rimuovi event listeners
    editDefaultTagForm.removeEventListener('submit', handleSaveDefaultTag);
    closeEditDefaultTagBtn.removeEventListener('click', closeEditDefaultTagPopup);
    cancelDefaultTagBtn.removeEventListener('click', closeEditDefaultTagPopup);
    editDefaultTagPopup.removeEventListener('click', handleEditDefaultTagPopupOutsideClick);

    // Nascondi il popup
    editDefaultTagPopup.classList.add('hidden');
    setTimeout(() => {
        editDefaultTagPopup.classList.add('fully-hidden');
    }, 300);
}

// Funzione per gestire il click fuori dal popup di aggiunta/modifica
function handleEditDefaultTagPopupOutsideClick(event) {
    if (event.target === editDefaultTagPopup) {
        closeEditDefaultTagPopup();
    }
}

// Funzione per gestire il salvataggio di un tag predefinito
async function handleSaveDefaultTag(event) {
    event.preventDefault();

    const tagId = defaultTagIdInput.value;
    const tagName = defaultTagNameInput.value.trim();
    const tagColor = defaultTagColorInput.value;

    if (!tagName) {
        alert('Il nome del tag è obbligatorio');
        return;
    }

    try {
        let result;

        if (tagId) {
            // Aggiorna un tag esistente
            result = await window.defaultTags.update({
                id: parseInt(tagId),
                name: tagName,
                color: tagColor
            });
        } else {
            // Aggiungi un nuovo tag
            result = await window.defaultTags.add({
                name: tagName,
                color: tagColor
            });
        }

        if (result) {
            // Ricarica i tag predefiniti
            await loadDefaultTags();

            // Aggiorna la visualizzazione
            renderDefaultTagsInManagePopup();

            // Chiudi il popup
            closeEditDefaultTagPopup();
        }
    } catch (error) {
        console.error('Error saving default tag:', error);
        alert(`Errore durante il salvataggio del tag: ${error.message}`);
    }
}

// Funzione per gestire la modifica di un tag predefinito
function handleEditDefaultTag(event) {
    event.stopPropagation();

    const tagId = event.currentTarget.dataset.tagId;
    const tagName = event.currentTarget.dataset.tagName;
    const tagColor = event.currentTarget.dataset.tagColor;

    openAddDefaultTagPopup(tagId, tagName, tagColor);
}

// Funzione per gestire l'eliminazione di un tag predefinito
async function handleDeleteDefaultTag(event) {
    event.stopPropagation();

    const tagId = event.currentTarget.dataset.tagId;
    const confirmation = confirm('Sei sicuro di voler eliminare questo tag predefinito?');

    if (confirmation && tagId) {
        try {
            const result = await window.defaultTags.delete({ id: parseInt(tagId) });

            if (result && result.success) {
                // Ricarica i tag predefiniti
                await loadDefaultTags();

                // Aggiorna la visualizzazione
                renderDefaultTagsInManagePopup();
            }
        } catch (error) {
            console.error('Error deleting default tag:', error);
            alert(`Errore durante l'eliminazione del tag: ${error.message}`);
        }
    }
}

// Funzioni per gestire il popup di modifica
let editCounter = 0; // Contatore per tenere traccia delle modifiche

// Named function for popup outside click handler
function handlePopupOutsideClick(event) {
    if (event.target === document.getElementById('edit-tags-popup')) {
        closeEditTagsPopup();
    }
}

// Function to open the edit tags popup
function openEditTagsPopup(filePath, title, artist, tags = '') {
    console.log('Opening edit popup for:', filePath, 'Edit counter:', editCounter);
    editCounter++; // Incrementa il contatore ad ogni apertura

    // Get the popup element directly
    const popup = document.getElementById('edit-tags-popup');
    if (!popup) {
        console.error('Popup element not found');
        return;
    }

    // Get form elements directly
    const filePathInput = document.getElementById('edit-file-path');
    const titleInput = document.getElementById('edit-title');
    const artistInput = document.getElementById('edit-artist');
    const tagsInput = document.getElementById('edit-tags');
    const form = document.getElementById('edit-tags-form');
    const closeBtn = document.getElementById('close-popup-btn');
    const cancelBtn = document.getElementById('cancel-edit-btn');
    const saveBtn = document.getElementById('save-tags-btn');

    if (!filePathInput || !titleInput || !artistInput || !tagsInput || !form || !closeBtn || !cancelBtn || !saveBtn) {
        console.error('Form elements not found');
        return;
    }

    // Reset the form
    form.reset();

    // Set the form values first
    filePathInput.value = filePath;
    titleInput.value = title || '';
    artistInput.value = artist || '';
    tagsInput.value = tags || '';

    console.log('Form values set:', {
        title: titleInput.value,
        artist: artistInput.value,
        tags: tagsInput.value
    });

    // Ensure all inputs are enabled and editable
    console.log('Input states before forcing enable:', {
        titleDisabled: titleInput.disabled,
        titleReadOnly: titleInput.readOnly,
        artistDisabled: artistInput.disabled,
        artistReadOnly: artistInput.readOnly,
        tagsDisabled: tagsInput.disabled,
        tagsReadOnly: tagsInput.readOnly
    });

    [titleInput, artistInput, tagsInput].forEach(input => {
        input.disabled = false;
        input.readOnly = false;
    });

    console.log('Input states after forcing enable:', {
        titleDisabled: titleInput.disabled,
        titleReadOnly: titleInput.readOnly,
        artistDisabled: artistInput.disabled,
        artistReadOnly: artistInput.readOnly,
        tagsDisabled: tagsInput.disabled,
        tagsReadOnly: tagsInput.readOnly
    });

    // Carica e renderizza i tag predefiniti
    loadDefaultTags().then(() => {
        renderDefaultTagsInEditPopup();

        // Evidenzia i tag predefiniti già presenti
        if (tags) {
            const currentTags = tags.split(',').map(tag => tag.trim().toLowerCase());
            document.querySelectorAll('.default-tag-item').forEach(tagItem => {
                const tagName = tagItem.dataset.tagName.toLowerCase();
                if (currentTags.includes(tagName)) {
                    tagItem.classList.add('active');
                }
            });
        }
    });

    // Add event listeners
    // Ensure listeners are removed in closeEditTagsPopup to prevent duplicates
    form.addEventListener('submit', handleTagFormSubmit);
    closeBtn.addEventListener('click', closeEditTagsPopup);
    cancelBtn.addEventListener('click', closeEditTagsPopup);

    // Aggiungi event listener per il pulsante di gestione dei tag
    if (manageTagsBtn) {
        manageTagsBtn.addEventListener('click', openManageTagsPopup);
    }

    // Event listener for clicking outside the popup (using named function)
    popup.addEventListener('click', handlePopupOutsideClick);

    // Log finale con tutti i dettagli
    console.log('Final form state:', {
        title: titleInput.value,
        artist: artistInput.value,
        tags: tagsInput.value,
        disabled: titleInput.disabled,
        readOnly: titleInput.readOnly,
        editCounter: editCounter
    });

    // Rendi il popup visibile rimuovendo le classi 'hidden'
    popup.classList.remove('fully-hidden');
    popup.classList.remove('hidden');

    // Usa requestAnimationFrame e setTimeout per impostare il focus dopo che il popup è visibile
    requestAnimationFrame(() => {
        setTimeout(() => {
            // Verifica che il popup sia ancora visibile prima di impostare il focus
            if (!popup.classList.contains('hidden') && !popup.classList.contains('fully-hidden')) {
                try {
                    titleInput.focus({ preventScroll: true });
                    console.log('Focus set attempt on titleInput after rAF and setTimeout.');
                    if (document.activeElement === titleInput) {
                        console.log('Focus successfully set.');
                    } else {
                        console.warn('Focus attempt made, but titleInput is not the active element.');
                    }
                } catch (e) {
                    console.error('Error setting focus:', e);
                }
            } else {
                console.log('Popup was hidden before focus could be set.');
            }
        }, 50); // Un piccolo ritardo per assicurare il rendering
    });
}

// Function to close the edit tags popup
function closeEditTagsPopup() {
    console.log('Closing edit popup, edit counter:', editCounter);

    // Get the popup element directly
    const popup = document.getElementById('edit-tags-popup');
    if (!popup) {
        console.error('Popup element not found');
        return;
    }

    // Get form elements to remove event listeners
    const form = document.getElementById('edit-tags-form');
    const closeBtn = document.getElementById('close-popup-btn');
    const cancelBtn = document.getElementById('cancel-edit-btn');

    // Remove event listeners
    if (form) {
        form.removeEventListener('submit', handleTagFormSubmit); // Ensure submit listener is removed on close
    }

    if (closeBtn) {
        closeBtn.removeEventListener('click', closeEditTagsPopup);
    }

    if (cancelBtn) {
        cancelBtn.removeEventListener('click', closeEditTagsPopup);
    }

    // Rimuovi event listener per il pulsante di gestione dei tag
    if (manageTagsBtn) {
        manageTagsBtn.removeEventListener('click', openManageTagsPopup);
    }

    // Remove click event listener from popup using the named function
    popup.removeEventListener('click', handlePopupOutsideClick);

    // First add the 'hidden' class which starts the transition
    popup.classList.add('hidden');

    // After a short delay, add the 'fully-hidden' class to completely remove from DOM flow
    setTimeout(() => {
        popup.classList.add('fully-hidden');
        console.log('Popup fully hidden, edit counter:', editCounter);
    }, 300); // Delay matches the CSS transition time
}

// Handle form submission
async function handleTagFormSubmit(event) {
    event.preventDefault();
    console.log('Form submitted, edit counter:', editCounter);

    // Get form elements directly
    const filePathInput = document.getElementById('edit-file-path');
    const titleInput = document.getElementById('edit-title');
    const artistInput = document.getElementById('edit-artist');
    const tagsInput = document.getElementById('edit-tags');
    const saveBtn = document.getElementById('save-tags-btn');

    if (!filePathInput || !titleInput || !artistInput || !tagsInput || !saveBtn) {
        console.error('Form elements not found');
        return;
    }

    const filePath = filePathInput.value;
    const title = titleInput.value;
    const artist = artistInput.value;

    // Get tags and format them properly (comma-separated with spaces)
    let tags = tagsInput.value.trim();
    if (tags) {
        // Split by comma, trim each tag, and rejoin with comma + space
        tags = tags.split(',')
            .map(tag => tag.trim())
            .filter(tag => tag) // Remove empty tags
            .join(', ');
    }

    if (!filePath) {
        alert('File path is missing');
        return;
    }

    try {
        // Show loading state
        saveBtn.disabled = true;
        saveBtn.textContent = 'Saving...';

        // Call the main process to update the tags
        const result = await window.track.updateTags({
            filePath,
            title,
            artist,
            tags
        });

        if (result.success) {
            // Update the track in the allTracks array
            const trackIndex = allTracks.findIndex(track => track.path === filePath);
            if (trackIndex !== -1) {
                allTracks[trackIndex].title = title;
                allTracks[trackIndex].artist = artist;
                allTracks[trackIndex].tags = tags;

                // Re-render the track list
                const searchTerm = searchInput.value.trim();
                updateSearchResults(searchTerm);
            }

            // Show success message
            alert('Changes saved successfully!');

            // Close the popup after showing the message
            closeEditTagsPopup();
        } else {
            alert(`Error saving changes: ${result.error}`);
        }
    } catch (error) {
        console.error('Error updating tags:', error);
        alert(`Error saving changes: ${error.message}`);
    } finally {
        // Reset button state
        saveBtn.disabled = false;
        saveBtn.textContent = 'Save';
    }
}

// Funzione placeholder per la riproduzione (da implementare)
let currentAudio = null; // Variabile per tenere traccia dell'audio corrente

window.playTrack = (path) => {
    console.log('Playing track:', path);

    // Ferma la traccia precedente se ne sta suonando una
    if (currentAudio) {
        currentAudio.pause();
        currentAudio.currentTime = 0; // Resetta il tempo
        currentAudio = null;
    }

    // Convert the file path to a file:// URL and properly encode special characters
    // First replace backslashes with forward slashes
    let normalizedPath = path.replace(/\\/g, '/');

    // For network paths (e.g., //server/share/file), the initial // is important.
    // For local paths, they might start with / (Unix) or C:/ (Windows after normalization).

    let audioUrl;
    if (normalizedPath.startsWith('//')) { // Network path
        // Correctly handle UNC paths: file:////server/share/path
        // The path itself needs to be //server/share/path after file://
        // Each component of //server/share/path needs to be URI encoded.
        const pathParts = normalizedPath.split('/');
        // Ignoriamo le prime due parti (empty e initialSlash) e prendiamo il server e il resto
        const server = pathParts[2];
        const rest = pathParts.slice(3);
        const encodedServer = encodeURIComponent(server);
        const encodedRest = rest.map(segment => encodeURIComponent(segment)).join('/');
        audioUrl = `file:////${encodedServer}/${encodedRest}`;

    } else if (normalizedPath.startsWith('/')) { // Absolute Unix path or Windows path normalized (e.g. /C:/Users...)
        // For absolute paths like /path/to/file or /C:/Users/<USER>
        const encodedPath = normalizedPath.substring(1).split('/').map(segment => encodeURIComponent(segment)).join('/');
        audioUrl = `file:///${encodedPath}`; // Becomes file:///path/to/file or file:///C:/Users/<USER>
    }
    else { // Relative paths or Windows drive paths like C:/Users/<USER>
        // This case should ideally handle paths like "C:/Users/<USER>" correctly.
        // If path is "C:/Users/<USER>", normalizedPath is "C:/Users/<USER>"
        // We want file:///C:/Users/<USER>
        const encodedPath = normalizedPath.split('/').map(segment => encodeURIComponent(segment)).join('/');
        audioUrl = `file:///${encodedPath}`;
    }

    console.log('Using audio URL:', audioUrl);
    const audio = new Audio(audioUrl);
    currentAudio = audio; // Imposta come audio corrente

    audio.addEventListener('loadedmetadata', () => {
        // Una volta caricati i metadati (inclusa la durata)
        const midpoint = audio.duration / 2;
        audio.currentTime = midpoint; // Vai a metà canzone
        audio.play();
        console.log(`Playing from midpoint: ${midpoint.toFixed(2)}s`);

        // Ferma dopo 10 secondi
        setTimeout(() => {
            if (audio === currentAudio) { // Assicurati che sia ancora l'audio corrente
                audio.pause();
                console.log('Playback stopped after 10 seconds.');
                currentAudio = null; // Rimuovi riferimento all'audio fermato
            }
        }, 10000); // 10000 millisecondi = 10 secondi
    });

    audio.addEventListener('error', (e) => {
        console.error('Error playing track:', e);
        alert(`Error playing track: ${path}\n${e.message}`);
        if (audio === currentAudio) {
            currentAudio = null;
        }
    });

    audio.addEventListener('ended', () => {
        // Evento chiamato se la traccia finisce naturalmente (non dovrebbe accadere con il timeout)
        console.log('Track ended naturally.');
        if (audio === currentAudio) {
            currentAudio = null;
        }
    });

    // Inizia a caricare l'audio
    audio.load();
};

// Playlist Generator functionality

// Elements - Playlist Generator
const generatePlaylistBtn = document.getElementById('generate-playlist-btn');
const playlistGeneratorPopup = document.getElementById('playlist-generator-popup');
const closeGeneratorPopupBtn = document.getElementById('close-generator-popup-btn');
const playlistGeneratorForm = document.getElementById('playlist-generator-form');
const generatorPlaylistName = document.getElementById('generator-playlist-name');
const generatorDuration = document.getElementById('generator-duration');
const generatorPlaylists = document.getElementById('generator-playlists');
const generatorSchema = document.getElementById('generator-schema');
const cancelGeneratorBtn = document.getElementById('cancel-generator-btn');

// Elements - Tag-based Playlist Generator
const generateTagPlaylistBtn = document.getElementById('generate-tag-playlist-btn');
const tagPlaylistGeneratorPopup = document.getElementById('tag-playlist-generator-popup');
const closeTagGeneratorPopupBtn = document.getElementById('close-tag-generator-popup-btn');
const tagPlaylistGeneratorForm = document.getElementById('tag-playlist-generator-form');
const tagGeneratorPlaylistName = document.getElementById('tag-generator-playlist-name');
const tagGeneratorDuration = document.getElementById('tag-generator-duration');
const tagGeneratorSourcePlaylist = document.getElementById('tag-generator-source-playlist');
const tagGeneratorAvailableTags = document.getElementById('tag-generator-available-tags');

// Elements - Step Builder
const stepBuilderBtn = document.getElementById('step-builder-btn');
const stepBuilderPopup = document.getElementById('step-builder-popup');
const closeStepBuilderPopupBtn = document.getElementById('close-step-builder-popup-btn');
const stepBuilderPlaylistName = document.getElementById('step-builder-playlist-name');
const stepBuilderDuration = document.getElementById('step-builder-duration');
const stepBuilderSchema = document.getElementById('step-builder-schema');
const stepBuilderGeneration = document.getElementById('step-builder-generation');
const addStepSchemaEntryBtn = document.getElementById('add-step-schema-entry-btn');

// Step navigation elements
const step1NextBtn = document.getElementById('step-1-next');
const step2PrevBtn = document.getElementById('step-2-prev');
const step2NextBtn = document.getElementById('step-2-next');
const step3PrevBtn = document.getElementById('step-3-prev');
const step3FinishBtn = document.getElementById('step-3-finish');
const step3AddCycleBtn = document.getElementById('step-3-add-cycle');
const tagGeneratorSchema = document.getElementById('tag-generator-schema');
const addTagSchemaEntryBtn = document.getElementById('add-tag-schema-entry-btn');
const cancelTagGeneratorBtn = document.getElementById('cancel-tag-generator-btn');

// Swap Tracks popup elements
const swapTracksPopup = document.getElementById('swap-tracks-popup');
const closeSwapTracksBtn = document.getElementById('close-swap-tracks-btn');
const swapTracksForm = document.getElementById('swap-tracks-form');
const swapTrackPosition1 = document.getElementById('swap-track-position-1');
const swapTrackPosition2 = document.getElementById('swap-track-position-2');
const cancelSwapTracksBtn = document.getElementById('cancel-swap-tracks-btn');
const confirmSwapTracksBtn = document.getElementById('confirm-swap-tracks-btn');

// Rename Playlist popup elements
const renamePlaylistPopup = document.getElementById('rename-playlist-popup');
const closeRenamePlaylistBtn = document.getElementById('close-rename-playlist-btn');
const renamePlaylistForm = document.getElementById('rename-playlist-form');
const newPlaylistNameInput = document.getElementById('new-playlist-name');
const cancelRenamePlaylistBtn = document.getElementById('cancel-rename-playlist-btn');
const confirmRenamePlaylistBtn = document.getElementById('confirm-rename-playlist-btn');

// State variables
let selectedSourcePlaylists = [];

// Open the playlist generator popup
function openPlaylistGeneratorPopup() {
    // Reset form
    playlistGeneratorForm.reset();

    // Set default playlist name with current date
    const now = new Date();
    const day = now.getDate().toString().padStart(2, '0');
    const month = (now.getMonth() + 1).toString().padStart(2, '0');
    const year = now.getFullYear();
    generatorPlaylistName.value = `Playlist ${day}/${month}/${year}`;

    // Reset selected source playlists, filtering out any generated playlists
    if (selectedSourcePlaylists.length > 0) {
        // Keep only sources that reference manual playlists
        selectedSourcePlaylists = selectedSourcePlaylists.filter(source => {
            const playlist = allPlaylists.find(p => p.id === source.playlistId);
            return playlist && !playlist.is_generated;
        });
    }

    // Populate playlists
    renderGeneratorPlaylists();

    // Show popup
    playlistGeneratorPopup.classList.remove('hidden');
    playlistGeneratorPopup.classList.remove('fully-hidden');

    // Focus on playlist name input
    generatorPlaylistName.focus();
}

// Close the playlist generator popup
function closePlaylistGeneratorPopup() {
    playlistGeneratorPopup.classList.add('hidden');
    setTimeout(() => {
        playlistGeneratorPopup.classList.add('fully-hidden');
    }, 300);

    // Reset state
    selectedSourcePlaylists = [];
}

// Render available playlists in the generator
function renderGeneratorPlaylists() {
    if (!allPlaylists || allPlaylists.length === 0) {
        generatorPlaylists.innerHTML = `
            <div class="empty-state">
                Nessuna playlist disponibile. Crea prima delle playlist.
            </div>
        `;
        return;
    }

    // Filter to only show manually created playlists (not generated ones)
    const manualPlaylists = allPlaylists.filter(playlist => !playlist.is_generated);

    if (manualPlaylists.length === 0) {
        generatorPlaylists.innerHTML = `
            <div class="empty-state">
                Nessuna playlist manuale disponibile. Crea prima delle playlist manuali.
            </div>
        `;
        return;
    }

    const playlistItems = manualPlaylists.map(playlist => {
        // Check if this playlist is in the selected sources
        const isSelected = selectedSourcePlaylists.some(source => source.playlistId === playlist.id);
        const trackCount = isSelected ? selectedSourcePlaylists.find(source => source.playlistId === playlist.id).trackCount : 1;

        return `
            <div class="generator-playlist-item" data-playlist-id="${playlist.id}">
                <div class="generator-playlist-info">
                    <span class="generator-playlist-name">${playlist.name}</span>
                    <span class="generator-playlist-track-count">(${playlist.trackCount} brani)</span>
                </div>
                <div class="generator-playlist-controls">
                    <label class="generator-playlist-toggle">
                        <input type="checkbox" class="playlist-toggle-input" data-playlist-id="${playlist.id}" ${isSelected ? 'checked' : ''}>
                        <span class="slider"></span>
                    </label>
                    <div class="generator-playlist-count">
                        <input type="number" class="playlist-count-input" data-playlist-id="${playlist.id}"
                            min="1" max="${playlist.trackCount}" value="${trackCount}"
                            ${isSelected ? '' : 'disabled'}>
                        <span>brani</span>
                    </div>
                </div>
            </div>
        `;
    }).join('');

    generatorPlaylists.innerHTML = playlistItems;

    // Add event listeners to toggles and count inputs
    document.querySelectorAll('.playlist-toggle-input').forEach(toggle => {
        toggle.addEventListener('change', handlePlaylistToggle);
    });

    document.querySelectorAll('.playlist-count-input').forEach(input => {
        input.addEventListener('change', handlePlaylistCountChange);
    });

    // Update schema
    updateGeneratorSchema();
}

// Handle playlist toggle change
function handlePlaylistToggle(event) {
    const playlistId = parseInt(event.target.dataset.playlistId);
    const isChecked = event.target.checked;
    const countInput = document.querySelector(`.playlist-count-input[data-playlist-id="${playlistId}"]`);

    if (isChecked) {
        // Enable count input
        countInput.disabled = false;

        // Add to selected sources
        const playlist = allPlaylists.find(p => p.id === playlistId);
        const trackCount = parseInt(countInput.value) || 1;

        // Calculate position (order) based on existing sources
        const position = selectedSourcePlaylists.length > 0
            ? Math.max(...selectedSourcePlaylists.map(s => s.position || 0)) + 1
            : 1;

        selectedSourcePlaylists.push({
            playlistId,
            playlistName: playlist.name,
            trackCount,
            position
        });
    } else {
        // Disable count input
        countInput.disabled = true;

        // Remove from selected sources
        selectedSourcePlaylists = selectedSourcePlaylists.filter(source => source.playlistId !== playlistId);
    }

    // Update schema
    updateGeneratorSchema();
}

// Handle playlist count change
function handlePlaylistCountChange(event) {
    const playlistId = parseInt(event.target.dataset.playlistId);
    const trackCount = parseInt(event.target.value) || 1;

    // Update selected sources
    const sourceIndex = selectedSourcePlaylists.findIndex(source => source.playlistId === playlistId);
    if (sourceIndex !== -1) {
        selectedSourcePlaylists[sourceIndex].trackCount = trackCount;
    }

    // Update schema
    updateGeneratorSchema();
}

// Update the generator schema display
function updateGeneratorSchema() {
    if (selectedSourcePlaylists.length === 0) {
        generatorSchema.innerHTML = `
            <div class="empty-state">
                Seleziona le playlist sorgenti per visualizzare lo schema
            </div>
        `;
        return;
    }

    // Sort by position
    const sortedSources = [...selectedSourcePlaylists].sort((a, b) => a.position - b.position);

    const schemaHtml = sortedSources.map(source => {
        return `
            <div class="generator-schema-item">
                <span class="generator-schema-playlist-name">${source.playlistName}</span>
                <span class="generator-schema-track-count">${source.trackCount} brani</span>
            </div>
        `;
    }).join('');

    generatorSchema.innerHTML = schemaHtml;
}

// Add these variables at the top with other global variables
let allTags = [];
let activeTagFilters = [];
const tagFiltersContainer = document.getElementById('tag-filters-container');
const clearTagFiltersBtn = document.getElementById('clear-tag-filters');

// Function to load all unique tags from the database
async function loadAllTags() {
    try {
        console.log('Requesting all tags from database...');
        allTags = await window.track.getAllTags();
        console.log('Loaded all tags:', allTags);

        // Force a refresh of the tag filters UI
        renderTagFilters();

        // If we have active tag filters, reapply them to update the track list
        if (activeTagFilters.length > 0) {
            applyTagFilters();
        }

        return allTags; // Return the tags for potential chaining
    } catch (error) {
        console.error('Failed to load tags:', error);
        tagFiltersContainer.innerHTML = `<div class="empty-state error">Failed to load tags: ${error.message}</div>`;
        return []; // Return empty array on error
    }
}

// Function to render tag filters
function renderTagFilters() {
    if (!allTags || allTags.length === 0) {
        tagFiltersContainer.innerHTML = `<div class="empty-state">No tags available</div>`;
        return;
    }

    const tagFiltersHtml = allTags.map(tag => {
        // Check if this tag is in the active filters (case-insensitive)
        const isActive = activeTagFilters.some(activeTag =>
            activeTag.toLowerCase() === tag.name.toLowerCase()
        );

        return `
      <div class="tag-filter ${isActive ? 'active' : ''}" data-tag="${tag.name}">
        ${tag.name} <span class="tag-count">(${tag.count})</span>
      </div>
    `;
    }).join('');

    tagFiltersContainer.innerHTML = tagFiltersHtml;

    // Add event listeners to tag filters
    const tagFilters = document.querySelectorAll('.tag-filter');
    console.log('Found tag filters:', tagFilters.length);

    tagFilters.forEach(tagFilter => {
        // Aggiungi il listener direttamente
        tagFilter.onclick = function (event) {
            console.log('Tag clicked via onclick:', this.dataset.tag);
            handleTagFilterClick(event);
        };
        console.log('Added onclick handler to tag:', tagFilter.dataset.tag);
    });
}

// Function to handle tag filter click
function handleTagFilterClick(event) {
    console.log('Tag filter clicked', event);

    // Ottieni l'elemento tag-filter (potrebbe essere l'elemento stesso o un suo genitore)
    let tagFilter = event.target;
    if (!tagFilter.classList.contains('tag-filter')) {
        tagFilter = tagFilter.closest('.tag-filter');
    }

    if (!tagFilter) {
        console.error('Tag filter element not found');
        return;
    }

    const tagName = tagFilter.dataset.tag;
    console.log('Tag clicked:', tagName);

    // Toggle active state - ensure case-insensitive comparison
    const tagIndex = activeTagFilters.findIndex(tag => tag.toLowerCase() === tagName.toLowerCase());

    if (tagIndex !== -1) {
        // Tag is already active, remove it
        activeTagFilters.splice(tagIndex, 1);
        tagFilter.classList.remove('active');
        console.log('Tag removed from filters:', tagName);
    } else {
        // Tag is not active, add it
        activeTagFilters.push(tagName);
        tagFilter.classList.add('active');
        console.log('Tag added to filters:', tagName);
    }

    // Apply filters
    applyTagFilters();
}

// Function to apply tag filters
function applyTagFilters() {
    console.log('Applying tag filters:', activeTagFilters);

    // Utilizziamo updateSearchResults che ora usa filterTracks
    // filterTracks gestisce già il filtraggio per tag attivi
    updateSearchResults(searchInput.value.trim());
}

// Function to clear all tag filters
function clearTagFilters() {
    console.log('Clearing all tag filters');
    activeTagFilters = [];

    // Remove active class from all tag filters
    document.querySelectorAll('.tag-filter').forEach(tagFilter => {
        tagFilter.classList.remove('active');
    });

    // Update the UI and results
    renderTagFilters();
    updateSearchResults(searchInput.value.trim());
}

// Add event listener for clear tag filters button
clearTagFiltersBtn.addEventListener('click', clearTagFilters);

// Modify the updateSearchResults function to respect tag filters
const originalUpdateSearchResults = updateSearchResults;
updateSearchResults = function (searchTerm) {
    // filterTracks ora gestisce già il filtraggio per tag attivi
    let filteredTracks = filterTracks(searchTerm);

    // Update the results count
    if (searchTerm || activeTagFilters.length > 0) {
        resultsCountSpan.textContent = filteredTracks.length;
        searchResultsCount.classList.remove('hidden');
    } else {
        searchResultsCount.classList.add('hidden');
    }

    // Render the filtered tracks
    renderTrackList(filteredTracks, searchTerm);
};

// Load tags when library is loaded
const originalLoadLibrary = loadLibrary;
loadLibrary = async function () {
    await originalLoadLibrary();
    if (allTracks.length > 0) {
        loadAllTags();
    }
};

// We no longer need this event listener since we're loading tags directly in the main event handler
// The loadAllTags() function is now called directly after the scan completes

// Initialize tag filters on page load
document.addEventListener('DOMContentLoaded', () => {
    // Add to existing DOMContentLoaded event
    if (allTracks.length > 0) {
        loadAllTags();
    }
});

// Set up scan event listeners
window.library.onScanStarted((data) => {
    console.log('Scan started:', data);
});

window.library.onScanProgress((data) => {
    console.log('Scan progress:', data);
});

// Listen for scan completion and load tags immediately
window.library.onScanComplete(async (data) => {
    console.log('Scan complete:', data);

    // Load tags immediately after scan completes
    console.log('Loading tags after scan completion event...');
    await loadAllTags();
    console.log('Tags loaded and displayed after scan completion');
});

// Generate playlist based on the configuration
async function generatePlaylist() {
    if (selectedSourcePlaylists.length === 0) {
        alert('Seleziona almeno una playlist sorgente.');
        return;
    }

    const playlistName = generatorPlaylistName.value.trim();
    if (!playlistName) {
        alert('Inserisci un nome per la playlist.');
        generatorPlaylistName.focus();
        return;
    }

    const targetDuration = parseInt(generatorDuration.value) || 90;
    if (targetDuration <= 0) {
        alert('La durata deve essere maggiore di zero.');
        generatorDuration.focus();
        return;
    }

    try {
        // First create a generator configuration
        const generator = await window.playlistGenerator.create({
            name: `Generator for ${playlistName}`,
            targetDuration
        });

        // Add sources to the generator
        for (const source of selectedSourcePlaylists) {
            await window.playlistGenerator.addSource({
                generatorId: generator.id,
                playlistId: source.playlistId,
                trackCount: source.trackCount,
                position: source.position
            });
        }

        // Generate the playlist
        const result = await window.playlistGenerator.generate({
            generatorId: generator.id,
            playlistName
        });

        if (result.success) {
            // Crea un messaggio più dettagliato con informazioni sulla durata
            let successMessage = `Playlist "${playlistName}" generata con successo!\n`;
            successMessage += `${result.playlist.trackCount} brani aggiunti.\n`;

            // Aggiungi informazioni sulla durata se disponibili
            if (result.actualDurationMinutes !== undefined && result.actualDurationSeconds !== undefined) {
                successMessage += `Durata totale stimata: ${result.actualDurationMinutes} minuti e ${result.actualDurationSeconds} secondi.\n`;

                // Aggiungi informazioni sulla durata target
                if (result.targetDurationMinutes !== undefined && result.targetDurationSeconds !== undefined) {
                    successMessage += `Durata target richiesta: ${result.targetDurationMinutes} minuti e ${result.targetDurationSeconds} secondi.\n`;
                }
            }

            // Aggiungi informazioni sulle ripetizioni se ce ne sono più di una
            if (result.repetitions > 1) {
                successMessage += `(${result.repetitions} cicli di ripetizione per raggiungere la durata target)`;
            }

            alert(successMessage);

            // Close the popup
            closePlaylistGeneratorPopup();

            // Refresh playlists
            await loadPlaylists();

            // Open the new playlist
            openPlaylist(result.playlist.id);
        } else {
            alert(`Errore nella generazione della playlist: ${result.error}`);
        }
    } catch (error) {
        console.error('Error generating playlist:', error);
        alert(`Errore nella generazione della playlist: ${error.message}`);
    }
}

// Event listeners for playlist generator
generatePlaylistBtn.addEventListener('click', openPlaylistGeneratorPopup);
closeGeneratorPopupBtn.addEventListener('click', closePlaylistGeneratorPopup);
cancelGeneratorBtn.addEventListener('click', closePlaylistGeneratorPopup);

// Handle form submission
playlistGeneratorForm.addEventListener('submit', async (event) => {
    event.preventDefault();
    await generatePlaylist();
});

// Tag-based Playlist Generator functionality
let availableTags = [];
let tagSchemaEntries = [];
let tagSchemaEntryCounter = 0;

// Step Builder functionality
let stepBuilderCurrentStep = 1;
let stepBuilderSchemaEntries = [];
let stepBuilderSchemaEntryCounter = 0;
let stepBuilderCurrentPlaylist = null;
let stepBuilderGeneratedTracks = [];

// Function to generate a default name for tag-based playlists
function generateTagPlaylistDefaultName() {
    // Get current date and time in a compact format
    const now = new Date();
    const dateStr = now.toISOString().slice(0, 10); // YYYY-MM-DD
    const timeStr = now.toTimeString().slice(0, 5).replace(':', ''); // HHMM

    // Create a base name with date and time to ensure uniqueness
    return `Tag Mix ${dateStr} ${timeStr}`;
}

// Step Builder Functions
function generateStepBuilderDefaultName() {
    const now = new Date();
    const dateStr = now.toISOString().slice(0, 10); // YYYY-MM-DD
    const timeStr = now.toTimeString().slice(0, 5).replace(':', ''); // HHMM
    return `Step Mix ${dateStr} ${timeStr}`;
}

async function openStepBuilderPopup() {
    // Reset state
    stepBuilderCurrentStep = 1;
    stepBuilderSchemaEntries = [];
    stepBuilderSchemaEntryCounter = 0;
    stepBuilderCurrentPlaylist = null;
    stepBuilderGeneratedTracks = [];

    // Reset form
    stepBuilderPlaylistName.value = generateStepBuilderDefaultName();
    stepBuilderDuration.value = 90;
    stepBuilderSchema.innerHTML = '<div class="empty-state">Aggiungi combinazioni per costruire lo schema</div>';
    stepBuilderGeneration.innerHTML = '<div class="empty-state">Torna al passo precedente per configurare lo schema</div>';

    // Load available tags for the step builder
    try {
        console.log('Loading available tags for step builder...');
        availableTags = await window.track.getAllTags();
        console.log('Loaded available tags for step builder:', availableTags.length);
    } catch (error) {
        console.error('Failed to load tags for step builder:', error);
        alert(`Errore nel caricamento dei tag: ${error.message}`);
    }

    // Show step 1
    showStepBuilderStep(1);

    // Show popup
    stepBuilderPopup.classList.remove('hidden');
    stepBuilderPopup.classList.remove('fully-hidden');
    stepBuilderPlaylistName.focus();
}

function closeStepBuilderPopup() {
    stepBuilderPopup.classList.add('fully-hidden');
    setTimeout(() => {
        stepBuilderPopup.classList.add('hidden');
    }, 300);
}

function showStepBuilderStep(stepNumber) {
    stepBuilderCurrentStep = stepNumber;

    // Update step indicators
    document.querySelectorAll('.step-indicator').forEach((indicator, index) => {
        const step = index + 1;
        indicator.classList.remove('active', 'completed');

        if (step === stepNumber) {
            indicator.classList.add('active');
        } else if (step < stepNumber) {
            indicator.classList.add('completed');
        }
    });

    // Show/hide step content
    document.querySelectorAll('.step-content').forEach((content, index) => {
        const step = index + 1;
        content.classList.remove('active');
        if (step === stepNumber) {
            content.classList.add('active');
        }
    });
}

function addStepBuilderSchemaEntry() {
    console.log('Adding new step builder schema entry...');
    stepBuilderSchemaEntryCounter++;
    const entryId = `step-schema-entry-${stepBuilderSchemaEntryCounter}`;

    const entryHtml = `
        <div class="step-schema-entry" data-entry-id="${entryId}">
            <div class="step-schema-entry-header">
                <span class="step-schema-entry-title">Combinazione ${stepBuilderSchemaEntryCounter}</span>
                <button type="button" class="step-schema-entry-remove" onclick="removeStepBuilderSchemaEntry('${entryId}')">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" width="16" height="16">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                </button>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>Tag Principale:</label>
                    <select class="form-control primary-tag-select" data-entry-id="${entryId}" required>
                        <option value="">Seleziona tag principale</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Numero Tracce:</label>
                    <input type="number" class="form-control track-count-input" data-entry-id="${entryId}" min="1" value="1" required>
                </div>
            </div>
            <div class="form-group">
                <label>Tag Secondari (opzionali):</label>
                <div class="secondary-tags-container" data-entry-id="${entryId}">
                    <!-- Secondary tags will be populated dynamically -->
                </div>
            </div>
            <div class="form-group">
                <label>Playlist Sorgente (opzionale):</label>
                <select class="form-control source-playlist-select" data-entry-id="${entryId}">
                    <option value="">Tutte le tracce</option>
                </select>
            </div>
        </div>
    `;

    // Remove empty state if it exists
    if (stepBuilderSchema.querySelector('.empty-state')) {
        stepBuilderSchema.innerHTML = '';
    }

    stepBuilderSchema.insertAdjacentHTML('beforeend', entryHtml);
    console.log('Schema entry HTML added, now populating dropdowns...');

    // Populate dropdowns for this entry
    populateStepBuilderEntryDropdowns(entryId);

    // Add to entries array
    stepBuilderSchemaEntries.push({
        id: entryId,
        primaryTag: '',
        secondaryTags: [],
        trackCount: 1,
        sourcePlaylistId: null
    });

    console.log('Step builder schema entry added successfully:', entryId);
}

function removeStepBuilderSchemaEntry(entryId) {
    // Remove from DOM
    const entryElement = document.querySelector(`[data-entry-id="${entryId}"]`);
    if (entryElement) {
        entryElement.remove();
    }

    // Remove from array
    stepBuilderSchemaEntries = stepBuilderSchemaEntries.filter(entry => entry.id !== entryId);

    // Show empty state if no entries
    if (stepBuilderSchemaEntries.length === 0) {
        stepBuilderSchema.innerHTML = '<div class="empty-state">Aggiungi combinazioni per costruire lo schema</div>';
    }
}

async function populateStepBuilderEntryDropdowns(entryId) {
    try {
        console.log('Populating step builder entry dropdowns for:', entryId);

        // Load available tags using the correct API
        let tagsToUse = availableTags;
        if (!tagsToUse || tagsToUse.length === 0) {
            console.log('Loading tags from database...');
            tagsToUse = await window.track.getAllTags();
            availableTags = tagsToUse; // Cache for future use
            console.log('Loaded tags:', tagsToUse.length);
        }

        if (!tagsToUse || tagsToUse.length === 0) {
            console.warn('No tags available in database');
            return;
        }

        // Populate primary tag dropdown
        const primaryTagSelect = document.querySelector(`.primary-tag-select[data-entry-id="${entryId}"]`);
        if (primaryTagSelect) {
            console.log('Populating primary tag dropdown');
            primaryTagSelect.innerHTML = '<option value="">Seleziona tag principale</option>';
            tagsToUse.forEach(tag => {
                primaryTagSelect.innerHTML += `<option value="${tag.name}">${tag.name}</option>`;
            });

            // Add event listener for primary tag changes
            primaryTagSelect.addEventListener('change', () => updateStepBuilderEntry(entryId));
        } else {
            console.warn('Primary tag select not found for entry:', entryId);
        }

        // Populate secondary tags
        const secondaryTagsContainer = document.querySelector(`.secondary-tags-container[data-entry-id="${entryId}"]`);
        if (secondaryTagsContainer) {
            console.log('Populating secondary tags container');
            secondaryTagsContainer.innerHTML = tagsToUse.map(tag => `
                <label class="tag-checkbox-label">
                    <input type="checkbox" class="secondary-tag-checkbox" data-entry-id="${entryId}" value="${tag.name}">
                    <span class="tag-name">${tag.name}</span>
                </label>
            `).join('');

            // Add event listeners for secondary tag changes
            secondaryTagsContainer.querySelectorAll('.secondary-tag-checkbox').forEach(checkbox => {
                checkbox.addEventListener('change', () => updateStepBuilderEntry(entryId));
            });
        } else {
            console.warn('Secondary tags container not found for entry:', entryId);
        }

        // Populate source playlist dropdown
        const sourcePlaylistSelect = document.querySelector(`.source-playlist-select[data-entry-id="${entryId}"]`);
        if (sourcePlaylistSelect && allPlaylists) {
            console.log('Populating source playlist dropdown');
            sourcePlaylistSelect.innerHTML = '<option value="">Tutte le tracce</option>';
            allPlaylists.filter(p => !p.is_generated).forEach(playlist => {
                sourcePlaylistSelect.innerHTML += `<option value="${playlist.id}">${playlist.name}</option>`;
            });

            // Add event listener for source playlist changes
            sourcePlaylistSelect.addEventListener('change', () => updateStepBuilderEntry(entryId));
        } else {
            console.warn('Source playlist select not found or no playlists available for entry:', entryId);
        }

        // Add event listener for track count changes
        const trackCountInput = document.querySelector(`.track-count-input[data-entry-id="${entryId}"]`);
        if (trackCountInput) {
            trackCountInput.addEventListener('change', () => updateStepBuilderEntry(entryId));
        } else {
            console.warn('Track count input not found for entry:', entryId);
        }

        console.log('Successfully populated step builder entry dropdowns for:', entryId);

    } catch (error) {
        console.error('Failed to populate step builder entry dropdowns:', error);
        alert(`Errore nel caricamento dei tag: ${error.message}`);
    }
}

function updateStepBuilderEntry(entryId) {
    const entry = stepBuilderSchemaEntries.find(e => e.id === entryId);
    if (!entry) return;

    // Update primary tag
    const primaryTagSelect = document.querySelector(`.primary-tag-select[data-entry-id="${entryId}"]`);
    entry.primaryTag = primaryTagSelect ? primaryTagSelect.value : '';

    // Update secondary tags
    const secondaryTagCheckboxes = document.querySelectorAll(`.secondary-tag-checkbox[data-entry-id="${entryId}"]:checked`);
    entry.secondaryTags = Array.from(secondaryTagCheckboxes).map(cb => cb.value);

    // Update track count
    const trackCountInput = document.querySelector(`.track-count-input[data-entry-id="${entryId}"]`);
    entry.trackCount = trackCountInput ? parseInt(trackCountInput.value) || 1 : 1;

    // Update source playlist
    const sourcePlaylistSelect = document.querySelector(`.source-playlist-select[data-entry-id="${entryId}"]`);
    entry.sourcePlaylistId = sourcePlaylistSelect && sourcePlaylistSelect.value ? parseInt(sourcePlaylistSelect.value) : null;
}

function prepareStepBuilderGeneration() {
    if (stepBuilderSchemaEntries.length === 0) {
        stepBuilderGeneration.innerHTML = '<div class="empty-state">Nessuna combinazione configurata</div>';
        return;
    }

    // Create generation entries
    const generationHtml = stepBuilderSchemaEntries.map((entry, index) => {
        const primaryTag = entry.primaryTag || 'Nessun tag';
        const secondaryTags = entry.secondaryTags.length > 0 ? entry.secondaryTags.join(', ') : 'Nessuno';
        const sourcePlaylist = entry.sourcePlaylistId ?
            (allPlaylists.find(p => p.id === entry.sourcePlaylistId)?.name || 'Sconosciuta') :
            'Tutte le tracce';

        return `
            <div class="generation-entry" data-entry-id="${entry.id}">
                <div class="generation-entry-info">
                    <div class="generation-entry-title">Combinazione ${index + 1}: ${primaryTag}</div>
                    <div class="generation-entry-details">
                        Tag secondari: ${secondaryTags}<br>
                        Tracce: ${entry.trackCount} | Sorgente: ${sourcePlaylist}
                    </div>
                </div>
                <div class="generation-entry-actions">
                    <button type="button" class="btn primary compact generate-entry-btn" data-entry-id="${entry.id}">
                        Genera
                    </button>
                </div>
            </div>
        `;
    }).join('');

    stepBuilderGeneration.innerHTML = generationHtml;

    // Add event listeners
    stepBuilderGeneration.querySelectorAll('.generate-entry-btn').forEach(btn => {
        btn.addEventListener('click', () => generateStepBuilderEntry(btn.dataset.entryId));
    });


}

async function generateStepBuilderEntry(entryId) {
    const entry = stepBuilderSchemaEntries.find(e => e.id === entryId);
    if (!entry || !entry.primaryTag) {
        alert('Configurazione non valida per questa combinazione');
        return;
    }

    const generateBtn = document.querySelector(`.generate-entry-btn[data-entry-id="${entryId}"]`);
    const entryElement = document.querySelector(`.generation-entry[data-entry-id="${entryId}"]`);

    try {
        generateBtn.disabled = true;
        generateBtn.textContent = 'Generando...';

        // Create playlist if not exists
        if (!stepBuilderCurrentPlaylist) {
            const playlistName = stepBuilderPlaylistName.value.trim();
            if (!playlistName) {
                alert('Inserisci un nome per la playlist');
                return;
            }

            stepBuilderCurrentPlaylist = await window.playlist.create({ name: playlistName });
            if (!stepBuilderCurrentPlaylist) {
                throw new Error('Impossibile creare la playlist');
            }
        }

        // Generate tracks for this entry
        const result = await window.stepBuilder.generateEntry({
            playlistId: stepBuilderCurrentPlaylist.id,
            primaryTag: entry.primaryTag,
            secondaryTags: entry.secondaryTags,
            trackCount: entry.trackCount,
            sourcePlaylistId: entry.sourcePlaylistId,
            excludeTrackIds: stepBuilderGeneratedTracks.map(t => t.id)
        });

        if (result && result.success && result.tracks) {
            // Add generated tracks to our list
            stepBuilderGeneratedTracks.push(...result.tracks);

            // Update UI
            entryElement.classList.add('completed');
            generateBtn.style.display = 'none';

            // Update entry details with track information
            const detailsElement = entryElement.querySelector('.generation-entry-details');
            const tracksHtml = result.tracks.map(track => {
                const title = track.title || 'Titolo sconosciuto';
                const artist = track.artist && track.artist !== 'Unknown Artist' ? track.artist : '';
                const duration = track.duration ? formatDuration(track.duration) : '';

                return `
                    <div class="generated-track-item" data-track-id="${track.id}">
                        <div class="track-info">
                            <span class="track-title">${escapeHtml(title)}</span>
                            ${artist ? `<span class="track-artist"> - ${escapeHtml(artist)}</span>` : ''}
                            ${duration ? `<span class="track-duration"> (${duration})</span>` : ''}
                        </div>
                        <div class="track-actions">
                            <button type="button" class="btn secondary compact demo-btn" data-track-path="${escapeHtml(track.path)}" title="Ascolta demo 10s">
                                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <polygon points="5,3 19,12 5,21"></polygon>
                                </svg>
                            </button>
                            <button type="button" class="btn secondary compact replace-track-btn" data-track-id="${track.id}" data-playlist-id="${stepBuilderCurrentPlaylist.id}" title="Sostituisci traccia">
                                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"></path>
                                    <path d="M21 3v5h-5"></path>
                                    <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"></path>
                                    <path d="M3 21v-5h5"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                `;
            }).join('');

            detailsElement.innerHTML += `
                <br><strong>Generato: ${result.tracks.length} tracce</strong>
                <div class="generated-tracks-list">
                    ${tracksHtml}
                </div>
            `;

            // Add event listeners for demo and replace buttons
            const tracksList = detailsElement.querySelector('.generated-tracks-list');

            // Demo buttons
            tracksList.querySelectorAll('.demo-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    const trackPath = btn.dataset.trackPath;
                    console.log('Demo button clicked, trackPath:', trackPath);
                    if (trackPath && trackPath !== 'undefined') {
                        window.playTrack(trackPath);
                    } else {
                        console.error('Track path is undefined or invalid:', trackPath);
                        alert('Errore: percorso della traccia non valido');
                    }
                });
            });

            // Replace buttons
            tracksList.querySelectorAll('.replace-track-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    const trackId = parseInt(btn.dataset.trackId);
                    console.log('Replace button clicked, trackId:', trackId, 'stepBuilderCurrentPlaylist:', stepBuilderCurrentPlaylist);
                    if (trackId && stepBuilderCurrentPlaylist) {
                        // Set the current playlist for the replacement popup
                        currentPlaylist = stepBuilderCurrentPlaylist;
                        openTrackReplacementPopup(trackId);
                    } else {
                        console.error('Track ID or playlist missing:', { trackId, stepBuilderCurrentPlaylist });
                        alert('Errore: traccia o playlist non valida');
                    }
                });
            });

        } else {
            throw new Error(result?.error || 'Nessuna traccia trovata per questa combinazione');
        }

    } catch (error) {
        console.error('Failed to generate step builder entry:', error);
        alert(`Errore nella generazione: ${error.message}`);
    } finally {
        generateBtn.disabled = false;
        generateBtn.textContent = 'Genera';
    }
}

async function replaceStepBuilderEntry(entryId) {
    const entry = stepBuilderSchemaEntries.find(e => e.id === entryId);
    if (!entry || !stepBuilderCurrentPlaylist) {
        alert('Configurazione non valida');
        return;
    }

    const replaceBtn = document.querySelector(`.replace-entry-btn[data-entry-id="${entryId}"]`);

    try {
        replaceBtn.disabled = true;
        replaceBtn.textContent = 'Sostituendo...';

        // Find tracks generated by this entry and replace them
        const result = await window.stepBuilder.replaceEntry({
            playlistId: stepBuilderCurrentPlaylist.id,
            primaryTag: entry.primaryTag,
            secondaryTags: entry.secondaryTags,
            trackCount: entry.trackCount,
            sourcePlaylistId: entry.sourcePlaylistId,
            excludeTrackIds: stepBuilderGeneratedTracks.map(t => t.id)
        });

        if (result && result.success) {
            alert('Tracce sostituite con successo');
        } else {
            throw new Error(result?.error || 'Errore nella sostituzione');
        }

    } catch (error) {
        console.error('Failed to replace step builder entry:', error);
        alert(`Errore nella sostituzione: ${error.message}`);
    } finally {
        replaceBtn.disabled = false;
        replaceBtn.textContent = 'Sostituisci';
    }
}

async function finishStepBuilder() {
    if (!stepBuilderCurrentPlaylist) {
        alert('Nessuna playlist generata');
        return;
    }

    try {
        // Mark playlist as generated and save step builder configuration
        const stepBuilderConfig = {
            type: 'step-builder',
            entries: stepBuilderSchemaEntries.map(entry => ({
                primaryTag: entry.primaryTag,
                secondaryTags: entry.secondaryTags,
                trackCount: entry.trackCount,
                sourcePlaylistId: entry.sourcePlaylistId
            })),
            totalDuration: parseInt(stepBuilderDuration.value) || 90
        };

        await window.playlist.markAsGenerated({
            playlistId: stepBuilderCurrentPlaylist.id,
            generatorConfig: stepBuilderConfig
        });

        // Refresh playlists
        await loadPlaylists();

        // Close popup
        closeStepBuilderPopup();

        // Open the generated playlist
        await openPlaylist(stepBuilderCurrentPlaylist.id);

        alert(`Playlist "${stepBuilderCurrentPlaylist.name}" completata con ${stepBuilderGeneratedTracks.length} tracce!`);

    } catch (error) {
        console.error('Failed to finish step builder:', error);
        alert(`Errore nel completamento: ${error.message}`);
    }
}

function addStepBuilderCycle() {
    // Instead of resetting, duplicate the current cycle
    if (!stepBuilderCurrentPlaylist || stepBuilderSchemaEntries.length === 0) {
        alert('Nessun ciclo da duplicare');
        return;
    }

    // Create a copy of the current schema entries for the new cycle
    const currentEntries = [...stepBuilderSchemaEntries];

    // Generate tracks for the new cycle using the same schema
    generateStepBuilderCycle(currentEntries);
}

// Function to generate a new cycle with the same schema
async function generateStepBuilderCycle(entries) {
    if (!stepBuilderCurrentPlaylist) {
        alert('Nessuna playlist attiva');
        return;
    }

    try {
        // Show loading state
        step3AddCycleBtn.disabled = true;
        step3AddCycleBtn.textContent = 'Generando ciclo...';

        let totalTracksAdded = 0;

        // Generate tracks for each entry in the cycle
        for (const entry of entries) {
            console.log(`Generating cycle entry: ${entry.primaryTag} with ${entry.trackCount} tracks`);

            const result = await window.stepBuilder.generateEntry({
                playlistId: stepBuilderCurrentPlaylist.id,
                primaryTag: entry.primaryTag,
                secondaryTags: entry.secondaryTags,
                trackCount: entry.trackCount,
                sourcePlaylistId: entry.sourcePlaylistId,
                excludeTrackIds: stepBuilderGeneratedTracks.map(t => t.id)
            });

            if (result && result.success && result.tracks) {
                // Add the new tracks to our generated tracks list
                stepBuilderGeneratedTracks.push(...result.tracks);
                totalTracksAdded += result.tracks.length;

                console.log(`Added ${result.tracks.length} tracks for ${entry.primaryTag}`);
            } else {
                console.warn(`Failed to generate tracks for ${entry.primaryTag}:`, result?.error);
            }
        }

        // Update the UI to show the new cycle
        if (totalTracksAdded > 0) {
            // Add a visual separator for the new cycle
            const cycleNumber = Math.floor(stepBuilderGeneratedTracks.length / entries.length);
            stepBuilderGeneration.insertAdjacentHTML('beforeend', `
                <div class="cycle-separator">
                    <h4>Ciclo ${cycleNumber + 1} - ${totalTracksAdded} tracce aggiunte</h4>
                </div>
            `);

            alert(`Nuovo ciclo aggiunto con successo!\n${totalTracksAdded} tracce generate.`);
        } else {
            alert('Nessuna nuova traccia trovata per il nuovo ciclo.');
        }

    } catch (error) {
        console.error('Failed to generate new cycle:', error);
        alert(`Errore nella generazione del nuovo ciclo: ${error.message}`);
    } finally {
        step3AddCycleBtn.disabled = false;
        step3AddCycleBtn.textContent = 'Aggiungi un ciclo';
    }
}

// Function to update the playlist name based on selected tags
function updateTagPlaylistName() {
    // Only update if the user hasn't manually changed the name
    if (!tagGeneratorPlaylistName.dataset.userModified || tagGeneratorPlaylistName.dataset.userModified !== 'true') {
        // Get all primary tags from the schema entries
        const primaryTags = tagSchemaEntries
            .filter(entry => entry.primaryTag)
            .map(entry => entry.primaryTag);

        // If we have primary tags, use them in the name
        if (primaryTags.length > 0) {
            // Use up to 3 tags to keep the name reasonable
            const tagsToUse = primaryTags.slice(0, 3);
            const tagsPart = tagsToUse.join(', ');

            // Add "and more" if there are more than 3 tags
            const morePart = primaryTags.length > 3 ? ' e altri' : '';

            // Get current date in a compact format
            const now = new Date();
            const dateStr = now.toLocaleDateString('it-IT', {
                day: '2-digit',
                month: '2-digit',
                year: '2-digit'
            }).replace(/\//g, '-');

            // Create a name with tags and date
            tagGeneratorPlaylistName.value = `Mix [${tagsPart}${morePart}] ${dateStr}`;
        } else {
            // If no tags selected yet, use the default name
            tagGeneratorPlaylistName.value = generateTagPlaylistDefaultName();
        }
    }
}

// Function to populate source playlist dropdown
function populateTagGeneratorSourcePlaylists() {
    // Clear existing options except the default "None" option
    tagGeneratorSourcePlaylist.innerHTML = '<option value="">None - Search all tracks</option>';

    if (!allPlaylists || allPlaylists.length === 0) {
        return;
    }

    // Filter to only show manually created playlists (not generated ones)
    const manualPlaylists = allPlaylists.filter(playlist => !playlist.is_generated);

    if (manualPlaylists.length === 0) {
        return;
    }

    // Add manual playlists as options
    manualPlaylists.forEach(playlist => {
        const option = document.createElement('option');
        option.value = playlist.id;
        option.textContent = `${playlist.name} (${playlist.trackCount || 0} tracks)`;
        tagGeneratorSourcePlaylist.appendChild(option);
    });
}

// Function to open the tag-based playlist generator popup
function openTagPlaylistGeneratorPopup() {
    // Reset form
    tagPlaylistGeneratorForm.reset();
    tagGeneratorSchema.innerHTML = '<div class="empty-state">Aggiungi combinazioni di tag per generare la playlist</div>';
    tagSchemaEntries = [];
    tagSchemaEntryCounter = 0;

    // Reset the user modified flag
    tagGeneratorPlaylistName.dataset.userModified = 'false';

    // Set a default name
    tagGeneratorPlaylistName.value = generateTagPlaylistDefaultName();

    // Load available tags (but don't render them in the UI since we've hidden that section)
    loadAvailableTagsForGenerator();

    // Populate source playlist dropdown
    populateTagGeneratorSourcePlaylists();

    // Show popup
    tagPlaylistGeneratorPopup.classList.remove('hidden');
    setTimeout(() => {
        tagPlaylistGeneratorPopup.classList.remove('fully-hidden');
    }, 10);

    // Add event listener for name field to track user modifications
    tagGeneratorPlaylistName.addEventListener('input', function () {
        this.dataset.userModified = 'true';
    });
}

// Function to close the tag-based playlist generator popup
function closeTagPlaylistGeneratorPopup() {
    tagPlaylistGeneratorPopup.classList.add('fully-hidden');
    setTimeout(() => {
        tagPlaylistGeneratorPopup.classList.add('hidden');
    }, 300);
}

// Function to load available tags for the generator
async function loadAvailableTagsForGenerator() {
    try {
        availableTags = await window.track.getAllTags();
        // We don't need to render the available tags anymore since that section is hidden
        // But we still need to load them for the schema entries
        console.log('Loaded available tags for generator:', availableTags.length);
    } catch (error) {
        console.error('Failed to load tags for generator:', error);
        // Don't try to update the UI since the section is hidden
    }
}

// Function to render available tags in the generator
// This function is kept for compatibility but doesn't do anything visible anymore
// since the Available Tags section is commented out in the HTML
function renderAvailableTagsForGenerator() {
    // This function is now a no-op since the Available Tags section is hidden
    // But we keep it for compatibility with existing code
    console.log('renderAvailableTagsForGenerator called, but section is hidden');

    // We still need to check if tags are available
    if (!availableTags || availableTags.length === 0) {
        console.log('No tags available for generator');
        return;
    }

    console.log(`${availableTags.length} tags available for generator`);
}

// Function to add a new schema entry
function addTagSchemaEntry() {
    // Remove empty state if present
    const emptyState = tagGeneratorSchema.querySelector('.empty-state');
    if (emptyState) {
        tagGeneratorSchema.removeChild(emptyState);
    }

    const entryId = ++tagSchemaEntryCounter;
    const entry = {
        id: entryId,
        primaryTag: '',
        secondaryTags: [],
        trackCount: 1
    };

    tagSchemaEntries.push(entry);

    const entryHtml = `
        <div class="tag-schema-entry" data-entry-id="${entryId}">
            <div class="tag-schema-entry-header">
                <div class="tag-schema-entry-title">Combinazione #${entryId}</div>
                <button type="button" class="tag-schema-remove-btn" data-entry-id="${entryId}">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                </button>
            </div>

            <div class="tag-schema-primary-tag">
                <div class="tag-schema-primary-tag-label">Tag Principale (obbligatorio):</div>
                <select class="tag-schema-primary-tag-select" data-entry-id="${entryId}" required>
                    <option value="">Seleziona un tag principale</option>
                    ${availableTags.map(tag => `<option value="${tag.name}">${tag.name}</option>`).join('')}
                </select>
            </div>

            <div class="tag-schema-secondary-tags">
                <div class="tag-schema-secondary-tags-label">
                    Tag Secondari (opzionali):
                </div>
                <div class="tag-schema-secondary-tags-container" data-entry-id="${entryId}">
                    <!-- Secondary tags will be selected here -->
                    <div class="empty-state">Seleziona dei tag secondari (opzionale)</div>
                </div>
            </div>

            <div class="tag-schema-track-count">
                <div class="tag-schema-track-count-label">Numero di tracce:</div>
                <input type="number" class="tag-schema-track-count-input" data-entry-id="${entryId}" min="1" value="1" required>
            </div>
        </div>
    `;

    tagGeneratorSchema.insertAdjacentHTML('beforeend', entryHtml);

    // Add event listeners for the new entry
    const primaryTagSelect = document.querySelector(`.tag-schema-primary-tag-select[data-entry-id="${entryId}"]`);
    primaryTagSelect.addEventListener('change', (e) => {
        const selectedTag = e.target.value;
        const entryIndex = tagSchemaEntries.findIndex(entry => entry.id === entryId);
        if (entryIndex !== -1) {
            tagSchemaEntries[entryIndex].primaryTag = selectedTag;
            updateSecondaryTagsContainer(entryId);

            // Update the playlist name based on the selected tags
            updateTagPlaylistName();
        }
    });

    const trackCountInput = document.querySelector(`.tag-schema-track-count-input[data-entry-id="${entryId}"]`);
    trackCountInput.addEventListener('change', (e) => {
        const count = parseInt(e.target.value) || 1;
        const entryIndex = tagSchemaEntries.findIndex(entry => entry.id === entryId);
        if (entryIndex !== -1) {
            tagSchemaEntries[entryIndex].trackCount = count;
        }
    });

    const removeBtn = document.querySelector(`.tag-schema-remove-btn[data-entry-id="${entryId}"]`);
    removeBtn.addEventListener('click', () => {
        removeTagSchemaEntry(entryId);
    });

    // Initialize secondary tags container
    updateSecondaryTagsContainer(entryId);
}

// Function to update the secondary tags container for an entry
function updateSecondaryTagsContainer(entryId) {
    const container = document.querySelector(`.tag-schema-secondary-tags-container[data-entry-id="${entryId}"]`);
    const entryIndex = tagSchemaEntries.findIndex(entry => entry.id === entryId);
    if (entryIndex === -1 || !container) return;

    const entry = tagSchemaEntries[entryIndex];
    const primaryTag = entry.primaryTag;

    // Clear container
    container.innerHTML = '';

    // If no primary tag is selected, show empty state
    if (!primaryTag) {
        container.innerHTML = '<div class="empty-state">Seleziona prima un tag principale</div>';
        return;
    }

    // Filter out the primary tag from available tags (case-insensitive)
    const secondaryTagOptions = availableTags.filter(tag =>
        tag.name.toLowerCase() !== primaryTag.toLowerCase()
    );

    if (secondaryTagOptions.length === 0) {
        container.innerHTML = '<div class="empty-state">Nessun tag secondario disponibile</div>';
        return;
    }

    // Add secondary tag options
    secondaryTagOptions.forEach(tag => {
        // Case-insensitive check for selected tags
        const isSelected = entry.secondaryTags.some(selectedTag =>
            selectedTag.toLowerCase() === tag.name.toLowerCase()
        );

        const tagElement = document.createElement('div');
        tagElement.className = `tag-option ${isSelected ? 'secondary-selected' : ''}`;
        tagElement.dataset.tag = tag.name;
        tagElement.innerHTML = `${tag.name} <span class="tag-count">(${tag.count})</span>`;

        tagElement.addEventListener('click', () => {
            const tagName = tag.name;
            const entryIndex = tagSchemaEntries.findIndex(entry => entry.id === entryId);
            if (entryIndex !== -1) {
                const entry = tagSchemaEntries[entryIndex];
                // Case-insensitive check for tag index
                const tagIndex = entry.secondaryTags.findIndex(selectedTag =>
                    selectedTag.toLowerCase() === tagName.toLowerCase()
                );

                if (tagIndex === -1) {
                    // Add tag
                    entry.secondaryTags.push(tagName);
                    tagElement.classList.add('secondary-selected');
                } else {
                    // Remove tag
                    entry.secondaryTags.splice(tagIndex, 1);
                    tagElement.classList.remove('secondary-selected');
                }
            }
        });

        container.appendChild(tagElement);
    });
}

// Function to remove a schema entry
function removeTagSchemaEntry(entryId) {
    const entryElement = document.querySelector(`.tag-schema-entry[data-entry-id="${entryId}"]`);
    if (entryElement) {
        entryElement.remove();
    }

    const entryIndex = tagSchemaEntries.findIndex(entry => entry.id === entryId);
    if (entryIndex !== -1) {
        tagSchemaEntries.splice(entryIndex, 1);

        // Update the playlist name based on the remaining tags
        updateTagPlaylistName();
    }

    // If no entries left, show empty state
    if (tagSchemaEntries.length === 0) {
        tagGeneratorSchema.innerHTML = '<div class="empty-state">Aggiungi combinazioni di tag per generare la playlist</div>';

        // Reset to default name if no entries left
        if (!tagGeneratorPlaylistName.dataset.userModified || tagGeneratorPlaylistName.dataset.userModified !== 'true') {
            tagGeneratorPlaylistName.value = generateTagPlaylistDefaultName();
        }
    }
}

// Function to generate a tag-based playlist
async function generateTagPlaylist() {
    if (tagSchemaEntries.length === 0) {
        alert('Aggiungi almeno una combinazione di tag.');
        return;
    }

    // Validate entries
    for (const entry of tagSchemaEntries) {
        if (!entry.primaryTag) {
            alert('Seleziona un tag principale per ogni combinazione.');
            return;
        }
        if (entry.trackCount < 1) {
            alert('Il numero di tracce deve essere maggiore di zero.');
            return;
        }
    }

    const playlistName = tagGeneratorPlaylistName.value.trim();
    if (!playlistName) {
        alert('Inserisci un nome per la playlist.');
        tagGeneratorPlaylistName.focus();
        return;
    }

    const targetDuration = parseInt(tagGeneratorDuration.value) || 90;
    if (targetDuration <= 0) {
        alert('La durata deve essere maggiore di zero.');
        tagGeneratorDuration.focus();
        return;
    }

    // Get the selected source playlist ID (if any)
    const sourcePlaylistId = tagGeneratorSourcePlaylist.value ? parseInt(tagGeneratorSourcePlaylist.value) : null;

    try {
        // First create a tag generator configuration
        const generator = await window.tagPlaylistGenerator.create({
            name: `Tag Generator for ${playlistName}`,
            targetDuration
        });

        // Add sources to the generator
        for (let i = 0; i < tagSchemaEntries.length; i++) {
            const source = tagSchemaEntries[i];
            await window.tagPlaylistGenerator.addSource({
                generatorId: generator.id,
                primaryTag: source.primaryTag,
                secondaryTags: source.secondaryTags,
                trackCount: source.trackCount,
                position: i + 1
            });
        }

        // Generate the playlist
        const result = await window.tagPlaylistGenerator.generate({
            generatorId: generator.id,
            playlistName,
            sourcePlaylistId
        });

        if (result.success) {
            alert(`Playlist "${playlistName}" generata con successo!\n\nDurata: ${result.actualDurationMinutes}m ${result.actualDurationSeconds}s\nTracce: ${result.playlist.trackCount}`);
            closeTagPlaylistGeneratorPopup();
            loadPlaylists();
        } else {
            alert(`Errore durante la generazione della playlist: ${result.error}`);
        }
    } catch (error) {
        console.error('Error generating tag-based playlist:', error);
        alert(`Errore durante la generazione della playlist: ${error.message}`);
    }
}

// Event listeners for tag-based playlist generator
closeTagGeneratorPopupBtn.addEventListener('click', closeTagPlaylistGeneratorPopup);
cancelTagGeneratorBtn.addEventListener('click', closeTagPlaylistGeneratorPopup);
addTagSchemaEntryBtn.addEventListener('click', addTagSchemaEntry);

// Handle tag-based generator form submission
tagPlaylistGeneratorForm.addEventListener('submit', async (event) => {
    event.preventDefault();
    await generateTagPlaylist();
});

// Event listeners for step builder (main ones are set up in the initialization)
closeStepBuilderPopupBtn.addEventListener('click', closeStepBuilderPopup);
addStepSchemaEntryBtn.addEventListener('click', addStepBuilderSchemaEntry);

// Step navigation event listeners
step1NextBtn.addEventListener('click', () => {
    const playlistName = stepBuilderPlaylistName.value.trim();
    if (!playlistName) {
        alert('Inserisci un nome per la playlist');
        stepBuilderPlaylistName.focus();
        return;
    }
    showStepBuilderStep(2);
});

step2PrevBtn.addEventListener('click', () => showStepBuilderStep(1));

step2NextBtn.addEventListener('click', () => {
    if (stepBuilderSchemaEntries.length === 0) {
        alert('Aggiungi almeno una combinazione di tag');
        return;
    }

    // Validate entries
    for (const entry of stepBuilderSchemaEntries) {
        if (!entry.primaryTag) {
            alert('Seleziona un tag principale per ogni combinazione');
            return;
        }
        if (entry.trackCount < 1) {
            alert('Il numero di tracce deve essere maggiore di zero');
            return;
        }
    }

    prepareStepBuilderGeneration();
    showStepBuilderStep(3);
});

step3PrevBtn.addEventListener('click', () => showStepBuilderStep(2));
step3FinishBtn.addEventListener('click', finishStepBuilder);
step3AddCycleBtn.addEventListener('click', addStepBuilderCycle);

// Function to replace a track with a random one from the same source playlist
// This is now handled by the main process through the IPC handler 'playlist:replace-track'

// Track replacement popup variables and functions
const trackReplacementPopup = document.getElementById('track-replacement-popup');
const closeTrackReplacementBtn = document.getElementById('close-track-replacement-btn');
const cancelTrackReplacementBtn = document.getElementById('cancel-track-replacement-btn');
let currentTrackForReplacement = null;
let selectedTagsForReplacement = [];

// Manual track selection variables
let selectedTrackForReplacement = null;
let manualSearchResults = [];
let manualSearchInput = null;
let manualSearchIncludePath = null;
let manualSearchResultsContainer = null;
let selectedTrackInfoContainer = null;
let selectManualBtn = null;
let replaceManualBtn = null;

// Function to open the track replacement popup
function openTrackReplacementPopup(trackId) {
    console.log('Opening track replacement popup for track:', trackId);
    currentTrackForReplacement = trackId;
    selectedTagsForReplacement = [];

    // Reset the custom tag selection
    const customTagSelection = document.querySelector('.custom-tag-selection');
    if (customTagSelection) {
        customTagSelection.classList.add('hidden');
    } else {
        console.error('customTagSelection not found in openTrackReplacementPopup');
    }

    const selectTagsBtn = document.querySelector('.select-tags-btn');
    if (selectTagsBtn) {
        selectTagsBtn.classList.remove('hidden');
        console.log('Select tags button found and shown');
    } else {
        console.error('selectTagsBtn not found in openTrackReplacementPopup');
    }

    const replaceCustomBtn = document.querySelector('.replace-custom-btn');
    if (replaceCustomBtn) {
        replaceCustomBtn.classList.add('hidden');
    } else {
        console.error('replaceCustomBtn not found in openTrackReplacementPopup');
    }

    // Reset all replace buttons to their default state
    const buttons = document.querySelectorAll('.replace-btn, .replace-custom-btn, .replace-manual-btn');
    buttons.forEach(button => {
        button.disabled = false;
        if (button.classList.contains('replace-manual-btn')) {
            button.innerHTML = 'Replace with Selected Track';
            button.disabled = true; // Disabled until a track is selected
        } else {
            button.innerHTML = 'Replace';
        }
    });

    // Reset the available tags container
    const availableTagsContainer = document.getElementById('available-tags-for-replacement');
    if (availableTagsContainer) {
        availableTagsContainer.innerHTML = '<div class="loading">Loading tags...</div>';
    } else {
        console.error('availableTagsContainer not found');
    }

    // Reset manual track selection
    resetManualTrackSelection();

    // Show the popup
    if (trackReplacementPopup) {
        trackReplacementPopup.classList.remove('hidden');
        trackReplacementPopup.classList.remove('fully-hidden');
        console.log('Track replacement popup shown');
    } else {
        console.error('trackReplacementPopup not found');
        return;
    }

    // Center the popup in the viewport
    const popupContainer = trackReplacementPopup.querySelector('.popup-container');
    if (popupContainer) {
        popupContainer.style.transform = 'scale(1)';
        popupContainer.style.opacity = '1';
    }

    // Ensure event listeners are set up (fallback in case they weren't set up at page load)
    setupTrackReplacementPopupEventListeners();

    // Load all available tags for the custom tag selection
    loadTrackTagsForReplacement();
}

// Function to close the track replacement popup
function closeTrackReplacementPopup() {
    // Add the hidden class to start the fade-out animation
    trackReplacementPopup.classList.add('hidden');

    // Reset all replace buttons to their default state
    const buttons = document.querySelectorAll('.replace-btn, .replace-custom-btn, .replace-manual-btn');
    buttons.forEach(button => {
        button.disabled = false;
        if (button.classList.contains('replace-manual-btn')) {
            button.innerHTML = 'Replace with Selected Track';
            button.disabled = true;
        } else {
            button.innerHTML = 'Replace';
        }
    });

    // After the animation completes, fully hide the popup
    setTimeout(() => {
        trackReplacementPopup.classList.add('fully-hidden');

        // Reset the popup state
        currentTrackForReplacement = null;
        selectedTagsForReplacement = [];

        // Reset the custom tag selection
        const customTagSelection = document.querySelector('.custom-tag-selection');
        if (customTagSelection) {
            customTagSelection.classList.add('hidden');
        }

        const selectTagsBtn = document.querySelector('.select-tags-btn');
        if (selectTagsBtn) {
            selectTagsBtn.classList.remove('hidden');
        }

        const replaceCustomBtn = document.querySelector('.replace-custom-btn');
        if (replaceCustomBtn) {
            replaceCustomBtn.classList.add('hidden');
        }

        // Clear the available tags container
        const availableTagsContainer = document.getElementById('available-tags-for-replacement');
        if (availableTagsContainer) {
            availableTagsContainer.innerHTML = '';
        }

        // Reset manual track selection
        resetManualTrackSelection();
    }, 300);
}

// Function to load all available tags for the custom tag selection
async function loadTrackTagsForReplacement() {
    try {
        console.log('Loading all available tags for replacement...');

        // Get all available tags from the database
        const allAvailableTags = await window.track.getAllTags();
        if (!allAvailableTags || allAvailableTags.length === 0) {
            console.log('No tags available in the database');

            // Get the available tags container and show empty state
            const availableTagsContainer = document.getElementById('available-tags-for-replacement');
            if (availableTagsContainer) {
                availableTagsContainer.innerHTML = '<div class="empty-state">No tags available in the database</div>';
            }
            return;
        }

        console.log('Available tags for replacement:', allAvailableTags.length);

        // Get the available tags container
        const availableTagsContainer = document.getElementById('available-tags-for-replacement');
        if (!availableTagsContainer) {
            console.error('Available tags container not found');
            return;
        }

        // Clear the container
        availableTagsContainer.innerHTML = '';

        // Create a tag element for each available tag
        allAvailableTags.forEach(tagData => {
            const tag = tagData.name; // Extract tag name from the tag data object

            const tagElement = document.createElement('div');
            tagElement.className = 'replacement-tag';
            tagElement.dataset.tag = tag;
            tagElement.textContent = tag;

            // Add click event to toggle selection
            tagElement.addEventListener('click', () => {
                tagElement.classList.toggle('selected');

                // Update the selected tags array
                if (tagElement.classList.contains('selected')) {
                    if (!selectedTagsForReplacement.includes(tag)) {
                        selectedTagsForReplacement.push(tag);
                    }
                } else {
                    const index = selectedTagsForReplacement.indexOf(tag);
                    if (index !== -1) {
                        selectedTagsForReplacement.splice(index, 1);
                    }
                }

                console.log('Selected tags for replacement:', selectedTagsForReplacement);
            });

            availableTagsContainer.appendChild(tagElement);
        });

        console.log('Loaded all available tags for replacement');
    } catch (error) {
        console.error('Failed to load available tags for replacement:', error);

        // Show error state in the container
        const availableTagsContainer = document.getElementById('available-tags-for-replacement');
        if (availableTagsContainer) {
            availableTagsContainer.innerHTML = '<div class="empty-state error">Failed to load tags</div>';
        }
    }
}

// Function to replace a track with a random track that matches the specified tags
async function replaceTrackByTags(matchMode) {
    if (!currentTrackForReplacement || !currentPlaylist) {
        console.error('No track or playlist selected for replacement');
        alert('No track or playlist selected for replacement');
        return;
    }

    try {
        // Show loading state
        const buttons = document.querySelectorAll('.replace-btn, .replace-custom-btn');
        buttons.forEach(button => {
            button.disabled = true;
            button.innerHTML = `
                <svg class="icon spin" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <line x1="12" y1="2" x2="12" y2="6"></line>
                    <line x1="12" y1="18" x2="12" y2="22"></line>
                    <line x1="4.93" y1="4.93" x2="7.76" y2="7.76"></line>
                    <line x1="16.24" y1="16.24" x2="19.07" y2="19.07"></line>
                    <line x1="2" y1="12" x2="6" y2="12"></line>
                    <line x1="18" y1="12" x2="22" y2="12"></line>
                    <line x1="4.93" y1="19.07" x2="7.76" y2="16.24"></line>
                    <line x1="16.24" y1="7.76" x2="19.07" y2="4.93"></line>
                </svg>
            `;
        });

        // Call the IPC handler to replace the track
        const result = await window.playlist.replaceTrackByTags({
            playlistId: currentPlaylist.id,
            trackId: currentTrackForReplacement,
            matchMode: matchMode,
            selectedTags: matchMode === 'custom' ? selectedTagsForReplacement : null
        });

        if (result && result.success) {
            // Close the popup
            closeTrackReplacementPopup();

            // Refresh the playlist view
            await openPlaylist(currentPlaylist.id);
        } else {
            const errorMessage = result && result.error ? result.error : 'Unknown error occurred';
            console.error('Failed to replace track:', errorMessage);
            alert(`Failed to replace track: ${errorMessage}`);

            // Reset button state
            buttons.forEach(button => {
                button.disabled = false;
                button.innerHTML = 'Replace';
            });
        }
    } catch (error) {
        console.error('Error replacing track by tags:', error);
        alert(`Error replacing track: ${error.message || 'Unknown error occurred'}`);

        // Reset button state
        const buttons = document.querySelectorAll('.replace-btn, .replace-custom-btn');
        buttons.forEach(button => {
            button.disabled = false;
            button.innerHTML = 'Replace';
        });
    }
}

// Function to reset manual track selection
function resetManualTrackSelection() {
    selectedTrackForReplacement = null;
    manualSearchResults = [];

    // Get DOM elements
    manualSearchInput = document.getElementById('manual-search-input');
    manualSearchIncludePath = document.getElementById('manual-search-include-path');
    manualSearchResultsContainer = document.getElementById('manual-search-results');
    selectedTrackInfoContainer = document.getElementById('selected-track-info');
    selectManualBtn = document.querySelector('.select-manual-btn');
    replaceManualBtn = document.querySelector('.replace-manual-btn');

    // Reset input values
    if (manualSearchInput) {
        manualSearchInput.value = '';
    }

    if (manualSearchIncludePath) {
        manualSearchIncludePath.checked = false;
    }

    // Reset results container
    if (manualSearchResultsContainer) {
        manualSearchResultsContainer.innerHTML = '<div class="empty-state">Start typing to search for tracks...</div>';
    }

    // Hide selected track info
    if (selectedTrackInfoContainer) {
        selectedTrackInfoContainer.classList.add('hidden');
    }

    // Reset button states - show "Select Manually" button and hide "Replace" button
    if (selectManualBtn) {
        selectManualBtn.classList.remove('hidden');
    }

    if (replaceManualBtn) {
        replaceManualBtn.disabled = true;
        replaceManualBtn.classList.add('hidden');
    }

    // Hide manual selection interface
    const manualTrackSelection = document.querySelector('.manual-track-selection');
    if (manualTrackSelection) {
        manualTrackSelection.classList.add('hidden');
    }
}

// Function to initialize manual track selection
function initializeManualTrackSelection() {
    // Get DOM elements
    manualSearchInput = document.getElementById('manual-search-input');
    manualSearchIncludePath = document.getElementById('manual-search-include-path');
    manualSearchResultsContainer = document.getElementById('manual-search-results');
    selectedTrackInfoContainer = document.getElementById('selected-track-info');
    selectManualBtn = document.querySelector('.select-manual-btn');
    replaceManualBtn = document.querySelector('.replace-manual-btn');

    // Add event listeners
    if (manualSearchInput) {
        manualSearchInput.addEventListener('input', handleManualSearch);
    }

    if (manualSearchIncludePath) {
        manualSearchIncludePath.addEventListener('change', handleManualSearch);
    }

    if (selectManualBtn) {
        selectManualBtn.addEventListener('click', showManualTrackSelection);
    }

    if (replaceManualBtn) {
        replaceManualBtn.addEventListener('click', handleManualTrackReplacement);
    }
}

// Function to show manual track selection interface
function showManualTrackSelection() {
    const manualTrackSelection = document.querySelector('.manual-track-selection');
    if (manualTrackSelection) {
        manualTrackSelection.classList.remove('hidden');
    }

    if (selectManualBtn) {
        selectManualBtn.classList.add('hidden');
    }

    if (replaceManualBtn) {
        replaceManualBtn.classList.remove('hidden');
    }

    // Focus on search input
    if (manualSearchInput) {
        manualSearchInput.focus();
    }
}

// Function to handle manual search
async function handleManualSearch() {
    if (!manualSearchInput || !manualSearchResultsContainer) return;

    const searchTerm = manualSearchInput.value.trim();

    if (!searchTerm) {
        manualSearchResultsContainer.innerHTML = '<div class="empty-state">Start typing to search for tracks...</div>';
        manualSearchResults = [];
        return;
    }

    try {
        // Show loading state
        manualSearchResultsContainer.innerHTML = '<div class="empty-state">Searching...</div>';

        // Get all tracks from database
        const allTracks = await window.library.getTracks();

        if (!allTracks || allTracks.length === 0) {
            manualSearchResultsContainer.innerHTML = '<div class="empty-state">No tracks found in library</div>';
            return;
        }

        // Filter tracks using the same logic as the main search
        const includePathInSearch = manualSearchIncludePath ? manualSearchIncludePath.checked : false;
        const filteredTracks = filterTracksForManualSearch(allTracks, searchTerm, includePathInSearch);

        // Limit results to 50 tracks for performance
        manualSearchResults = filteredTracks.slice(0, 50);

        // Render results
        renderManualSearchResults();

    } catch (error) {
        console.error('Error searching tracks:', error);
        manualSearchResultsContainer.innerHTML = '<div class="empty-state error">Error searching tracks</div>';
    }
}

// Utility function to escape HTML special characters
function escapeHtml(text) {
    if (typeof text !== 'string') {
        return text;
    }

    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// Function to filter tracks for manual search (reuses main search logic with accent-insensitive normalization)
function filterTracksForManualSearch(tracks, searchTerm, includePathInSearch) {
    const normalizedTerm = normalizeText(searchTerm);

    return tracks.filter(track => {
        const normalizedTitle = normalizeText(track.title || track.name || '');
        const normalizedArtist = normalizeText(track.artist || 'Unknown Artist');
        const normalizedTags = normalizeText(track.tags || '');
        const normalizedPath = includePathInSearch ? normalizeText(track.path || '') : '';

        return normalizedTitle.includes(normalizedTerm) ||
            normalizedArtist.includes(normalizedTerm) ||
            normalizedTags.includes(normalizedTerm) ||
            normalizedPath.includes(normalizedTerm);
    });
}

// Function to render manual search results
function renderManualSearchResults() {
    if (!manualSearchResultsContainer) return;

    if (manualSearchResults.length === 0) {
        manualSearchResultsContainer.innerHTML = '<div class="empty-state">No tracks found matching your search</div>';
        return;
    }

    const resultsHtml = manualSearchResults.map(track => {
        const title = track.title || track.name || 'Unknown Title';
        const artist = track.artist && track.artist !== 'Unknown Artist' ? track.artist : '';
        const duration = track.duration ? formatDuration(track.duration) : '';
        const tags = track.tags ? track.tags.split(',').map(tag => tag.trim()).filter(tag => tag) : [];

        return `
            <div class="manual-track-item" data-track-id="${track.id}">
                <div class="manual-track-info">
                    <div class="manual-track-title">${escapeHtml(title)}</div>
                    <div class="manual-track-details">
                        ${artist ? `<span class="manual-track-artist">${escapeHtml(artist)}</span>` : ''}
                        ${duration ? `<span class="manual-track-duration">${duration}</span>` : ''}
                    </div>
                    ${tags.length > 0 ? `
                        <div class="manual-track-tags">
                            ${tags.map(tag => `<span class="manual-track-tag">${escapeHtml(tag)}</span>`).join('')}
                        </div>
                    ` : ''}
                </div>
            </div>
        `;
    }).join('');

    manualSearchResultsContainer.innerHTML = resultsHtml;

    // Add click event listeners to track items
    const trackItems = manualSearchResultsContainer.querySelectorAll('.manual-track-item');
    trackItems.forEach(item => {
        item.addEventListener('click', () => {
            const trackId = parseInt(item.dataset.trackId);
            selectTrackForReplacement(trackId);
        });
    });
}

// Function to select a track for replacement
function selectTrackForReplacement(trackId) {
    const track = manualSearchResults.find(t => t.id === trackId);
    if (!track) return;

    selectedTrackForReplacement = track;

    // Update UI to show selected track
    const trackItems = manualSearchResultsContainer.querySelectorAll('.manual-track-item');
    trackItems.forEach(item => {
        item.classList.remove('selected');
        if (parseInt(item.dataset.trackId) === trackId) {
            item.classList.add('selected');
        }
    });

    // Show selected track info
    if (selectedTrackInfoContainer) {
        const title = track.title || track.name || 'Unknown Title';
        const artist = track.artist && track.artist !== 'Unknown Artist' ? track.artist : '';
        const duration = track.duration ? formatDuration(track.duration) : '';

        selectedTrackInfoContainer.innerHTML = `
            <h6>Selected Track:</h6>
            <div class="selected-track-details">
                <div class="selected-track-name">${escapeHtml(title)}</div>
                <div class="selected-track-meta">
                    ${artist ? `Artist: ${escapeHtml(artist)}` : ''}
                    ${duration ? ` • Duration: ${duration}` : ''}
                </div>
            </div>
        `;
        selectedTrackInfoContainer.classList.remove('hidden');
    }

    // Enable replace button
    if (replaceManualBtn) {
        replaceManualBtn.disabled = false;
    }
}

// Function to handle manual track replacement
async function handleManualTrackReplacement() {
    if (!selectedTrackForReplacement || !currentTrackForReplacement || !currentPlaylist) {
        console.error('Missing required data for manual track replacement');
        alert('Please select a track to replace with');
        return;
    }

    try {
        // Show loading state
        if (replaceManualBtn) {
            replaceManualBtn.disabled = true;
            replaceManualBtn.innerHTML = `
                <svg class="icon spin" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <line x1="12" y1="2" x2="12" y2="6"></line>
                    <line x1="12" y1="18" x2="12" y2="22"></line>
                    <line x1="4.93" y1="4.93" x2="7.76" y2="7.76"></line>
                    <line x1="16.24" y1="16.24" x2="19.07" y2="19.07"></line>
                    <line x1="2" y1="12" x2="6" y2="12"></line>
                    <line x1="18" y1="12" x2="22" y2="12"></line>
                    <line x1="4.93" y1="19.07" x2="7.76" y2="16.24"></line>
                    <line x1="16.24" y1="7.76" x2="19.07" y2="4.93"></line>
                </svg>
            `;
        }

        // Call the IPC handler to replace the track manually
        const result = await window.playlist.replaceTrackManually({
            playlistId: currentPlaylist.id,
            oldTrackId: currentTrackForReplacement,
            newTrackId: selectedTrackForReplacement.id
        });

        if (result && result.success) {
            // Close the popup
            closeTrackReplacementPopup();

            // Refresh the playlist view
            await openPlaylist(currentPlaylist.id);
        } else {
            const errorMessage = result && result.error ? result.error : 'Unknown error occurred';
            console.error('Failed to replace track manually:', errorMessage);
            alert(`Failed to replace track: ${errorMessage}`);

            // Reset button state
            if (replaceManualBtn) {
                replaceManualBtn.disabled = false;
                replaceManualBtn.innerHTML = 'Replace with Selected Track';
            }
        }
    } catch (error) {
        console.error('Error replacing track manually:', error);
        alert(`Error replacing track: ${error.message || 'Unknown error occurred'}`);

        // Reset button state
        if (replaceManualBtn) {
            replaceManualBtn.disabled = false;
            replaceManualBtn.innerHTML = 'Replace with Selected Track';
        }
    }
}

// Flag to track if event listeners have been set up
let trackReplacementListenersSetup = false;

// Function to set up track replacement popup event listeners (called once)
function setupTrackReplacementPopupListeners() {
    console.log('Setting up track replacement popup listeners...');

    // Close button event listeners
    if (closeTrackReplacementBtn) {
        closeTrackReplacementBtn.addEventListener('click', closeTrackReplacementPopup);
        console.log('Close button listener added');
    } else {
        console.error('closeTrackReplacementBtn not found');
    }

    if (cancelTrackReplacementBtn) {
        cancelTrackReplacementBtn.addEventListener('click', closeTrackReplacementPopup);
        console.log('Cancel button listener added');
    } else {
        console.error('cancelTrackReplacementBtn not found');
    }

    // Replace buttons event listeners
    document.querySelectorAll('.replace-btn').forEach(button => {
        button.addEventListener('click', () => {
            const mode = button.dataset.mode;
            replaceTrackByTags(mode);
        });
    });
    console.log('Replace buttons listeners added:', document.querySelectorAll('.replace-btn').length);

    // Click outside to close
    if (trackReplacementPopup) {
        trackReplacementPopup.addEventListener('click', (event) => {
            if (event.target === trackReplacementPopup) {
                closeTrackReplacementPopup();
            }
        });
        console.log('Click outside listener added');
    } else {
        console.error('trackReplacementPopup not found');
    }

    // Initialize manual track selection
    initializeManualTrackSelection();

    trackReplacementListenersSetup = true;
}

// Function to set up dynamic event listeners (called each time popup opens)
function setupTrackReplacementPopupEventListeners() {
    if (!trackReplacementListenersSetup) {
        setupTrackReplacementPopupListeners();
    }

    // Select tags button event listener (set up each time to ensure it works)
    const selectTagsBtn = document.querySelector('.select-tags-btn');
    if (selectTagsBtn) {
        // Remove any existing listeners to prevent duplicates
        selectTagsBtn.replaceWith(selectTagsBtn.cloneNode(true));
        const newSelectTagsBtn = document.querySelector('.select-tags-btn');

        newSelectTagsBtn.addEventListener('click', () => {
            console.log('Select tags button clicked');
            const customTagSelection = document.querySelector('.custom-tag-selection');
            if (customTagSelection) {
                customTagSelection.classList.remove('hidden');
                console.log('Custom tag selection shown');
            } else {
                console.error('customTagSelection element not found');
            }

            newSelectTagsBtn.classList.add('hidden');

            const replaceCustomBtn = document.querySelector('.replace-custom-btn');
            if (replaceCustomBtn) {
                replaceCustomBtn.classList.remove('hidden');
                console.log('Replace custom button shown');
            } else {
                console.error('replaceCustomBtn element not found');
            }
        });
        console.log('Select tags button listener added');
    } else {
        console.error('selectTagsBtn not found');
    }

    // Replace with selected tags button event listener (set up each time)
    const replaceCustomBtn = document.querySelector('.replace-custom-btn');
    if (replaceCustomBtn) {
        // Remove any existing listeners to prevent duplicates
        replaceCustomBtn.replaceWith(replaceCustomBtn.cloneNode(true));
        const newReplaceCustomBtn = document.querySelector('.replace-custom-btn');

        newReplaceCustomBtn.addEventListener('click', () => {
            console.log('Replace custom button clicked, selected tags:', selectedTagsForReplacement);
            if (selectedTagsForReplacement.length === 0) {
                alert('Please select at least one tag for replacement');
                return;
            }

            replaceTrackByTags('custom');
        });
        console.log('Replace custom button listener added');
    } else {
        console.error('replaceCustomBtn not found');
    }
}

// Function to export a playlist as .m3u8 file
async function exportPlaylistAsM3u8(playlistId, playlistName) {
    try {
        console.log(`Exporting playlist ${playlistId} (${playlistName}) as .m3u8`);

        // Make sure we have a valid playlist name
        if (!playlistName || playlistName.trim() === '') {
            console.warn('Empty playlist name provided, using default name');
            playlistName = `Playlist_${new Date().toISOString().slice(0, 10)}`;
        }

        console.log(`Using playlist name for export: "${playlistName}"`);

        // Call the main process to export the playlist
        const result = await window.playlist.exportM3u8({
            playlistId,
            playlistName
        });

        if (result.success) {
            alert(`Playlist esportata con successo in formato .m3u8!\nFile: ${result.filePath}\nTracce: ${result.trackCount}`);
        } else if (result.canceled) {
            console.log('Export canceled by user');
        } else {
            alert(`Errore durante l'esportazione della playlist: ${result.error}`);
        }
    } catch (error) {
        console.error('Error exporting playlist:', error);
        alert(`Errore durante l'esportazione della playlist: ${error.message}`);
    }
}

// Il pulsante "Esporta .m3u8" è stato rimosso dal form di generazione della playlist

// Function to rename a playlist
async function renamePlaylist(playlistId, newName) {
    if (!playlistId || !newName) return null;

    try {
        console.log(`Renaming playlist ${playlistId} to "${newName}"`);

        // Call the main process to update the playlist name
        const result = await window.playlist.rename({ playlistId, name: newName });

        if (result.success) {
            console.log('Playlist renamed successfully');

            // Update the playlist in the allPlaylists array
            const playlistIndex = allPlaylists.findIndex(p => p.id === playlistId);
            if (playlistIndex !== -1) {
                allPlaylists[playlistIndex].name = newName;
            }

            // Update the current playlist if it's the one being renamed
            if (currentPlaylist && currentPlaylist.id === playlistId) {
                currentPlaylist.name = newName;
                currentPlaylistName.textContent = newName;
            }

            // Refresh the playlists list
            renderPlaylists();

            return result;
        } else {
            console.error('Failed to rename playlist:', result.error);
            alert(`Failed to rename playlist: ${result.error}`);
            return null;
        }
    } catch (error) {
        console.error('Error renaming playlist:', error);
        alert(`Error renaming playlist: ${error.message}`);
        return null;
    }
}

// Function to open the rename playlist popup
function openRenamePlaylistPopup() {
    if (!currentPlaylist) return;

    // Reset form
    renamePlaylistForm.reset();

    // Set current playlist name as default
    newPlaylistNameInput.value = currentPlaylist.name;

    // Show popup
    renamePlaylistPopup.classList.remove('hidden');
    renamePlaylistPopup.classList.remove('fully-hidden');

    // Focus on input
    newPlaylistNameInput.focus();
    newPlaylistNameInput.select(); // Select the text for easy editing

    // Add event listeners
    closeRenamePlaylistBtn.addEventListener('click', closeRenamePlaylistPopup);
    cancelRenamePlaylistBtn.addEventListener('click', closeRenamePlaylistPopup);
    renamePlaylistForm.addEventListener('submit', handleRenamePlaylist);

    // Event listener for clicking outside the popup
    renamePlaylistPopup.addEventListener('click', (event) => {
        if (event.target === renamePlaylistPopup) {
            closeRenamePlaylistPopup();
        }
    });
}

// Function to close the rename playlist popup
function closeRenamePlaylistPopup() {
    // Remove event listeners
    closeRenamePlaylistBtn.removeEventListener('click', closeRenamePlaylistPopup);
    cancelRenamePlaylistBtn.removeEventListener('click', closeRenamePlaylistPopup);
    renamePlaylistForm.removeEventListener('submit', handleRenamePlaylist);

    // Hide popup
    renamePlaylistPopup.classList.add('hidden');
    setTimeout(() => {
        renamePlaylistPopup.classList.add('fully-hidden');
    }, 300);
}

// Function to handle rename playlist form submission
async function handleRenamePlaylist(event) {
    event.preventDefault();

    if (!currentPlaylist) {
        alert('Nessuna playlist selezionata.');
        return;
    }

    const newName = newPlaylistNameInput.value.trim();

    if (!newName) {
        alert('Inserisci un nome valido per la playlist.');
        newPlaylistNameInput.focus();
        return;
    }

    // Rename the playlist
    const result = await renamePlaylist(currentPlaylist.id, newName);

    if (result && result.success) {
        // Close the popup
        closeRenamePlaylistPopup();

        // Show success message
        alert(`Playlist rinominata con successo in "${newName}".`);
    }
}

// Function to open the swap tracks popup
function openSwapTracksPopup() {
    // First, ensure any existing event listeners are removed
    removeSwapTracksEventListeners();

    // Reset form
    swapTracksForm.reset();

    // Set max values for position inputs based on current playlist track count
    const trackCount = currentPlaylist ? currentPlaylist.trackCount : 0;
    swapTrackPosition1.max = trackCount;
    swapTrackPosition2.max = trackCount;

    // Default values
    swapTrackPosition1.value = 1;
    swapTrackPosition2.value = trackCount > 1 ? 2 : 1;

    // Show popup
    swapTracksPopup.classList.remove('hidden');
    swapTracksPopup.classList.remove('fully-hidden');

    // Focus on first input
    setTimeout(() => {
        swapTrackPosition1.focus();
    }, 100);

    // Add event listeners
    addSwapTracksEventListeners();
}

// Store references to event listeners for proper cleanup
let swapTracksEventListeners = {
    closeClick: null,
    cancelClick: null,
    formSubmit: null,
    outsideClick: null
};

// Function to add swap tracks event listeners
function addSwapTracksEventListeners() {
    // Create event listener functions
    swapTracksEventListeners.closeClick = () => closeSwapTracksPopup();
    swapTracksEventListeners.cancelClick = () => closeSwapTracksPopup();
    swapTracksEventListeners.formSubmit = (event) => handleSwapTracks(event);
    swapTracksEventListeners.outsideClick = (event) => {
        if (event.target === swapTracksPopup) {
            closeSwapTracksPopup();
        }
    };

    // Add event listeners
    closeSwapTracksBtn.addEventListener('click', swapTracksEventListeners.closeClick);
    cancelSwapTracksBtn.addEventListener('click', swapTracksEventListeners.cancelClick);
    swapTracksForm.addEventListener('submit', swapTracksEventListeners.formSubmit);
    swapTracksPopup.addEventListener('click', swapTracksEventListeners.outsideClick);

    // Add event listeners to prevent input events from bubbling up and causing issues
    swapTrackPosition1.addEventListener('click', (event) => {
        event.stopPropagation();
    });
    swapTrackPosition1.addEventListener('input', (event) => {
        event.stopPropagation();
    });
    swapTrackPosition1.addEventListener('change', (event) => {
        event.stopPropagation();
    });

    swapTrackPosition2.addEventListener('click', (event) => {
        event.stopPropagation();
    });
    swapTrackPosition2.addEventListener('input', (event) => {
        event.stopPropagation();
    });
    swapTrackPosition2.addEventListener('change', (event) => {
        event.stopPropagation();
    });

    // Prevent form container clicks from bubbling up
    const popupContainer = swapTracksPopup.querySelector('.popup-container');
    if (popupContainer) {
        popupContainer.addEventListener('click', (event) => {
            event.stopPropagation();
        });
    }
}

// Function to remove swap tracks event listeners
function removeSwapTracksEventListeners() {
    if (swapTracksEventListeners.closeClick) {
        closeSwapTracksBtn.removeEventListener('click', swapTracksEventListeners.closeClick);
    }
    if (swapTracksEventListeners.cancelClick) {
        cancelSwapTracksBtn.removeEventListener('click', swapTracksEventListeners.cancelClick);
    }
    if (swapTracksEventListeners.formSubmit) {
        swapTracksForm.removeEventListener('submit', swapTracksEventListeners.formSubmit);
    }
    if (swapTracksEventListeners.outsideClick) {
        swapTracksPopup.removeEventListener('click', swapTracksEventListeners.outsideClick);
    }

    // Clear references
    swapTracksEventListeners = {
        closeClick: null,
        cancelClick: null,
        formSubmit: null,
        outsideClick: null
    };
}

// Function to close the swap tracks popup
function closeSwapTracksPopup() {
    // Remove event listeners
    removeSwapTracksEventListeners();

    // Hide popup
    swapTracksPopup.classList.add('hidden');
    setTimeout(() => {
        swapTracksPopup.classList.add('fully-hidden');
    }, 300);
}

// Function to handle swap tracks form submission
async function handleSwapTracks(event) {
    event.preventDefault();

    if (!currentPlaylist) {
        alert('Nessuna playlist selezionata.');
        return;
    }

    const position1 = parseInt(swapTrackPosition1.value);
    const position2 = parseInt(swapTrackPosition2.value);

    if (isNaN(position1) || isNaN(position2)) {
        alert('Inserisci posizioni valide.');
        return;
    }

    if (position1 === position2) {
        alert('Le posizioni devono essere diverse.');
        return;
    }

    if (position1 < 1 || position2 < 1 ||
        position1 > currentPlaylist.trackCount ||
        position2 > currentPlaylist.trackCount) {
        alert(`Le posizioni devono essere comprese tra 1 e ${currentPlaylist.trackCount}.`);
        return;
    }

    try {
        // Get all track elements with their data
        const trackElements = Array.from(playlistTracks.querySelectorAll('.playlist-track-item'));

        if (trackElements.length < 2) {
            alert('La playlist deve contenere almeno 2 tracce per poter scambiare le posizioni.');
            return;
        }

        // Adjust positions to array indices (0-based)
        const index1 = position1 - 1;
        const index2 = position2 - 1;

        // Get the track elements at the specified positions
        const track1Element = trackElements[index1];
        const track2Element = trackElements[index2];

        const track1Id = parseInt(track1Element.dataset.trackId);
        const track2Id = parseInt(track2Element.dataset.trackId);
        const track1Cycle = track1Element.dataset.cycle ? parseInt(track1Element.dataset.cycle) : null;
        const track2Cycle = track2Element.dataset.cycle ? parseInt(track2Element.dataset.cycle) : null;

        console.log(`Swapping tracks: ${track1Id} (cycle ${track1Cycle}) <-> ${track2Id} (cycle ${track2Cycle})`);

        // Check if this is a cross-cycle swap in a tag-generated playlist
        const isCrossCycleSwap = track1Cycle !== null && track2Cycle !== null && track1Cycle !== track2Cycle;

        if (isCrossCycleSwap) {
            // For cross-cycle swaps, we need to use a special handler that updates cycle information
            const result = await window.playlist.swapTracksWithCycles({
                playlistId: currentPlaylist.id,
                track1Id: track1Id,
                track1Position: position1,
                track1Cycle: track1Cycle,
                track2Id: track2Id,
                track2Position: position2,
                track2Cycle: track2Cycle
            });

            if (result && result.success) {
                // Close the popup
                closeSwapTracksPopup();

                // Refresh the playlist view
                await openPlaylist(currentPlaylist.id);

                // Show success message
                alert(`Posizioni delle tracce ${position1} e ${position2} scambiate con successo.`);
            } else {
                alert('Errore durante lo scambio delle posizioni delle tracce.');
            }
        } else {
            // For same-cycle or non-cycle swaps, use the existing logic
            const tracks = trackElements.map(item => parseInt(item.dataset.trackId));

            // Swap the tracks
            const temp = tracks[index1];
            tracks[index1] = tracks[index2];
            tracks[index2] = temp;

            // Update the positions in the database
            const result = await updatePlaylistTrackPositions(currentPlaylist.id, tracks);

            if (result && result.success) {
                // Close the popup
                closeSwapTracksPopup();

                // Refresh the playlist view
                await openPlaylist(currentPlaylist.id);

                // Show success message
                alert(`Posizioni delle tracce ${position1} e ${position2} scambiate con successo.`);
            } else {
                alert('Errore durante lo scambio delle posizioni delle tracce.');
            }
        }
    } catch (error) {
        console.error('Error swapping track positions:', error);
        alert(`Errore durante lo scambio delle posizioni delle tracce: ${error.message}`);
    }
}

// Function to handle track up button click
async function handleTrackUpClick(event) {
    event.stopPropagation();
    if (!currentPlaylist) return;

    const trackId = parseInt(this.dataset.trackId);
    if (isNaN(trackId)) return;

    try {
        console.log(`Moving track ${trackId} up`);

        // Get all tracks in the current playlist
        const tracks = Array.from(playlistTracks.querySelectorAll('.playlist-track-item'))
            .map(item => parseInt(item.dataset.trackId));

        // Find the index of the current track
        const currentIndex = tracks.indexOf(trackId);

        // If it's already at the top or not found, do nothing
        if (currentIndex <= 0) {
            console.log('Track is already at the top or not found');
            return;
        }

        // For tag-generated playlists, we need to check if we're moving within the same cycle
        const cycleNumber = this.dataset.cycle ? parseInt(this.dataset.cycle) : null;

        if (cycleNumber !== null) {
            // This is a track in a tag-generated playlist with cycles
            // Get all tracks in the current cycle
            const tracksInCycle = Array.from(playlistTracks.querySelectorAll(`.playlist-track-item[data-cycle="${cycleNumber}"]`))
                .map(item => parseInt(item.dataset.trackId));

            // Find the index of the current track within its cycle
            const cycleIndex = tracksInCycle.indexOf(trackId);

            // If it's already at the top of its cycle, do nothing
            if (cycleIndex <= 0) {
                console.log('Track is already at the top of its cycle');
                return;
            }

            // Get the track to swap with (the one above it in the same cycle)
            const trackAboveId = tracksInCycle[cycleIndex - 1];

            // Find the indices in the overall tracks array
            const trackAboveIndex = tracks.indexOf(trackAboveId);

            // Create a new array with the swapped positions
            const newTracks = [...tracks];

            // Simple swap: exchange the two tracks
            [newTracks[trackAboveIndex], newTracks[currentIndex]] = [newTracks[currentIndex], newTracks[trackAboveIndex]];

            // Update the positions in the database
            const result = await updatePlaylistTrackPositions(currentPlaylist.id, newTracks);

            if (result && result.success) {
                // Refresh the playlist view
                await openPlaylist(currentPlaylist.id);
            } else {
                console.error('Failed to move track up:', result?.error);
                alert(`Failed to move track up: ${result?.error || 'Unknown error'}`);
            }
        } else {
            // Regular playlist without cycles, just swap with the track above
            const newTracks = [...tracks];

            // Swap the current track with the one above it
            [newTracks[currentIndex - 1], newTracks[currentIndex]] = [newTracks[currentIndex], newTracks[currentIndex - 1]];

            // Update the positions in the database
            const result = await updatePlaylistTrackPositions(currentPlaylist.id, newTracks);

            if (result && result.success) {
                // Refresh the playlist view
                await openPlaylist(currentPlaylist.id);
            } else {
                console.error('Failed to move track up:', result?.error);
                alert(`Failed to move track up: ${result?.error || 'Unknown error'}`);
            }
        }
    } catch (error) {
        console.error('Error handling track up click:', error);
        alert(`Error moving track up: ${error.message}`);
    }
}

// Function to handle track down button click
async function handleTrackDownClick(event) {
    event.stopPropagation();
    if (!currentPlaylist) return;

    const trackId = parseInt(this.dataset.trackId);
    if (isNaN(trackId)) return;

    try {
        console.log(`Moving track ${trackId} down`);

        // Get all tracks in the current playlist
        const tracks = Array.from(playlistTracks.querySelectorAll('.playlist-track-item'))
            .map(item => parseInt(item.dataset.trackId));

        // Find the index of the current track
        const currentIndex = tracks.indexOf(trackId);

        // If it's already at the bottom or not found, do nothing
        if (currentIndex === -1 || currentIndex >= tracks.length - 1) {
            console.log('Track is already at the bottom or not found');
            return;
        }

        // For tag-generated playlists, we need to check if we're moving within the same cycle
        const cycleNumber = this.dataset.cycle ? parseInt(this.dataset.cycle) : null;

        if (cycleNumber !== null) {
            // This is a track in a tag-generated playlist with cycles
            // Get all tracks in the current cycle
            const tracksInCycle = Array.from(playlistTracks.querySelectorAll(`.playlist-track-item[data-cycle="${cycleNumber}"]`))
                .map(item => parseInt(item.dataset.trackId));

            // Find the index of the current track within its cycle
            const cycleIndex = tracksInCycle.indexOf(trackId);

            // If it's already at the bottom of its cycle, do nothing
            if (cycleIndex === -1 || cycleIndex >= tracksInCycle.length - 1) {
                console.log('Track is already at the bottom of its cycle');
                return;
            }

            // Get the track to swap with (the one below it in the same cycle)
            const trackBelowId = tracksInCycle[cycleIndex + 1];

            // Find the indices in the overall tracks array
            const trackBelowIndex = tracks.indexOf(trackBelowId);

            // Create a new array with the swapped positions
            const newTracks = [...tracks];

            // Simple swap: exchange the two tracks
            [newTracks[currentIndex], newTracks[trackBelowIndex]] = [newTracks[trackBelowIndex], newTracks[currentIndex]];

            // Update the positions in the database
            const result = await updatePlaylistTrackPositions(currentPlaylist.id, newTracks);

            if (result && result.success) {
                // Refresh the playlist view
                await openPlaylist(currentPlaylist.id);
            } else {
                console.error('Failed to move track down:', result?.error);
                alert(`Failed to move track down: ${result?.error || 'Unknown error'}`);
            }
        } else {
            // Regular playlist without cycles, just swap with the track below
            const newTracks = [...tracks];

            // Swap the current track with the one below it
            [newTracks[currentIndex], newTracks[currentIndex + 1]] = [newTracks[currentIndex + 1], newTracks[currentIndex]];

            // Update the positions in the database
            const result = await updatePlaylistTrackPositions(currentPlaylist.id, newTracks);

            if (result && result.success) {
                // Refresh the playlist view
                await openPlaylist(currentPlaylist.id);
            } else {
                console.error('Failed to move track down:', result?.error);
                alert(`Failed to move track down: ${result?.error || 'Unknown error'}`);
            }
        }
    } catch (error) {
        console.error('Error handling track down click:', error);
        alert(`Error moving track down: ${error.message}`);
    }
}

// Function to show import completion report with detailed error information
function showImportCompletionReport(stats, failedFiles) {
    let message = `Import completed!\n\n`;

    // Add success statistics
    if (stats.new > 0) {
        message += `✓ Successfully imported: ${stats.new} tracks\n`;
    }
    if (stats.updated > 0) {
        message += `✓ Updated: ${stats.updated} tracks\n`;
    }
    if (stats.unchanged > 0) {
        message += `• Unchanged: ${stats.unchanged} tracks\n`;
    }
    if (stats.skipped > 0) {
        message += `• Skipped: ${stats.skipped} files (unsupported format)\n`;
    }

    // Add error information if there are any failures
    if (stats.errors > 0 && failedFiles.length > 0) {
        message += `\n❌ Failed to import: ${stats.errors} files\n\n`;
        message += `Failed files and reasons:\n`;
        message += `${'='.repeat(50)}\n`;

        // Group failed files by error type for better readability
        const errorGroups = {};
        failedFiles.forEach(file => {
            if (!errorGroups[file.errorType]) {
                errorGroups[file.errorType] = [];
            }
            errorGroups[file.errorType].push(file);
        });

        // Display grouped errors
        Object.keys(errorGroups).forEach(errorType => {
            message += `\n${errorType}:\n`;
            errorGroups[errorType].forEach(file => {
                message += `  • ${file.fileName}\n`;
                message += `    Path: ${file.filePath}\n`;
                if (file.error !== errorType) {
                    message += `    Details: ${file.error}\n`;
                }
            });
        });

        message += `\n${'='.repeat(50)}\n`;
        message += `\nTroubleshooting tips:\n`;
        message += `• Check file permissions and ensure files are accessible\n`;
        message += `• Verify network paths are properly mounted\n`;
        message += `• Ensure files are in supported formats (MP3, M4A, AAC, FLAC, WAV, OGG)\n`;
        message += `• Try moving problematic files to a local directory\n`;
    } else if (stats.errors > 0) {
        message += `\n❌ Failed to import: ${stats.errors} files\n`;
        message += `(No detailed error information available)\n`;
    }

    // Show the report
    alert(message);
}