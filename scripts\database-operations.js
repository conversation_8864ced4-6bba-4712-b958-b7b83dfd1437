const { app, ipcMain } = require('electron/main');
const path = require('node:path');
const fs = require('fs/promises'); // For fs.access
const sqlite3 = require('sqlite3').verbose();
const fileOps = require('./file-operations'); // For generateUniqueTrackId

let db; // Module-scoped database instance

// Database setup (adapted from main.js)
function setupDatabase() {
    return new Promise((resolve, reject) => {
        const dbPath = path.join(app.getPath('userData'), 'playlist-maker.sqlite');
        console.log('Database path:', dbPath);

        fs.access(dbPath)
            .then(() => console.log('Database file exists'))
            .catch(err => {
                if (err.code === 'ENOENT') {
                    console.log('Database file does not exist yet, will be created');
                } else {
                    console.error('Error checking database file:', err);
                }
            });

        db = new sqlite3.Database(dbPath, (err) => {
            if (err) {
                console.error('Error opening database', err.message);
                reject(err);
            } else {
                console.log('Connected to the SQLite database.');
                db.run(`CREATE TABLE IF NOT EXISTS tracks (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    unique_id TEXT UNIQUE,
                    path TEXT NOT NULL,
                    name TEXT NOT NULL,
                    extension TEXT,
                    title TEXT,
                    artist TEXT,
                    createdAt TEXT DEFAULT CURRENT_TIMESTAMP,
                    updatedAt TEXT DEFAULT CURRENT_TIMESTAMP
                )`, (err) => {
                    if (err) {
                        console.error('Error creating table', err.message);
                        reject(err);
                    } else {
                        db.all("PRAGMA table_info(tracks)", (err, rows) => {
                            if (err) {
                                console.error('Error checking table schema:', err.message);
                                reject(err);
                                return;
                            }

                            const hasDurationColumn = rows && rows.some(row => row.name === 'duration');
                            const hasTagsColumn = rows && rows.some(row => row.name === 'tags');
                            const hasUniqueIdColumn = rows && rows.some(row => row.name === 'unique_id');

                            const checkTrackCountAndResolve = () => {
                                db.get('SELECT COUNT(*) as count FROM tracks', (countErr, row) => {
                                    if (countErr) {
                                        console.error('Error checking track count:', countErr.message);
                                    } else {
                                        console.log(`Database contains ${row.count} tracks`);
                                    }
                                    console.log('Database setup complete.');
                                    resolve(db); // Resolve with the db instance
                                });
                            };

                            const createPlaylistTables = () => {
                                db.run(`CREATE TABLE IF NOT EXISTS playlists (
                                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                                    name TEXT NOT NULL,
                                    is_generated INTEGER DEFAULT 0,
                                    createdAt TEXT DEFAULT CURRENT_TIMESTAMP,
                                    updatedAt TEXT DEFAULT CURRENT_TIMESTAMP
                                )`, (err) => {
                                    if (err) console.error('Error creating playlists table:', err.message);
                                    else console.log('Playlists table created or already exists');

                                    db.all("PRAGMA table_info(playlists)", (err, playlistRows) => {
                                        if (err) console.error('Error checking playlists table structure:', err.message);
                                        else {
                                            const hasIsGeneratedColumn = playlistRows.some(row => row.name === 'is_generated');
                                            if (!hasIsGeneratedColumn) {
                                                db.run('ALTER TABLE playlists ADD COLUMN is_generated INTEGER DEFAULT 0', (alterErr) => {
                                                    if (alterErr) console.error('Error adding is_generated column:', alterErr.message);
                                                    else console.log('is_generated column added successfully');
                                                });
                                            }
                                        }
                                    });

                                    db.run(`CREATE TABLE IF NOT EXISTS playlist_tracks (
                                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                                        playlist_id INTEGER NOT NULL,
                                        track_id INTEGER NOT NULL,
                                        position INTEGER,
                                        source_playlist_id INTEGER,
                                        cycle INTEGER DEFAULT 1,
                                        createdAt TEXT DEFAULT CURRENT_TIMESTAMP,
                                        FOREIGN KEY (playlist_id) REFERENCES playlists (id) ON DELETE CASCADE,
                                        FOREIGN KEY (track_id) REFERENCES tracks (id) ON DELETE CASCADE,
                                        FOREIGN KEY (source_playlist_id) REFERENCES playlists (id) ON DELETE SET NULL,
                                        UNIQUE(playlist_id, track_id, cycle)
                                    )`, (err) => {
                                        if (err) console.error('Error creating playlist_tracks table:', err.message);
                                        else console.log('Playlist_tracks table created or already exists');

                                        // Check if we need to recreate the playlist_tracks table
                                        db.all("PRAGMA table_info(playlist_tracks)", (err, rows) => {
                                            if (err) {
                                                console.error('Error checking playlist_tracks table structure:', err.message);
                                                return;
                                            }

                                            // Check if the table exists and has the correct structure
                                            const hasCycleColumn = rows && rows.some(row => row.name === 'cycle');
                                            const hasSourcePlaylistIdColumn = rows && rows.some(row => row.name === 'source_playlist_id');

                                            if (!hasCycleColumn || !hasSourcePlaylistIdColumn || rows.length === 0) {
                                                console.log('Recreating playlist_tracks table from scratch...');
                                                db.run('DROP TABLE IF EXISTS playlist_tracks', (dropErr) => {
                                                    if (dropErr) {
                                                        console.error('Error dropping playlist_tracks table:', dropErr.message);
                                                        return;
                                                    }

                                                    // Create the new table with the correct structure
                                                    db.run(`CREATE TABLE playlist_tracks (
                                                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                                                        playlist_id INTEGER NOT NULL,
                                                        track_id INTEGER NOT NULL,
                                                        position INTEGER,
                                                        source_playlist_id INTEGER,
                                                        cycle INTEGER DEFAULT 1,
                                                        createdAt TEXT DEFAULT CURRENT_TIMESTAMP,
                                                        FOREIGN KEY (playlist_id) REFERENCES playlists (id) ON DELETE CASCADE,
                                                        FOREIGN KEY (track_id) REFERENCES tracks (id) ON DELETE CASCADE,
                                                        FOREIGN KEY (source_playlist_id) REFERENCES playlists (id) ON DELETE SET NULL,
                                                        UNIQUE(playlist_id, track_id, cycle)
                                                    )`, (createErr) => {
                                                        if (createErr) {
                                                            console.error('Error creating new playlist_tracks table:', createErr.message);
                                                        } else {
                                                            console.log('Successfully recreated playlist_tracks table with correct structure');

                                                            // Create an index to improve query performance
                                                            db.run('CREATE INDEX IF NOT EXISTS idx_playlist_tracks_playlist_id ON playlist_tracks(playlist_id)', (indexErr) => {
                                                                if (indexErr) {
                                                                    console.error('Error creating index on playlist_tracks:', indexErr.message);
                                                                } else {
                                                                    console.log('Created index on playlist_tracks.playlist_id');
                                                                }
                                                            });
                                                        }
                                                    });
                                                });
                                            } else {
                                                console.log('playlist_tracks table has the correct structure');

                                                // Create an index to improve query performance if it doesn't exist
                                                db.run('CREATE INDEX IF NOT EXISTS idx_playlist_tracks_playlist_id ON playlist_tracks(playlist_id)', (indexErr) => {
                                                    if (indexErr) {
                                                        console.error('Error creating index on playlist_tracks:', indexErr.message);
                                                    } else {
                                                        console.log('Created index on playlist_tracks.playlist_id');
                                                    }
                                                });
                                            }
                                        });

                                        db.run(`CREATE TABLE IF NOT EXISTS playlist_generators (
                                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                                            name TEXT NOT NULL,
                                            target_duration INTEGER,
                                            createdAt TEXT DEFAULT CURRENT_TIMESTAMP,
                                            updatedAt TEXT DEFAULT CURRENT_TIMESTAMP
                                        )`, (err) => {
                                            if (err) console.error('Error creating playlist_generators table:', err.message);
                                            else console.log('Playlist_generators table created or already exists');

                                            db.run(`CREATE TABLE IF NOT EXISTS playlist_generator_sources (
                                                id INTEGER PRIMARY KEY AUTOINCREMENT,
                                                generator_id INTEGER NOT NULL,
                                                playlist_id INTEGER NOT NULL,
                                                track_count INTEGER NOT NULL DEFAULT 1,
                                                position INTEGER,
                                                createdAt TEXT DEFAULT CURRENT_TIMESTAMP,
                                                FOREIGN KEY (generator_id) REFERENCES playlist_generators (id) ON DELETE CASCADE,
                                                FOREIGN KEY (playlist_id) REFERENCES playlists (id) ON DELETE CASCADE,
                                                UNIQUE(generator_id, playlist_id)
                                            )`, (err) => {
                                                if (err) console.error('Error creating playlist_generator_sources table:', err.message);
                                                else console.log('Playlist_generator_sources table created or already exists');

                                                db.run(`CREATE TABLE IF NOT EXISTS default_tags (
                                                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                                                    name TEXT NOT NULL UNIQUE,
                                                    color TEXT,
                                                    createdAt TEXT DEFAULT CURRENT_TIMESTAMP,
                                                    updatedAt TEXT DEFAULT CURRENT_TIMESTAMP
                                                )`, (err) => {
                                                    if (err) console.error('Error creating default_tags table:', err.message);
                                                    else console.log('Default_tags table created or already exists');

                                                    // Create tables for tag-based playlist generation
                                                    db.run(`CREATE TABLE IF NOT EXISTS playlist_tag_generators (
                                                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                                                        name TEXT NOT NULL,
                                                        target_duration INTEGER,
                                                        createdAt TEXT DEFAULT CURRENT_TIMESTAMP,
                                                        updatedAt TEXT DEFAULT CURRENT_TIMESTAMP
                                                    )`, (err) => {
                                                        if (err) console.error('Error creating playlist_tag_generators table:', err.message);
                                                        else console.log('Playlist_tag_generators table created or already exists');

                                                        db.run(`CREATE TABLE IF NOT EXISTS playlist_tag_generator_sources (
                                                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                                                            generator_id INTEGER NOT NULL,
                                                            primary_tag TEXT NOT NULL,
                                                            secondary_tags TEXT,
                                                            track_count INTEGER NOT NULL DEFAULT 1,
                                                            position INTEGER,
                                                            createdAt TEXT DEFAULT CURRENT_TIMESTAMP,
                                                            FOREIGN KEY (generator_id) REFERENCES playlist_tag_generators (id) ON DELETE CASCADE
                                                        )`, (err) => {
                                                            if (err) console.error('Error creating playlist_tag_generator_sources table:', err.message);
                                                            else console.log('Playlist_tag_generator_sources table created or already exists');

                                                            checkTrackCountAndResolve();
                                                        });
                                                    });
                                                });
                                            });
                                        });
                                    });
                                });
                            };

                            const addUniqueIdColumnIfNeeded = async () => {
                                if (!hasUniqueIdColumn) {
                                    console.log('Adding unique_id column to tracks table...');
                                    db.run('ALTER TABLE tracks ADD COLUMN unique_id TEXT', async (alterErr) => {
                                        if (alterErr) {
                                            console.error('Error adding unique_id column:', alterErr.message);
                                        } else {
                                            console.log('unique_id column added successfully');
                                            try {
                                                const tracksToUpdate = await new Promise((res, rej) => {
                                                    db.all('SELECT id, path FROM tracks WHERE unique_id IS NULL', [], (err, r) => err ? rej(err) : res(r));
                                                });
                                                console.log(`Found ${tracksToUpdate.length} tracks without unique_id`);
                                                for (const track of tracksToUpdate) {
                                                    try {
                                                        const uniqueId = await fileOps.generateUniqueTrackId(track.path);
                                                        await new Promise((res, rej) => {
                                                            db.run('UPDATE tracks SET unique_id = ? WHERE id = ?', [uniqueId, track.id], (errU) => errU ? rej(errU) : res());
                                                        });
                                                        console.log(`Updated unique_id for track ${track.id}`);
                                                    } catch (uidError) {
                                                        console.error(`Error generating or setting unique_id for track ${track.id}:`, uidError.message);
                                                    }
                                                }
                                                console.log('Finished updating unique_ids for existing tracks');
                                            } catch (popError) {
                                                console.error('Error populating unique_ids:', popError.message);
                                            }
                                        }
                                        createPlaylistTables();
                                    });
                                } else {
                                    console.log('unique_id column already exists');
                                    createPlaylistTables();
                                }
                            };

                            const addTagsColumnIfNeeded = () => {
                                if (!hasTagsColumn) {
                                    console.log('Adding tags column to tracks table...');
                                    db.run('ALTER TABLE tracks ADD COLUMN tags TEXT', (alterErr) => {
                                        if (alterErr) console.error('Error adding tags column:', alterErr.message);
                                        else console.log('Tags column added successfully');
                                        addUniqueIdColumnIfNeeded();
                                    });
                                } else {
                                    console.log('Tags column already exists');
                                    addUniqueIdColumnIfNeeded();
                                }
                            };

                            const runMigrations = () => {
                                if (!hasDurationColumn) {
                                    console.log('Adding duration column to tracks table...');
                                    db.run('ALTER TABLE tracks ADD COLUMN duration REAL', (alterErr) => {
                                        if (alterErr) console.error('Error adding duration column:', alterErr.message);
                                        else console.log('Duration column added successfully');
                                        addTagsColumnIfNeeded();
                                    });
                                } else {
                                    console.log('Duration column already exists');
                                    addTagsColumnIfNeeded();
                                }
                            };

                            runMigrations();
                        });
                    }
                });
            }
        });
    });
}

function getDB() {
    if (!db) {
        // This state should ideally not be reached if setupDatabase is awaited properly.
        console.error("getDB called before database was initialized.");
        throw new Error("Database not initialized. Call setupDatabase first and ensure it completes.");
    }
    return db;
}

function initializeDbIpcHandlers() {
    const currentDB = getDB(); // Ensure DB is available
    if (!currentDB) {
        console.error("DB not initialized when trying to set up DB IPC handlers. Aborting.");
        return;
    }

    // Handler to get a specific track by ID
    ipcMain.handle('track:get', async (_, { trackId }) => {
        console.log(`Getting track with ID: ${trackId}`);
        if (!trackId) return { success: false, error: 'No track ID specified' };
        return new Promise((resolve, reject) => {
            const query = `
                SELECT id, unique_id, path, name, extension, title, artist, duration, tags, createdAt, updatedAt
                FROM tracks
                WHERE id = ?
            `;
            currentDB.get(query, [trackId], (err, track) => {
                if (err) {
                    console.error(`Error getting track ${trackId}:`, err.message);
                    reject(new Error(`Failed to get track: ${err.message}`));
                } else if (!track) {
                    console.error(`Track with ID ${trackId} not found`);
                    reject(new Error(`Track with ID ${trackId} not found`));
                } else {
                    resolve({ success: true, track });
                }
            });
        });
    });

    // Handler to get all playlists containing a specific track
    ipcMain.handle('track:getPlaylists', async (_, { trackId }) => {
        console.log(`Getting playlists containing track ID: ${trackId}`);
        if (!trackId) return { success: false, error: 'No track ID specified' };
        return new Promise((resolve, reject) => {
            const query = `
                SELECT p.id, p.name, p.createdAt
                FROM playlists p
                JOIN playlist_tracks pt ON p.id = pt.playlist_id
                WHERE pt.track_id = ?
                ORDER BY p.name COLLATE NOCASE
            `;
            currentDB.all(query, [trackId], (err, playlists) => {
                if (err) {
                    console.error(`Error getting playlists for track ${trackId}:`, err.message);
                    reject(new Error(`Failed to get playlists for track: ${err.message}`));
                } else {
                    resolve({ success: true, playlists });
                }
            });
        });
    });

    // Create a new playlist
    ipcMain.handle('playlist:create', async (_, { name }) => {
        console.log(`Creating new playlist: ${name}`);
        return new Promise((resolve, reject) => {
            const stmt = currentDB.prepare('INSERT INTO playlists (name, is_generated, updatedAt) VALUES (?, 0, CURRENT_TIMESTAMP)');
            stmt.run(name, function (err) {
                if (err) {
                    console.error('Error creating playlist:', err.message);
                    reject(new Error(`Failed to create playlist: ${err.message}`));
                } else {
                    resolve({ id: this.lastID, name, is_generated: 0, createdAt: new Date().toISOString() });
                }
            });
            stmt.finalize();
        });
    });

    // Get all playlists with track counts
    ipcMain.handle('playlist:getAll', async () => {
        console.log('Getting all playlists with track counts');
        return new Promise((resolve, reject) => {
            const query = `
                SELECT p.id, p.name, p.is_generated, p.createdAt, p.updatedAt,
                       COUNT(pt.track_id) as trackCount,
                       MAX(pt.cycle) as repetitions
                FROM playlists p
                LEFT JOIN playlist_tracks pt ON p.id = pt.playlist_id
                GROUP BY p.id
                ORDER BY p.is_generated ASC, p.name COLLATE NOCASE
            `;
            currentDB.all(query, [], (err, rows) => {
                if (err) {
                    console.error('Error getting playlists with track counts:', err.message);
                    reject(new Error(`Failed to get playlists: ${err.message}`));
                } else {
                    console.log(`Retrieved ${rows.length} playlists`);

                    // Log information about generated playlists
                    const generatedPlaylists = rows.filter(playlist => playlist.is_generated);
                    if (generatedPlaylists.length > 0) {
                        console.log(`Found ${generatedPlaylists.length} generated playlists:`);
                        generatedPlaylists.forEach(playlist => {
                            console.log(`  - ${playlist.name} (ID: ${playlist.id}, Tracks: ${playlist.trackCount}, Repetitions: ${playlist.repetitions || 1})`);
                        });
                    }

                    resolve(rows);
                }
            });
        });
    });

    // Get a specific playlist with its tracks
    ipcMain.handle('playlist:get', async (_, { playlistId }) => {
        console.log(`Getting playlist with ID: ${playlistId}`);
        return new Promise((resolve, reject) => {
            currentDB.get('SELECT id, name, is_generated, createdAt, updatedAt FROM playlists WHERE id = ?', [playlistId], (err, playlist) => {
                if (err || !playlist) {
                    console.error(`Error getting playlist ${playlistId}:`, err ? err.message : 'Not found');
                    reject(new Error(err ? `Failed to get playlist: ${err.message}` : `Playlist with ID ${playlistId} not found`));
                    return;
                }

                // Se è una playlist generata, ottieni anche il numero di ripetizioni
                if (playlist.is_generated) {
                    currentDB.get('SELECT MAX(cycle) as repetitions FROM playlist_tracks WHERE playlist_id = ?', [playlistId], (err_rep, repRow) => {
                        if (err_rep) {
                            console.error(`Error getting repetitions for playlist ${playlistId}:`, err_rep.message);
                            // Continuiamo comunque, non è un errore critico
                        } else if (repRow && repRow.repetitions) {
                            playlist.repetitions = repRow.repetitions;
                            console.log(`Playlist ${playlistId} has ${repRow.repetitions} repetitions`);
                        }

                        // Ora ottieni le tracce
                        getPlaylistTracks(playlist, playlistId, resolve, reject);
                    });
                } else {
                    // Per le playlist normali, ottieni direttamente le tracce
                    getPlaylistTracks(playlist, playlistId, resolve, reject);
                }
            });
        });
    });

    // Funzione helper per ottenere le tracce di una playlist
    function getPlaylistTracks(playlist, playlistId, resolve, reject) {
        console.log(`Getting tracks for playlist ${playlistId} (is_generated: ${playlist.is_generated})`);

        // First, check if there are any tracks in the playlist_tracks table for this playlist
        currentDB.get('SELECT COUNT(*) as count FROM playlist_tracks WHERE playlist_id = ?', [playlistId], (err_count, countRow) => {
            if (err_count) {
                console.error(`Error checking track count for playlist ${playlistId}:`, err_count.message);
            } else {
                console.log(`Playlist ${playlistId} has ${countRow.count} tracks in playlist_tracks table`);

                // If this is a generated playlist, also check cycle information
                if (playlist.is_generated) {
                    currentDB.all('SELECT cycle, COUNT(*) as count FROM playlist_tracks WHERE playlist_id = ? GROUP BY cycle ORDER BY cycle',
                        [playlistId], (err_cycles, cycleRows) => {
                            if (err_cycles) {
                                console.error(`Error checking cycle counts for playlist ${playlistId}:`, err_cycles.message);
                            } else {
                                console.log(`Playlist ${playlistId} cycle counts in database:`, cycleRows);

                                // If no cycles found or empty cycles, log a warning
                                if (!cycleRows || cycleRows.length === 0) {
                                    console.warn(`No cycles found for generated playlist ${playlistId}!`);

                                    // Check if there are any tracks at all
                                    currentDB.all('SELECT track_id FROM playlist_tracks WHERE playlist_id = ? LIMIT 5',
                                        [playlistId], (err_tracks, trackRows) => {
                                            if (err_tracks) {
                                                console.error(`Error checking tracks for playlist ${playlistId}:`, err_tracks.message);
                                            } else if (!trackRows || trackRows.length === 0) {
                                                console.warn(`No tracks found for playlist ${playlistId} in playlist_tracks table!`);
                                            } else {
                                                console.log(`Found ${trackRows.length} tracks for playlist ${playlistId}, but no cycle information.`);

                                                // Fix missing cycle information by setting it to 1
                                                console.log(`Fixing missing cycle information for playlist ${playlistId}`);
                                                currentDB.run('UPDATE playlist_tracks SET cycle = 1 WHERE playlist_id = ? AND (cycle IS NULL OR cycle = 0)',
                                                    [playlistId], function (err_update) {
                                                        if (err_update) {
                                                            console.error(`Error fixing cycle information:`, err_update.message);
                                                        } else {
                                                            console.log(`Fixed cycle information for ${this.changes} tracks`);
                                                        }
                                                    }
                                                );
                                            }
                                        }
                                    );
                                }
                            }
                        }
                    );
                }
            }
        });

        // Use a transaction to ensure consistent data
        currentDB.serialize(() => {
            // For generated playlists, make sure we have the correct cycle information
            if (playlist.is_generated) {
                currentDB.get('SELECT MAX(cycle) as max_cycle FROM playlist_tracks WHERE playlist_id = ?', [playlistId], (err_max, maxCycleRow) => {
                    if (err_max) {
                        console.error(`Error getting max cycle for playlist ${playlistId}:`, err_max.message);
                    } else if (maxCycleRow && maxCycleRow.max_cycle) {
                        console.log(`Playlist ${playlistId} has max cycle: ${maxCycleRow.max_cycle}`);
                        playlist.repetitions = maxCycleRow.max_cycle;
                    } else {
                        console.warn(`No max cycle found for generated playlist ${playlistId}, setting to 1`);
                        playlist.repetitions = 1;
                    }
                });
            }

            // Get all tracks for the playlist with their cycle information
            // Use a LEFT JOIN instead of JOIN to ensure we get all playlist_tracks entries even if the track doesn't exist
            currentDB.all(`
                SELECT pt.id as playlist_track_id, pt.track_id, pt.position, pt.source_playlist_id, pt.cycle,
                       t.id, t.unique_id, t.path, t.name, t.extension, t.title, t.artist, t.duration, t.tags, t.updatedAt,
                       sp.name as source_playlist_name
                FROM playlist_tracks pt
                LEFT JOIN tracks t ON pt.track_id = t.id
                LEFT JOIN playlists sp ON pt.source_playlist_id = sp.id
                WHERE pt.playlist_id = ?
                ORDER BY pt.cycle, pt.position, t.name COLLATE NOCASE
            `, [playlistId], (err_tracks, tracks) => {
                if (err_tracks) {
                    console.error(`Error getting tracks for playlist ${playlistId}:`, err_tracks.message);
                    reject(new Error(`Failed to get playlist tracks: ${err_tracks.message}`));
                } else {
                    // Log dei cicli per debug
                    const cycleCounts = {};
                    tracks.forEach(track => {
                        const cycle = track.cycle || 1;
                        cycleCounts[cycle] = (cycleCounts[cycle] || 0) + 1;
                    });
                    console.log(`Playlist ${playlistId} tracks by cycle from query:`, cycleCounts);

                    // Log the first few tracks for debugging
                    if (tracks.length > 0) {
                        console.log(`First track from playlist ${playlistId}:`, JSON.stringify(tracks[0], null, 2));
                    } else {
                        console.warn(`No tracks found for playlist ${playlistId} in query result!`);
                    }

                    // Filter out any tracks that don't have a valid track record
                    const validTracks = tracks.filter(track => track.id !== null);
                    if (validTracks.length < tracks.length) {
                        console.warn(`Filtered out ${tracks.length - validTracks.length} invalid tracks from playlist ${playlistId}`);
                    }

                    // Make sure cycle information is properly included
                    validTracks.forEach(track => {
                        if (track.cycle === undefined || track.cycle === null) {
                            console.warn(`Track ${track.id} in playlist ${playlistId} has no cycle information, defaulting to 1`);
                            track.cycle = 1;
                        }
                    });

                    // For generated playlists, ensure the repetitions property is set
                    if (playlist.is_generated && validTracks.length > 0) {
                        // Calculate max cycle from tracks if not already set
                        if (!playlist.repetitions) {
                            const maxCycle = Math.max(...validTracks.map(track => track.cycle || 1));
                            console.log(`Calculated max cycle from tracks: ${maxCycle}`);
                            playlist.repetitions = maxCycle;
                        }

                        console.log(`Playlist ${playlistId} has ${playlist.repetitions} repetitions and ${validTracks.length} tracks`);
                    }

                    resolve({ ...playlist, tracks: validTracks });
                }
            });
        });
    }

    // Add tracks to a playlist
    ipcMain.handle('playlist:addTracks', async (_, { playlistId, trackIds }) => {
        console.log(`Adding tracks to playlist ${playlistId}:`, trackIds);
        if (!Array.isArray(trackIds) || trackIds.length === 0) return { success: false, error: 'No tracks specified' };
        return new Promise((resolve, reject) => {
            currentDB.get('SELECT MAX(position) as maxPosition FROM playlist_tracks WHERE playlist_id = ?', [playlistId], (err, positionRow) => {
                if (err) {
                    reject(new Error(`Failed to get max position: ${err.message}`));
                    return;
                }
                let nextPosition = (positionRow.maxPosition || 0) + 1;
                currentDB.serialize(() => {
                    currentDB.run('BEGIN TRANSACTION', (dberr) => { if (dberr) return reject(new Error(`DB TX BEGIN Error: ${dberr.message}`)); });
                    const stmt = currentDB.prepare(`INSERT INTO playlist_tracks (playlist_id, track_id, position, cycle) VALUES (?, ?, ?, ?) ON CONFLICT(playlist_id, track_id, cycle) DO NOTHING`);
                    let insertCount = 0;
                    for (const trackId of trackIds) {
                        stmt.run(playlistId, trackId, nextPosition++, 1, function (err_run) {
                            if (err_run) console.error(`Error inserting track ${trackId}:`, err_run.message); // Log error but continue transaction
                            else if (this.changes > 0) insertCount++;
                        });
                    }
                    stmt.finalize((err_finalize) => {
                        if (err_finalize) { // Finalize error, attempt rollback
                            console.error('Finalize error in addTracks:', err_finalize.message);
                            currentDB.run('ROLLBACK', (dberr_rb) => { if (dberr_rb) console.error('Rollback error', dberr_rb.message); });
                            return reject(new Error(`DB Finalize Error: ${err_finalize.message}`));
                        }
                        currentDB.run('COMMIT', (dberr_commit) => {
                            if (dberr_commit) {
                                console.error('Commit error in addTracks:', dberr_commit.message);
                                currentDB.run('ROLLBACK', (dberr_rb) => { if (dberr_rb) console.error('Rollback error on commit fail', dberr_rb.message); });
                                return reject(new Error(`DB Commit Error: ${dberr_commit.message}`));
                            }
                            resolve({ success: true, count: insertCount });
                        });
                    });
                });
            });
        });
    });

    // Remove tracks from a playlist
    ipcMain.handle('playlist:removeTracks', async (_, { playlistId, trackIds }) => {
        console.log(`Removing tracks from playlist ${playlistId}:`, trackIds);
        if (!Array.isArray(trackIds) || trackIds.length === 0) return { success: false, error: 'No tracks specified' };
        return new Promise((resolve, reject) => {
            const placeholders = trackIds.map(() => '?').join(',');
            currentDB.run(`DELETE FROM playlist_tracks WHERE playlist_id = ? AND track_id IN (${placeholders})`, [playlistId, ...trackIds], function (err) {
                if (err) {
                    reject(new Error(`Failed to remove tracks: ${err.message}`));
                } else {
                    resolve({ success: true, count: this.changes });
                }
            });
        });
    });

    // Rename a playlist
    ipcMain.handle('playlist:rename', async (_, { playlistId, name }) => {
        console.log(`Renaming playlist ${playlistId} to "${name}"`);
        return new Promise((resolve, reject) => {
            const stmt = currentDB.prepare('UPDATE playlists SET name = ?, updatedAt = CURRENT_TIMESTAMP WHERE id = ?');
            stmt.run(name, playlistId, function (err) {
                if (err) {
                    reject(new Error(`Failed to rename playlist: ${err.message}`));
                } else {
                    resolve({ success: this.changes > 0, playlistId, name });
                }
            });
            stmt.finalize();
        });
    });

    // Delete a playlist
    ipcMain.handle('playlist:delete', async (_, { playlistId }) => {
        console.log(`Deleting playlist with ID: ${playlistId}`);
        return new Promise((resolve, reject) => {
            currentDB.serialize(() => {
                currentDB.run('BEGIN TRANSACTION');
                currentDB.run('DELETE FROM playlist_tracks WHERE playlist_id = ?', [playlistId], (err) => {
                    if (err) {
                        currentDB.run('ROLLBACK');
                        return reject(new Error(`Failed to delete playlist tracks: ${err.message}`));
                    }
                    currentDB.run('DELETE FROM playlists WHERE id = ?', [playlistId], function (err_del_pl) {
                        if (err_del_pl) {
                            currentDB.run('ROLLBACK');
                            return reject(new Error(`Failed to delete playlist: ${err_del_pl.message}`));
                        }
                        currentDB.run('COMMIT');
                        resolve({ success: true, deleted: this.changes > 0 });
                    });
                });
            });
        });
    });

    // Swap tracks with cycle information for tag-generated playlists
    ipcMain.handle('playlist:swapTracksWithCycles', async (_, { playlistId, track1Id, track1Position, track1Cycle, track2Id, track2Position, track2Cycle }) => {
        console.log(`Swapping tracks with cycles: ${track1Id} (pos ${track1Position}, cycle ${track1Cycle}) <-> ${track2Id} (pos ${track2Position}, cycle ${track2Cycle})`);

        return new Promise((resolve, reject) => {
            // Get all tracks in the playlist with their current information
            currentDB.all(`
                SELECT track_id, position, source_playlist_id, cycle
                FROM playlist_tracks
                WHERE playlist_id = ?
                ORDER BY position
            `, [playlistId], (err, tracks) => {
                if (err) return reject(new Error(`Failed to get tracks: ${err.message}`));

                // Create a new array with the swapped tracks and updated cycle information
                const updatedTracks = tracks.map(track => {
                    if (track.track_id === track1Id) {
                        // Track 1 goes to track 2's position and cycle
                        return {
                            track_id: track1Id,
                            position: track2Position,
                            source_playlist_id: track.source_playlist_id,
                            cycle: track2Cycle
                        };
                    } else if (track.track_id === track2Id) {
                        // Track 2 goes to track 1's position and cycle
                        return {
                            track_id: track2Id,
                            position: track1Position,
                            source_playlist_id: track.source_playlist_id,
                            cycle: track1Cycle
                        };
                    } else {
                        // Other tracks keep their current information
                        return {
                            track_id: track.track_id,
                            position: track.position,
                            source_playlist_id: track.source_playlist_id,
                            cycle: track.cycle || 1
                        };
                    }
                });

                // Sort by position to maintain order
                updatedTracks.sort((a, b) => a.position - b.position);

                // Update the database with the new track order and cycle information
                currentDB.serialize(() => {
                    currentDB.run('BEGIN TRANSACTION');
                    currentDB.run('DELETE FROM playlist_tracks WHERE playlist_id = ?', [playlistId], (err_del) => {
                        if (err_del) {
                            currentDB.run('ROLLBACK');
                            return reject(new Error(`Failed to delete existing tracks: ${err_del.message}`));
                        }

                        const stmt = currentDB.prepare('INSERT INTO playlist_tracks (playlist_id, track_id, position, source_playlist_id, cycle) VALUES (?, ?, ?, ?, ?)');
                        updatedTracks.forEach(track => {
                            stmt.run(playlistId, track.track_id, track.position, track.source_playlist_id, track.cycle);
                        });

                        stmt.finalize((err_fin) => {
                            if (err_fin) {
                                currentDB.run('ROLLBACK');
                                return reject(new Error(`Failed to finalize statement: ${err_fin.message}`));
                            }

                            currentDB.run('COMMIT', (err_commit) => {
                                if (err_commit) {
                                    currentDB.run('ROLLBACK');
                                    return reject(new Error(`Failed to commit transaction: ${err_commit.message}`));
                                }

                                console.log(`Successfully swapped tracks ${track1Id} and ${track2Id} with cycle updates`);
                                resolve({
                                    success: true,
                                    message: `Successfully swapped tracks ${track1Id} and ${track2Id}`
                                });
                            });
                        });
                    });
                });
            });
        });
    });

    // Reorder tracks in a playlist
    ipcMain.handle('playlist:reorderTracks', async (_, { playlistId, trackIds }) => {
        console.log(`Reordering tracks in playlist ${playlistId}:`, trackIds);
        if (!Array.isArray(trackIds) || trackIds.length === 0) return { success: false, error: 'No tracks specified' };
        return new Promise((resolve, reject) => {
            currentDB.all('SELECT track_id, source_playlist_id, cycle FROM playlist_tracks WHERE playlist_id = ?', [playlistId], (err, rows) => {
                if (err) return reject(new Error(`Failed to get source playlist IDs: ${err.message}`));
                const trackSourceMap = {};
                const trackCycleMap = {};
                rows.forEach(row => {
                    trackSourceMap[row.track_id] = row.source_playlist_id;
                    trackCycleMap[row.track_id] = row.cycle || 1;
                });

                currentDB.serialize(() => {
                    currentDB.run('BEGIN TRANSACTION');
                    currentDB.run('DELETE FROM playlist_tracks WHERE playlist_id = ?', [playlistId], (err_del) => {
                        if (err_del) {
                            currentDB.run('ROLLBACK');
                            return reject(new Error(`Failed to delete existing tracks: ${err_del.message}`));
                        }
                        const stmt = currentDB.prepare('INSERT INTO playlist_tracks (playlist_id, track_id, position, source_playlist_id, cycle) VALUES (?, ?, ?, ?, ?)');
                        trackIds.forEach((trackId, index) => {
                            stmt.run(playlistId, trackId, index + 1, trackSourceMap[trackId] || null, trackCycleMap[trackId] || 1);
                        });
                        stmt.finalize((err_fin) => {
                            if (err_fin) {
                                currentDB.run('ROLLBACK');
                                return reject(new Error(`Failed to finalize reorder: ${err_fin.message}`));
                            }
                            currentDB.run('COMMIT');
                            resolve({ success: true, count: trackIds.length });
                        });
                    });
                });
            });
        });
    });

    // Default Tags Handlers
    ipcMain.handle('default-tags:get-all', async () => {
        return new Promise((resolve, reject) => {
            currentDB.all('SELECT id, name, color FROM default_tags ORDER BY name COLLATE NOCASE', [], (err, rows) => {
                if (err) reject(new Error(`Failed to get default tags: ${err.message}`));
                else resolve(rows);
            });
        });
    });
    ipcMain.handle('default-tags:add', async (_, { name, color }) => {
        return new Promise((resolve, reject) => {
            const stmt = currentDB.prepare('INSERT INTO default_tags (name, color) VALUES (?, ?)');
            stmt.run(name, color || null, function (err) {
                if (err) reject(new Error(`Failed to add default tag: ${err.message}`));
                else resolve({ id: this.lastID, name, color });
            });
            stmt.finalize();
        });
    });
    ipcMain.handle('default-tags:update', async (_, { id, name, color }) => {
        return new Promise((resolve, reject) => {
            const stmt = currentDB.prepare('UPDATE default_tags SET name = ?, color = ?, updatedAt = CURRENT_TIMESTAMP WHERE id = ?');
            stmt.run(name, color || null, id, function (err) {
                if (err) reject(new Error(`Failed to update default tag: ${err.message}`));
                else resolve({ success: this.changes > 0, id, name, color });
            });
            stmt.finalize();
        });
    });
    ipcMain.handle('default-tags:delete', async (_, { id }) => {
        return new Promise((resolve, reject) => {
            const stmt = currentDB.prepare('DELETE FROM default_tags WHERE id = ?');
            stmt.run(id, function (err) {
                if (err) reject(new Error(`Failed to delete default tag: ${err.message}`));
                else resolve({ success: this.changes > 0 });
            });
            stmt.finalize();
        });
    });

    // Playlist Generator Handlers
    ipcMain.handle('playlist-generator:create', async (_, { name, targetDuration }) => {
        return new Promise((resolve, reject) => {
            const stmt = currentDB.prepare('INSERT INTO playlist_generators (name, target_duration, updatedAt) VALUES (?, ?, CURRENT_TIMESTAMP)');
            stmt.run(name, targetDuration, function (err) {
                if (err) reject(new Error(`Failed to create playlist generator: ${err.message}`));
                else resolve({ id: this.lastID, name, targetDuration, createdAt: new Date().toISOString() });
            });
            stmt.finalize();
        });
    });
    ipcMain.handle('playlist-generator:getAll', async () => {
        return new Promise((resolve, reject) => {
            currentDB.all(`SELECT id, name, target_duration as targetDuration, createdAt, updatedAt FROM playlist_generators ORDER BY name COLLATE NOCASE`, [], (err, rows) => {
                if (err) reject(new Error(`Failed to get playlist generators: ${err.message}`));
                else resolve(rows);
            });
        });
    });
    ipcMain.handle('playlist-generator:addSource', async (_, { generatorId, playlistId, trackCount, position }) => {
        return new Promise((resolve, reject) => {
            currentDB.get('SELECT id FROM playlist_generator_sources WHERE generator_id = ? AND playlist_id = ?', [generatorId, playlistId], (err, row) => {
                if (err) return reject(new Error(`Failed to check existing source: ${err.message}`));
                if (row) {
                    const stmt = currentDB.prepare('UPDATE playlist_generator_sources SET track_count = ?, position = ? WHERE id = ?');
                    stmt.run(trackCount, position, row.id, function (err_upd) {
                        if (err_upd) reject(new Error(`Failed to update source: ${err_upd.message}`));
                        else resolve({ id: row.id, generatorId, playlistId, trackCount, position, updated: true });
                    });
                    stmt.finalize();
                } else {
                    const stmt = currentDB.prepare('INSERT INTO playlist_generator_sources (generator_id, playlist_id, track_count, position) VALUES (?, ?, ?, ?)');
                    stmt.run(generatorId, playlistId, trackCount, position, function (err_ins) {
                        if (err_ins) reject(new Error(`Failed to add source: ${err_ins.message}`));
                        else resolve({ id: this.lastID, generatorId, playlistId, trackCount, position, updated: false });
                    });
                    stmt.finalize();
                }
            });
        });
    });
    ipcMain.handle('playlist-generator:removeSource', async (_, { generatorId, playlistId }) => {
        return new Promise((resolve, reject) => {
            currentDB.run('DELETE FROM playlist_generator_sources WHERE generator_id = ? AND playlist_id = ?', [generatorId, playlistId], function (err) {
                if (err) reject(new Error(`Failed to remove source: ${err.message}`));
                else resolve({ success: true, changes: this.changes });
            });
        });
    });
    ipcMain.handle('playlist-generator:getSources', async (_, { generatorId }) => {
        return new Promise((resolve, reject) => {
            currentDB.all(`SELECT pgs.id, pgs.generator_id as generatorId, pgs.playlist_id as playlistId, pgs.track_count as trackCount, pgs.position, p.name as playlistName FROM playlist_generator_sources pgs JOIN playlists p ON pgs.playlist_id = p.id WHERE pgs.generator_id = ? ORDER BY pgs.position, p.name COLLATE NOCASE`, [generatorId], (err, rows) => {
                if (err) reject(new Error(`Failed to get sources: ${err.message}`));
                else resolve(rows);
            });
        });
    });


    ipcMain.handle('playlist-generator:generate', async (_, { generatorId, playlistName }) => {
        console.log(`Generating playlist "${playlistName}" from generator ${generatorId}`);
        try {

            const generator = await new Promise((res, rej) => currentDB.get('SELECT id, name, target_duration as targetDuration FROM playlist_generators WHERE id = ?', [generatorId], (e, r) => e ? rej(e) : (r ? res(r) : rej(new Error('Generator not found')))));
            const sources = await new Promise((res, rej) => currentDB.all(`SELECT pgs.playlist_id as playlistId, pgs.track_count as trackCount, pgs.position FROM playlist_generator_sources pgs WHERE pgs.generator_id = ? ORDER BY pgs.position, pgs.id`, [generatorId], (e, r) => e ? rej(e) : res(r)));
            if (sources.length === 0) return { success: false, error: 'No source playlists configured' };

            const newPlaylist = await new Promise((res, rej) => {
                const stmt = currentDB.prepare('INSERT INTO playlists (name, is_generated, updatedAt) VALUES (?, 1, CURRENT_TIMESTAMP)');
                stmt.run(playlistName, function (e) { e ? rej(e) : res({ id: this.lastID, name: playlistName, is_generated: 1 }); });
                stmt.finalize();
            });

            let position = 1;
            let totalDuration = 0;
            const targetDurationSeconds = generator.targetDuration * 60;
            const availableTracksBySource = {};
            for (const source of sources) {
                availableTracksBySource[source.playlistId] = await new Promise((res, rej) => currentDB.all(`SELECT t.id, t.duration FROM tracks t JOIN playlist_tracks pt ON t.id = pt.track_id WHERE pt.playlist_id = ? ORDER BY RANDOM()`, [source.playlistId], (e, r) => e ? rej(e) : res(r)));
            }
            const orderedSources = [...sources].sort((a, b) => a.position - b.position);
            let usedTrackIds = new Set();

            // Calcola quante tracce totali abbiamo bisogno per ogni ciclo
            const tracksPerCycle = orderedSources.reduce((sum, source) => sum + source.trackCount, 0);
            console.log(`Each cycle requires ${tracksPerCycle} tracks (${orderedSources.length} sources)`);

            // Verifica se abbiamo abbastanza tracce per più cicli
            const totalAvailableTracks = orderedSources.reduce((sum, source) => {
                return sum + availableTracksBySource[source.playlistId].length;
            }, 0);
            console.log(`Total available tracks: ${totalAvailableTracks}`);

            // Calcola quanti cicli possiamo creare con le tracce disponibili
            const maxPossibleCycles = Math.floor(totalAvailableTracks / tracksPerCycle);
            console.log(`Maximum possible cycles based on available tracks: ${maxPossibleCycles}`);

            // Calcola la durata di un singolo ciclo
            let cycleDuration = 0;

            // Crea un array di tracce per ogni ciclo
            const tracksByCycle = [];

            // Funzione per selezionare tracce per un ciclo
            const selectTracksForCycle = (cycleNumber) => {
                const cycleTrackIds = new Set();
                const cycleTracks = [];
                let cycleTotalDuration = 0;

                for (const source of orderedSources) {
                    let tracksAddedFromSource = 0;

                    // Filtra le tracce disponibili per questa fonte, escludendo quelle già usate
                    const availableTracks = availableTracksBySource[source.playlistId].filter(
                        track => !usedTrackIds.has(track.id)
                    );

                    if (availableTracks.length === 0) {
                        console.warn(`No more available tracks from source ${source.playlistId} for cycle ${cycleNumber}`);
                        continue;
                    }

                    for (const track of availableTracks) {
                        if (tracksAddedFromSource >= source.trackCount) break;
                        if (!cycleTrackIds.has(track.id) && !usedTrackIds.has(track.id)) {
                            cycleTracks.push({
                                id: track.id,
                                duration: track.duration,
                                sourcePlaylistId: source.playlistId
                            });
                            cycleTrackIds.add(track.id);
                            usedTrackIds.add(track.id);
                            cycleTotalDuration += (track.duration || 180);
                            tracksAddedFromSource++;
                        }
                    }

                    if (tracksAddedFromSource < source.trackCount) {
                        console.warn(`Could only add ${tracksAddedFromSource}/${source.trackCount} tracks from source ${source.playlistId} for cycle ${cycleNumber}`);
                    }
                }

                return { tracks: cycleTracks, duration: cycleTotalDuration };
            };

            // Seleziona tracce per il primo ciclo per calcolare la durata
            const firstCycle = selectTracksForCycle(1);
            tracksByCycle.push(firstCycle.tracks);
            cycleDuration = firstCycle.duration;

            // Calcola quanti cicli sono necessari per raggiungere la durata target
            let repetitions = 1;

            // Assicuriamoci che la durata totale sia valida
            if (cycleDuration <= 0) {
                // Se la durata totale non è valida, stimiamo una durata media di 3 minuti per traccia
                cycleDuration = tracksPerCycle * 180;
                console.log(`Invalid cycle duration, using estimated duration: ${cycleDuration}s based on ${tracksPerCycle} tracks`);
            }

            if (targetDurationSeconds > 0 && cycleDuration > 0) {
                if (cycleDuration < targetDurationSeconds) {
                    // Calcola quante ripetizioni sono necessarie per raggiungere la durata target
                    repetitions = Math.ceil(targetDurationSeconds / cycleDuration);
                    console.log(`Target duration: ${targetDurationSeconds}s, Cycle duration: ${cycleDuration}s, Cycles needed: ${repetitions}`);
                } else {
                    // Se la durata del ciclo è già maggiore della durata target, usiamo un solo ciclo
                    console.log(`Cycle duration ${cycleDuration}s already exceeds target duration ${targetDurationSeconds}s, using 1 cycle`);
                }

                // Limita il numero di ripetizioni in base alle tracce disponibili
                repetitions = Math.min(repetitions, maxPossibleCycles);
                console.log(`Limited to ${repetitions} cycles based on available tracks`);
            } else {
                console.log(`Using default 1 cycle (targetDuration: ${targetDurationSeconds}s, cycleDuration: ${cycleDuration}s)`);
            }

            // Seleziona tracce per i cicli rimanenti
            for (let i = 2; i <= repetitions; i++) {
                const cycle = selectTracksForCycle(i);
                tracksByCycle.push(cycle.tracks);
            }

            // Calcola la durata totale di tutti i cicli
            totalDuration = tracksByCycle.reduce((sum, cycleTracks) => {
                return sum + cycleTracks.reduce((cycleSum, track) => cycleSum + (track.duration || 180), 0);
            }, 0);

            // Usa una transazione per inserire tutte le tracce in modo efficiente
            await new Promise((res, rej) => {
                currentDB.serialize(() => {
                    currentDB.run('BEGIN TRANSACTION', (err_begin) => {
                        if (err_begin) {
                            console.error('BEGIN TRANSACTION error:', err_begin.message);
                            return rej(new Error(`Begin TX failed: ${err_begin.message}`));
                        }
                    });

                    // Elimina eventuali tracce esistenti per questa playlist (per sicurezza)
                    currentDB.run('DELETE FROM playlist_tracks WHERE playlist_id = ?', [newPlaylist.id], function (err_del) {
                        if (err_del) {
                            console.error('Error deleting existing tracks:', err_del.message);
                        } else {
                            console.log(`Deleted ${this.changes} existing tracks from playlist ${newPlaylist.id}`);
                        }
                    });

                    // Prepariamo tutti i valori da inserire in un unico array
                    const allValues = [];
                    const allPlaceholders = [];

                    // Inserisci le tracce per ogni ciclo
                    for (let cycleIndex = 0; cycleIndex < tracksByCycle.length; cycleIndex++) {
                        const cycleNumber = cycleIndex + 1; // Cycles are 1-based
                        const cycleTracks = tracksByCycle[cycleIndex];

                        console.log(`Preparing ${cycleTracks.length} tracks for cycle ${cycleNumber}/${tracksByCycle.length}`);

                        for (const track of cycleTracks) {
                            const currentPosition = position++;

                            // Aggiungi i valori all'array
                            allValues.push(
                                newPlaylist.id,
                                track.id,
                                currentPosition,
                                track.sourcePlaylistId,
                                cycleNumber
                            );

                            // Aggiungi i placeholder per questa riga
                            allPlaceholders.push('(?, ?, ?, ?, ?)');

                            console.log(`Prepared track ${track.id} for cycle ${cycleNumber} at position ${currentPosition}`);
                        }
                    }

                    // Crea una singola query di inserimento con tutti i valori
                    const insertSQL = `INSERT INTO playlist_tracks
                        (playlist_id, track_id, position, source_playlist_id, cycle)
                        VALUES ${allPlaceholders.join(', ')}`;

                    console.log(`Executing bulk insert with ${allPlaceholders.length} tracks...`);

                    // Esegui la query di inserimento
                    currentDB.run(insertSQL, allValues, function (err_run) {
                        if (err_run) {
                            console.error('Error in bulk insert:', err_run.message);
                        } else {
                            console.log(`Successfully inserted ${this.changes} tracks into playlist ${newPlaylist.id}`);
                        }

                        // Verifica che i cicli siano stati salvati correttamente
                        currentDB.all(`SELECT cycle, COUNT(*) as count FROM playlist_tracks WHERE playlist_id = ? GROUP BY cycle ORDER BY cycle`,
                            [newPlaylist.id], (err, rows) => {
                                if (err) {
                                    console.error('Error checking cycle counts:', err.message);
                                } else {
                                    console.log('Cycle counts in database:');
                                    rows.forEach(row => {
                                        console.log(`Cycle ${row.cycle}: ${row.count} tracks`);
                                    });
                                }
                            }
                        );
                    });

                    // Commit della transazione
                    currentDB.run('COMMIT', (err_commit) => {
                        if (err_commit) {
                            console.error('COMMIT error:', err_commit.message);
                            currentDB.run('ROLLBACK');
                            return rej(new Error(`COMMIT failed: ${err_commit.message}`));
                        }
                        res();
                    });
                });
            });
            const finalPlaylist = await new Promise((res, rej) => currentDB.get('SELECT id, name, createdAt, updatedAt FROM playlists WHERE id = ?', [newPlaylist.id], (e, r) => e ? rej(e) : (r ? res(r) : rej(new Error('Final playlist not found')))));
            const trackCountRow = await new Promise((res, rej) => currentDB.get('SELECT COUNT(*) as trackCount FROM playlist_tracks WHERE playlist_id = ?', [newPlaylist.id], (e, r) => e ? rej(e) : res(r)));
            finalPlaylist.trackCount = trackCountRow ? trackCountRow.trackCount : 0;

            // Aggiungi il numero di ripetizioni alla playlist
            finalPlaylist.repetitions = tracksByCycle.length;

            // Log del numero di tracce per ciclo
            console.log(`Created ${tracksByCycle.length} cycles with the following track counts:`);
            tracksByCycle.forEach((cycleTracks, index) => {
                console.log(`Cycle ${index + 1}: ${cycleTracks.length} tracks`);
            });

            // Calculate actual duration in minutes and seconds for display
            const actualTotalDuration = totalDuration; // totalDuration already includes all cycles
            const actualDurationMinutes = Math.floor(actualTotalDuration / 60);
            const actualDurationSeconds = Math.floor(actualTotalDuration % 60);

            // Calculate target duration in minutes and seconds for comparison
            const targetDurationMinutes = Math.floor(targetDurationSeconds / 60);
            const targetDurationSecondsRemainder = Math.floor(targetDurationSeconds % 60);

            console.log(`Playlist generated with actual duration: ${actualDurationMinutes}m ${actualDurationSeconds}s vs target: ${targetDurationMinutes}m ${targetDurationSecondsRemainder}s`);

            return {
                success: true,
                playlist: finalPlaylist,
                totalDuration: actualTotalDuration,
                actualDurationMinutes,
                actualDurationSeconds,
                targetDurationMinutes,
                targetDurationSeconds: targetDurationSecondsRemainder,
                repetitions: tracksByCycle.length, // Use actual number of cycles created
                targetDurationOriginal: targetDurationSeconds,
                cycleCount: tracksByCycle.length,
                tracksPerCycle: tracksByCycle.map(cycle => cycle.length)
            };
        } catch (error) {
            console.error('Error generating playlist:', error);
            return { success: false, error: error.message };
        }
    });

    // Reorder cycles in a generated playlist
    ipcMain.handle('playlist:reorderCycles', async (_, { playlistId, cycleOrder }) => {
        console.log(`Reordering cycles in playlist ${playlistId}:`, cycleOrder);
        if (!Array.isArray(cycleOrder) || cycleOrder.length === 0) return { success: false, error: 'No cycle order specified' };

        return new Promise((resolve, reject) => {
            // First, get all tracks in the playlist with their current cycle and position
            currentDB.all(`
                SELECT track_id, position, source_playlist_id, cycle
                FROM playlist_tracks
                WHERE playlist_id = ?
                ORDER BY cycle, position
            `, [playlistId], (err, tracks) => {
                if (err) return reject(new Error(`Failed to get tracks: ${err.message}`));

                // Group tracks by cycle
                const tracksByCycle = {};
                tracks.forEach(track => {
                    const cycle = track.cycle || 1;
                    if (!tracksByCycle[cycle]) {
                        tracksByCycle[cycle] = [];
                    }
                    tracksByCycle[cycle].push(track);
                });

                // Create a new array of tracks with updated cycle numbers
                const updatedTracks = [];
                let position = 1;

                // Add tracks in the new cycle order
                cycleOrder.forEach((oldCycle, newCycleIndex) => {
                    const newCycle = newCycleIndex + 1; // New cycles are 1-based
                    const tracksInCycle = tracksByCycle[oldCycle] || [];

                    // Sort tracks within the cycle by their original position
                    tracksInCycle.sort((a, b) => a.position - b.position);

                    // Add tracks with the new cycle number
                    tracksInCycle.forEach(track => {
                        updatedTracks.push({
                            track_id: track.track_id,
                            position: position++,
                            source_playlist_id: track.source_playlist_id,
                            cycle: newCycle
                        });
                    });
                });

                // Update the database with the new track order and cycle numbers
                currentDB.serialize(() => {
                    currentDB.run('BEGIN TRANSACTION');
                    currentDB.run('DELETE FROM playlist_tracks WHERE playlist_id = ?', [playlistId], (err_del) => {
                        if (err_del) {
                            currentDB.run('ROLLBACK');
                            return reject(new Error(`Failed to delete existing tracks: ${err_del.message}`));
                        }

                        const stmt = currentDB.prepare('INSERT INTO playlist_tracks (playlist_id, track_id, position, source_playlist_id, cycle) VALUES (?, ?, ?, ?, ?)');
                        updatedTracks.forEach(track => {
                            stmt.run(playlistId, track.track_id, track.position, track.source_playlist_id, track.cycle);
                        });

                        stmt.finalize((err_fin) => {
                            if (err_fin) {
                                currentDB.run('ROLLBACK');
                                return reject(new Error(`Failed to finalize statement: ${err_fin.message}`));
                            }

                            currentDB.run('COMMIT', (err_commit) => {
                                if (err_commit) {
                                    currentDB.run('ROLLBACK');
                                    return reject(new Error(`Failed to commit transaction: ${err_commit.message}`));
                                }

                                resolve({
                                    success: true,
                                    message: `Successfully reordered ${updatedTracks.length} tracks in ${cycleOrder.length} cycles`
                                });
                            });
                        });
                    });
                });
            });
        });
    });

    ipcMain.handle('playlist:replace-track', async (_, { playlistId, trackId, sourcePlaylistId }) => {
        console.log(`Replacing track ${trackId} in playlist ${playlistId} from source ${sourcePlaylistId}`);
        try {
            const sourceTracks = await new Promise((res, rej) => currentDB.all(`SELECT t.id FROM tracks t JOIN playlist_tracks pt ON t.id = pt.track_id WHERE pt.playlist_id = ? ORDER BY RANDOM()`, [sourcePlaylistId], (e, r) => e ? rej(e) : res(r)));
            if (!sourceTracks || sourceTracks.length === 0) return { success: false, error: 'Source playlist empty' };
            const playlistTracks = await new Promise((res, rej) => currentDB.all(`SELECT track_id, position, cycle FROM playlist_tracks WHERE playlist_id = ?`, [playlistId], (e, r) => e ? rej(e) : res(r)));
            const trackToReplace = playlistTracks.find(t => t.track_id === trackId);
            if (!trackToReplace) return { success: false, error: 'Track not found in playlist' };
            const existingTrackIds = playlistTracks.map(track => track.track_id);
            const availableTracks = sourceTracks.filter(track => !existingTrackIds.includes(track.id) || track.id === trackId);
            if (availableTracks.length === 0) return { success: false, error: 'No available tracks for replacement' };
            let randomTrack = availableTracks[Math.floor(Math.random() * availableTracks.length)];
            if (randomTrack.id === trackId && availableTracks.length > 1) {
                availableTracks.splice(availableTracks.findIndex(t => t.id === trackId), 1);
                randomTrack = availableTracks[Math.floor(Math.random() * availableTracks.length)];
            } else if (randomTrack.id === trackId && availableTracks.length === 1) {
                return { success: false, error: 'No other tracks available in source' };
            }

            await new Promise((res, rej) => currentDB.serialize(() => {
                currentDB.run('BEGIN TRANSACTION', (err_begin) => {
                    if (err_begin) {
                        console.error('BEGIN TRANSACTION error:', err_begin.message);
                        return rej(new Error(`Begin TX failed: ${err_begin.message}`));
                    }
                });
                currentDB.run('DELETE FROM playlist_tracks WHERE playlist_id = ? AND track_id = ?', [playlistId, trackId], (e_del) => {
                    if (e_del) {
                        console.error('DELETE error:', e_del.message);
                        currentDB.run('ROLLBACK');
                        return rej(new Error(`DELETE failed: ${e_del.message}`));
                    }
                });
                const stmt = currentDB.prepare('INSERT INTO playlist_tracks (playlist_id, track_id, position, source_playlist_id, cycle) VALUES (?, ?, ?, ?, ?)');
                stmt.run(playlistId, randomTrack.id, trackToReplace.position, sourcePlaylistId, trackToReplace.cycle || 1, (e_run) => {
                    if (e_run) {
                        console.error('INSERT stmt.run error:', e_run.message);
                        currentDB.run('ROLLBACK');
                        return rej(new Error(`INSERT failed: ${e_run.message}`));
                    }
                });
                stmt.finalize((e_fin) => {
                    if (e_fin) {
                        console.error('INSERT stmt.finalize error:', e_fin.message);
                        currentDB.run('ROLLBACK');
                        return rej(new Error(`FINALIZE failed: ${e_fin.message}`));
                    }
                });
                currentDB.run('COMMIT', (e_commit) => {
                    if (e_commit) {
                        console.error('COMMIT error:', e_commit.message);
                        currentDB.run('ROLLBACK');
                        return rej(new Error(`COMMIT failed: ${e_commit.message}`));
                    } else {
                        res();
                    }
                });
            }));
            return { success: true, oldTrackId: trackId, newTrackId: randomTrack.id, position: trackToReplace.position };
        } catch (error) {
            console.error('Error replacing track:', error);
            return { success: false, error: error.message };
        }
    });

    // Handler for replacing a track in a playlist based on tags
    ipcMain.handle('playlist:replace-track-by-tags', async (_, { playlistId, trackId, matchMode, selectedTags }) => {
        console.log(`Replacing track ${trackId} in playlist ${playlistId} using tag match mode: ${matchMode}`);
        try {
            // Get the track to replace and its tags
            const trackToReplaceInfo = await new Promise((res, rej) => {
                currentDB.get(`
                    SELECT t.id, t.tags, pt.position, pt.cycle
                    FROM tracks t
                    JOIN playlist_tracks pt ON t.id = pt.track_id
                    WHERE pt.playlist_id = ? AND t.id = ?
                `, [playlistId, trackId], (e, r) => e ? rej(e) : res(r));
            });

            if (!trackToReplaceInfo) {
                return { success: false, error: 'Track not found in playlist' };
            }

            // Get all existing track IDs in the playlist to avoid duplicates
            const playlistTracks = await new Promise((res, rej) => {
                currentDB.all(`SELECT track_id FROM playlist_tracks WHERE playlist_id = ?`,
                    [playlistId], (e, r) => e ? rej(e) : res(r));
            });
            const existingTrackIds = playlistTracks.map(track => track.track_id);

            // Parse the tags of the track to replace
            const trackTags = trackToReplaceInfo.tags ?
                trackToReplaceInfo.tags.split(',').map(tag => tag.trim().toLowerCase()) : [];

            if (trackTags.length === 0) {
                return { success: false, error: 'Track has no tags for matching' };
            }

            console.log(`Track to replace has tags: ${JSON.stringify(trackTags)}`);

            // Build the query based on the match mode
            let query = '';
            let params = [];

            if (matchMode === 'exact') {
                // Exact match: Find tracks with exactly the same tags
                // This is complex in SQL, so we'll fetch tracks with any of these tags and filter in JS
                const tagLikeConditions = trackTags.map(() => 'tags LIKE ?').join(' OR ');
                query = `SELECT id, tags FROM tracks WHERE (${tagLikeConditions}) ORDER BY RANDOM()`;
                params = trackTags.map(tag => `%${tag}%`);
            }
            else if (matchMode === 'partial') {
                // Partial match: Find tracks with at least one matching tag
                const tagLikeConditions = trackTags.map(() => 'tags LIKE ?').join(' OR ');
                query = `SELECT id, tags FROM tracks WHERE (${tagLikeConditions}) ORDER BY RANDOM()`;
                params = trackTags.map(tag => `%${tag}%`);
            }
            else if (matchMode === 'custom' && Array.isArray(selectedTags) && selectedTags.length > 0) {
                // Custom match: Find tracks with the selected tags
                const tagLikeConditions = selectedTags.map(() => 'tags LIKE ?').join(' AND ');
                query = `SELECT id, tags FROM tracks WHERE (${tagLikeConditions}) ORDER BY RANDOM()`;
                params = selectedTags.map(tag => `%${tag}%`);
            }
            else {
                return { success: false, error: 'Invalid match mode or no tags selected' };
            }

            // Execute the query to find potential replacement tracks
            const potentialTracks = await new Promise((res, rej) => {
                currentDB.all(query, params, (e, r) => e ? rej(e) : res(r));
            });

            console.log(`Found ${potentialTracks.length} potential tracks for replacement`);

            // Filter out tracks that are already in the playlist (except the one being replaced)
            let availableTracks = potentialTracks.filter(track =>
                !existingTrackIds.includes(track.id) || track.id === trackId
            );

            // Further filter based on match mode
            if (matchMode === 'exact') {
                availableTracks = availableTracks.filter(track => {
                    if (!track.tags) return false;
                    const trackTagSet = new Set(track.tags.split(',').map(tag => tag.trim().toLowerCase()));
                    const originalTagSet = new Set(trackTags);

                    // Check if the sets have the same size and all elements
                    if (trackTagSet.size !== originalTagSet.size) return false;
                    for (const tag of originalTagSet) {
                        if (!trackTagSet.has(tag)) return false;
                    }
                    return true;
                });
            }

            console.log(`After filtering, ${availableTracks.length} tracks are available for replacement`);

            if (availableTracks.length === 0) {
                return { success: false, error: 'No matching tracks available for replacement' };
            }

            // Remove the current track from available tracks if it's there
            if (availableTracks.length > 1) {
                const currentTrackIndex = availableTracks.findIndex(t => t.id === trackId);
                if (currentTrackIndex !== -1) {
                    availableTracks.splice(currentTrackIndex, 1);
                }
            } else if (availableTracks.length === 1 && availableTracks[0].id === trackId) {
                return { success: false, error: 'No other matching tracks available' };
            }

            // Select a random track from the available tracks
            const randomTrack = availableTracks[Math.floor(Math.random() * availableTracks.length)];

            // Replace the track in the database
            await new Promise((res, rej) => currentDB.serialize(() => {
                currentDB.run('BEGIN TRANSACTION', (err_begin) => {
                    if (err_begin) {
                        console.error('BEGIN TRANSACTION error:', err_begin.message);
                        return rej(new Error(`Begin TX failed: ${err_begin.message}`));
                    }
                });

                currentDB.run('DELETE FROM playlist_tracks WHERE playlist_id = ? AND track_id = ?',
                    [playlistId, trackId], (e_del) => {
                        if (e_del) {
                            console.error('DELETE error:', e_del.message);
                            currentDB.run('ROLLBACK');
                            return rej(new Error(`DELETE failed: ${e_del.message}`));
                        }
                    });

                const stmt = currentDB.prepare(`
                    INSERT INTO playlist_tracks
                    (playlist_id, track_id, position, source_playlist_id, cycle)
                    VALUES (?, ?, ?, NULL, ?)
                `);

                stmt.run(
                    playlistId,
                    randomTrack.id,
                    trackToReplaceInfo.position,
                    trackToReplaceInfo.cycle || 1,
                    (e_run) => {
                        if (e_run) {
                            console.error('INSERT stmt.run error:', e_run.message);
                            currentDB.run('ROLLBACK');
                            return rej(new Error(`INSERT failed: ${e_run.message}`));
                        }
                    }
                );

                stmt.finalize((e_fin) => {
                    if (e_fin) {
                        console.error('INSERT stmt.finalize error:', e_fin.message);
                        currentDB.run('ROLLBACK');
                        return rej(new Error(`FINALIZE failed: ${e_fin.message}`));
                    }
                });

                currentDB.run('COMMIT', (e_commit) => {
                    if (e_commit) {
                        console.error('COMMIT error:', e_commit.message);
                        currentDB.run('ROLLBACK');
                        return rej(new Error(`COMMIT failed: ${e_commit.message}`));
                    } else {
                        res();
                    }
                });
            }));

            return {
                success: true,
                oldTrackId: trackId,
                newTrackId: randomTrack.id,
                position: trackToReplaceInfo.position,
                matchMode
            };
        } catch (error) {
            console.error('Error replacing track by tags:', error);
            return { success: false, error: error.message };
        }
    });

    // Handler for manually replacing a track in a playlist
    ipcMain.handle('playlist:replace-track-manually', async (_, { playlistId, oldTrackId, newTrackId }) => {
        console.log(`Manually replacing track ${oldTrackId} with track ${newTrackId} in playlist ${playlistId}`);
        try {
            // Get the track to replace and its position/cycle info
            const trackToReplaceInfo = await new Promise((res, rej) => {
                currentDB.get(`
                    SELECT pt.position, pt.cycle
                    FROM playlist_tracks pt
                    WHERE pt.playlist_id = ? AND pt.track_id = ?
                `, [playlistId, oldTrackId], (e, r) => e ? rej(e) : res(r));
            });

            if (!trackToReplaceInfo) {
                return { success: false, error: 'Track not found in playlist' };
            }

            // Verify that the new track exists in the database
            const newTrackExists = await new Promise((res, rej) => {
                currentDB.get(`
                    SELECT id FROM tracks WHERE id = ?
                `, [newTrackId], (e, r) => e ? rej(e) : res(r));
            });

            if (!newTrackExists) {
                return { success: false, error: 'Selected replacement track not found in database' };
            }

            // Check if the new track is already in the playlist
            const trackAlreadyInPlaylist = await new Promise((res, rej) => {
                currentDB.get(`
                    SELECT track_id FROM playlist_tracks
                    WHERE playlist_id = ? AND track_id = ?
                `, [playlistId, newTrackId], (e, r) => e ? rej(e) : res(r));
            });

            if (trackAlreadyInPlaylist) {
                return { success: false, error: 'Selected track is already in this playlist' };
            }

            // Replace the track in the playlist
            await new Promise((res, rej) => {
                currentDB.run(`
                    UPDATE playlist_tracks
                    SET track_id = ?
                    WHERE playlist_id = ? AND track_id = ?
                `, [newTrackId, playlistId, oldTrackId], function (e) {
                    if (e) rej(e);
                    else res();
                });
            });

            console.log(`Successfully replaced track ${oldTrackId} with track ${newTrackId} in playlist ${playlistId}`);

            return {
                success: true,
                oldTrackId: oldTrackId,
                newTrackId: newTrackId,
                position: trackToReplaceInfo.position,
                cycle: trackToReplaceInfo.cycle
            };
        } catch (error) {
            console.error('Error manually replacing track:', error);
            return { success: false, error: error.message };
        }
    });

    ipcMain.handle('library:get-tracks', async (_, { directoryPath } = {}) => {
        console.log(`Fetching tracks from DB. Optional directory filter: ${directoryPath}`);
        const currentDB = getDB();
        return new Promise((resolve, reject) => {
            let query = 'SELECT id, unique_id, path, name, extension, title, artist, duration, tags, createdAt, updatedAt FROM tracks';
            const params = [];
            if (directoryPath) {
                query += ' WHERE path LIKE ?';
                params.push(directoryPath + '%'); // Ensure path starts with the directory
            }
            query += ' ORDER BY artist COLLATE NOCASE, title COLLATE NOCASE';

            currentDB.all(query, params, (err, rows) => {
                if (err) {
                    console.error('Error fetching tracks:', err.message);
                    reject(new Error(`Failed to fetch tracks: ${err.message}`));
                } else {
                    console.log(`Retrieved ${rows.length} tracks ${directoryPath ? 'for directory ' + directoryPath : 'from library'}.`);
                    resolve(rows);
                }
            });
        });
    });

    // Add this handler for getting all unique tags with counts
    ipcMain.handle('track:getAllTags', async () => {
        return new Promise((resolve, reject) => {
            // This query extracts all tags, splits them, and counts occurrences
            // Using a simpler approach with instr() and substr() to avoid JSON parsing issues
            const query = `
                WITH RECURSIVE
                split_tags(tag, rest) AS (
                    SELECT
                        TRIM(SUBSTR(tags, 1, CASE WHEN INSTR(tags, ',') = 0 THEN LENGTH(tags) ELSE INSTR(tags, ',') - 1 END)),
                        CASE WHEN INSTR(tags, ',') = 0 THEN '' ELSE TRIM(SUBSTR(tags, INSTR(tags, ',') + 1)) END
                    FROM tracks
                    WHERE tags IS NOT NULL AND tags != ''
                    UNION ALL
                    SELECT
                        TRIM(SUBSTR(rest, 1, CASE WHEN INSTR(rest, ',') = 0 THEN LENGTH(rest) ELSE INSTR(rest, ',') - 1 END)),
                        CASE WHEN INSTR(rest, ',') = 0 THEN '' ELSE TRIM(SUBSTR(rest, INSTR(rest, ',') + 1)) END
                    FROM split_tags
                    WHERE rest != ''
                )
                SELECT tag as name, COUNT(*) as count
                FROM split_tags
                WHERE tag != ''
                GROUP BY tag
                ORDER BY count DESC, tag COLLATE NOCASE
            `;

            currentDB.all(query, [], (err, rows) => {
                if (err) {
                    reject(new Error(`Failed to get all tags: ${err.message}`));
                } else {
                    resolve(rows);
                }
            });
        });
    });

    // Tag-based Playlist Generator Handlers
    ipcMain.handle('tag-playlist-generator:create', async (_, { name, targetDuration }) => {
        return new Promise((resolve, reject) => {
            const stmt = currentDB.prepare('INSERT INTO playlist_tag_generators (name, target_duration, updatedAt) VALUES (?, ?, CURRENT_TIMESTAMP)');
            stmt.run(name, targetDuration, function (err) {
                if (err) reject(new Error(`Failed to create tag-based playlist generator: ${err.message}`));
                else resolve({ id: this.lastID, name, targetDuration, createdAt: new Date().toISOString() });
            });
            stmt.finalize();
        });
    });

    ipcMain.handle('tag-playlist-generator:addSource', async (_, { generatorId, primaryTag, secondaryTags, trackCount, position }) => {
        // Convert secondaryTags array to JSON string if it exists
        const secondaryTagsStr = secondaryTags && secondaryTags.length > 0 ? JSON.stringify(secondaryTags) : null;

        return new Promise((resolve, reject) => {
            const stmt = currentDB.prepare('INSERT INTO playlist_tag_generator_sources (generator_id, primary_tag, secondary_tags, track_count, position) VALUES (?, ?, ?, ?, ?)');
            stmt.run(generatorId, primaryTag, secondaryTagsStr, trackCount, position, function (err) {
                if (err) reject(new Error(`Failed to add tag source: ${err.message}`));
                else resolve({
                    id: this.lastID,
                    generatorId,
                    primaryTag,
                    secondaryTags: secondaryTags || [],
                    trackCount,
                    position
                });
            });
            stmt.finalize();
        });
    });

    ipcMain.handle('tag-playlist-generator:generate', async (_, { generatorId, playlistName, sourcePlaylistId }) => {
        console.log(`Generating tag-based playlist "${playlistName}" from generator ${generatorId}${sourcePlaylistId ? ` with source playlist ${sourcePlaylistId}` : ''}`);
        try {
            // Get generator info
            const generator = await new Promise((res, rej) => {
                currentDB.get('SELECT id, name, target_duration as targetDuration FROM playlist_tag_generators WHERE id = ?',
                    [generatorId], (e, r) => e ? rej(e) : (r ? res(r) : rej(new Error('Generator not found'))));
            });

            // Get sources (tag combinations)
            const sources = await new Promise((res, rej) => {
                currentDB.all(`
                    SELECT id, generator_id as generatorId, primary_tag as primaryTag,
                           secondary_tags as secondaryTags, track_count as trackCount, position
                    FROM playlist_tag_generator_sources
                    WHERE generator_id = ?
                    ORDER BY position, id`,
                    [generatorId], (e, r) => e ? rej(e) : res(r));
            });

            if (sources.length === 0) return { success: false, error: 'No tag sources configured' };

            // Parse secondary tags from JSON string
            sources.forEach(source => {
                if (source.secondaryTags) {
                    try {
                        source.secondaryTags = JSON.parse(source.secondaryTags);
                    } catch (e) {
                        console.error(`Error parsing secondary tags for source ${source.id}:`, e);
                        source.secondaryTags = [];
                    }
                } else {
                    source.secondaryTags = [];
                }
            });

            // Create new playlist
            const newPlaylist = await new Promise((res, rej) => {
                const stmt = currentDB.prepare('INSERT INTO playlists (name, is_generated, updatedAt) VALUES (?, 1, CURRENT_TIMESTAMP)');
                stmt.run(playlistName, function (e) {
                    e ? rej(e) : res({ id: this.lastID, name: playlistName, is_generated: 1 });
                });
                stmt.finalize();
            });

            let position = 1;
            let totalDuration = 0;
            const targetDurationSeconds = generator.targetDuration * 60;
            const usedTrackIds = new Set();
            const tracksByCycle = [];
            const availableTracksByTag = {};

            // Calcola quante tracce totali abbiamo bisogno per ogni ciclo
            const tracksPerCycle = sources.reduce((sum, source) => sum + source.trackCount, 0);
            console.log(`Each cycle requires ${tracksPerCycle} tracks (${sources.length} sources)`);

            // Funzione per selezionare tracce per un ciclo
            const selectTracksForCycle = async (cycleNumber) => {
                const cycleTrackIds = new Set();
                const cycleTracks = [];
                let cycleTotalDuration = 0;

                // Process each tag source for this cycle
                for (const source of sources) {
                    // Create unique cache keys for this tag combination - separate for source playlist and general database
                    const secondaryTagsStr = source.secondaryTags && source.secondaryTags.length > 0
                        ? source.secondaryTags.sort().join(',')
                        : '';
                    const baseCacheKey = `${source.primaryTag}|${secondaryTagsStr}`;
                    const sourcePlaylistCacheKey = sourcePlaylistId ? `${baseCacheKey}|source:${sourcePlaylistId}` : null;
                    const generalCacheKey = `${baseCacheKey}|general`;

                    // Fetch tracks from source playlist if specified and not cached
                    if (sourcePlaylistId && !availableTracksByTag[sourcePlaylistCacheKey]) {
                        console.log(`Fetching tracks from source playlist ${sourcePlaylistId} for tag combination: primary="${source.primaryTag}", secondary=[${secondaryTagsStr}]`);

                        let sourcePlaylistTracks = [];

                        // Build query for source playlist
                        if (source.secondaryTags && source.secondaryTags.length > 0) {
                            // Query with both primary and secondary tags
                            let query = `
                                SELECT DISTINCT t.id, t.duration
                                FROM tracks t
                                JOIN playlist_tracks pt ON t.id = pt.track_id
                                WHERE pt.playlist_id = ? AND t.tags LIKE ?
                            `;
                            const params = [sourcePlaylistId, `%${source.primaryTag}%`];

                            // Add each secondary tag as an additional AND condition
                            source.secondaryTags.forEach((tag) => {
                                query += ` AND t.tags LIKE ?`;
                                params.push(`%${tag}%`);
                            });
                            query += ' ORDER BY RANDOM()';

                            console.log(`SQL (source playlist with secondary tags): ${query}`);
                            console.log(`Params: ${JSON.stringify(params)}`);

                            sourcePlaylistTracks = await new Promise((res, rej) => {
                                currentDB.all(query, params, (e, r) => e ? rej(e) : res(r));
                            });

                            console.log(`Found ${sourcePlaylistTracks.length} tracks with primary tag "${source.primaryTag}" AND all secondary tags in source playlist ${sourcePlaylistId}`);
                        } else {
                            // Query with only primary tag
                            const query = `
                                SELECT DISTINCT t.id, t.duration
                                FROM tracks t
                                JOIN playlist_tracks pt ON t.id = pt.track_id
                                WHERE pt.playlist_id = ? AND t.tags LIKE ?
                                ORDER BY RANDOM()
                            `;
                            const params = [sourcePlaylistId, `%${source.primaryTag}%`];

                            console.log(`SQL (source playlist primary only): ${query}`);
                            console.log(`Params: ${JSON.stringify(params)}`);

                            sourcePlaylistTracks = await new Promise((res, rej) => {
                                currentDB.all(query, params, (e, r) => e ? rej(e) : res(r));
                            });

                            console.log(`Found ${sourcePlaylistTracks.length} tracks with primary tag "${source.primaryTag}" in source playlist ${sourcePlaylistId}`);
                        }

                        // Cache the source playlist tracks
                        availableTracksByTag[sourcePlaylistCacheKey] = sourcePlaylistTracks;
                    }

                    // Fetch tracks from general database if not cached
                    if (!availableTracksByTag[generalCacheKey]) {
                        console.log(`Fetching tracks from general database for tag combination: primary="${source.primaryTag}", secondary=[${secondaryTagsStr}]`);

                        let generalTracks = [];

                        // Build query for general database
                        if (source.secondaryTags && source.secondaryTags.length > 0) {
                            // Query with both primary and secondary tags
                            let query = `
                                SELECT t.id, t.duration
                                FROM tracks t
                                WHERE t.tags LIKE ?
                            `;
                            const params = [`%${source.primaryTag}%`];

                            // Add each secondary tag as an additional AND condition
                            source.secondaryTags.forEach((tag) => {
                                query += ` AND t.tags LIKE ?`;
                                params.push(`%${tag}%`);
                            });
                            query += ' ORDER BY RANDOM()';

                            generalTracks = await new Promise((res, rej) => {
                                currentDB.all(query, params, (e, r) => e ? rej(e) : res(r));
                            });

                            console.log(`Found ${generalTracks.length} tracks with primary tag "${source.primaryTag}" AND all secondary tags in general database`);
                        } else {
                            // Query with only primary tag
                            const query = `
                                SELECT t.id, t.duration
                                FROM tracks t
                                WHERE t.tags LIKE ?
                                ORDER BY RANDOM()
                            `;
                            const params = [`%${source.primaryTag}%`];

                            generalTracks = await new Promise((res, rej) => {
                                currentDB.all(query, params, (e, r) => e ? rej(e) : res(r));
                            });

                            console.log(`Found ${generalTracks.length} tracks with primary tag "${source.primaryTag}" in general database`);
                        }

                        // Cache the general database tracks
                        availableTracksByTag[generalCacheKey] = generalTracks;
                    }

                    // Now select tracks for this cycle using the improved logic
                    console.log(`Selecting tracks for cycle ${cycleNumber}, tag combination: primary="${source.primaryTag}", secondary=[${secondaryTagsStr}]`);

                    let selectedTracks = [];
                    let tracksAdded = 0;

                    // Step 1: Try to get tracks from source playlist first (if specified)
                    if (sourcePlaylistId && availableTracksByTag[sourcePlaylistCacheKey]) {
                        const availableSourceTracks = availableTracksByTag[sourcePlaylistCacheKey].filter(
                            track => !usedTrackIds.has(track.id)
                        );

                        console.log(`Found ${availableSourceTracks.length} available tracks from source playlist ${sourcePlaylistId} for tag "${source.primaryTag}"`);

                        const tracksFromSource = Math.min(availableSourceTracks.length, source.trackCount);
                        if (tracksFromSource > 0) {
                            selectedTracks = availableSourceTracks.slice(0, tracksFromSource);
                            tracksAdded = tracksFromSource;
                            console.log(`Selected ${tracksFromSource} tracks from source playlist for tag "${source.primaryTag}"`);
                        }
                    }

                    // Step 2: If we still need more tracks, get them from the general database
                    if (tracksAdded < source.trackCount && availableTracksByTag[generalCacheKey]) {
                        const remainingCount = source.trackCount - tracksAdded;
                        const availableGeneralTracks = availableTracksByTag[generalCacheKey].filter(
                            track => !usedTrackIds.has(track.id) && !selectedTracks.some(selected => selected.id === track.id)
                        );

                        console.log(`Need ${remainingCount} more tracks for tag "${source.primaryTag}", found ${availableGeneralTracks.length} available in general database`);

                        const tracksFromGeneral = Math.min(availableGeneralTracks.length, remainingCount);
                        if (tracksFromGeneral > 0) {
                            const additionalTracks = availableGeneralTracks.slice(0, tracksFromGeneral);
                            selectedTracks = [...selectedTracks, ...additionalTracks];
                            tracksAdded += tracksFromGeneral;
                            console.log(`Selected ${tracksFromGeneral} additional tracks from general database for tag "${source.primaryTag}"`);
                        }
                    }

                    // If we still couldn't find any tracks for this tag combination, skip to the next one
                    if (selectedTracks.length === 0) {
                        console.log(`No tracks available for tag "${source.primaryTag}" in cycle ${cycleNumber}, skipping this tag combination`);
                        continue;
                    }

                    // Add the selected tracks to the current cycle
                    console.log(`Adding ${selectedTracks.length} total tracks for tag "${source.primaryTag}" to cycle ${cycleNumber}`);
                    for (const track of selectedTracks) {
                        cycleTracks.push({
                            trackId: track.id,
                            duration: track.duration || 0,
                            sourceId: source.id,
                            primaryTag: source.primaryTag
                        });
                        cycleTrackIds.add(track.id);
                        usedTrackIds.add(track.id);
                        cycleTotalDuration += (track.duration || 0);
                    }

                    // Log if we couldn't find enough tracks
                    if (selectedTracks.length < source.trackCount) {
                        console.log(`Could only add ${selectedTracks.length}/${source.trackCount} total tracks for tag "${source.primaryTag}" in cycle ${cycleNumber}`);
                    }
                }

                return { tracks: cycleTracks, duration: cycleTotalDuration };
            };

            // Seleziona tracce per il primo ciclo per calcolare la durata
            const firstCycle = await selectTracksForCycle(1);
            tracksByCycle.push(firstCycle.tracks);
            const cycleDuration = firstCycle.duration;
            totalDuration += cycleDuration;

            // If no tracks were found, return an error
            if (tracksByCycle.length === 0 || tracksByCycle[0].length === 0) {
                return { success: false, error: 'No tracks found matching the specified tags' };
            }

            // Calcola quanti cicli sono necessari per raggiungere la durata target
            let repetitions = 1;

            // Calcola il numero totale di tracce disponibili per stimare il massimo numero di cicli possibili
            // Create a map to count unique tracks across all tag combinations
            const uniqueTrackIds = new Set();
            Object.values(availableTracksByTag).forEach(tracks => {
                tracks.forEach(track => uniqueTrackIds.add(track.id));
            });
            const totalAvailableTracks = uniqueTrackIds.size;
            const maxPossibleCycles = Math.floor(totalAvailableTracks / tracksPerCycle);
            console.log(`Total available tracks: ${totalAvailableTracks}, Maximum possible cycles: ${maxPossibleCycles}`);

            // Calculate how many cycles we need to reach the target duration
            console.log(`First cycle duration: ${cycleDuration}s, Target duration: ${targetDurationSeconds}s`);

            // Make sure the cycle duration is valid
            if (cycleDuration <= 0) {
                // If the cycle duration is invalid, estimate an average of 3 minutes per track
                const estimatedCycleDuration = tracksPerCycle * 180;
                console.log(`Invalid cycle duration, using estimated duration: ${estimatedCycleDuration}s based on ${tracksPerCycle} tracks`);

                // Calculate how many repetitions are needed to reach the target duration
                if (targetDurationSeconds > 0) {
                    repetitions = Math.ceil(targetDurationSeconds / estimatedCycleDuration);
                    console.log(`Estimated cycles needed: ${repetitions}`);

                    // Limit the number of repetitions based on available tracks
                    repetitions = Math.min(repetitions, maxPossibleCycles);
                }
            } else if (targetDurationSeconds > 0) {
                // Calculate how many repetitions are needed to reach the target duration
                if (cycleDuration < targetDurationSeconds) {
                    repetitions = Math.ceil(targetDurationSeconds / cycleDuration);
                    console.log(`Target duration: ${targetDurationSeconds}s, Cycle duration: ${cycleDuration}s, Cycles needed: ${repetitions}`);
                } else {
                    console.log(`Cycle duration ${cycleDuration}s already exceeds target duration ${targetDurationSeconds}s, using 1 cycle`);
                }

                // Limit the number of repetitions based on available tracks
                repetitions = Math.min(repetitions, maxPossibleCycles);
                console.log(`Limited to ${repetitions} cycles based on available tracks (${maxPossibleCycles} max possible)`);
            } else {
                console.log(`Using default 1 cycle (no target duration specified)`);
            }

            // Seleziona tracce per i cicli rimanenti
            for (let i = 2; i <= repetitions; i++) {
                const cycle = await selectTracksForCycle(i);

                // Only stop if we couldn't add ANY tracks to this cycle
                if (cycle.tracks.length > 0) {
                    tracksByCycle.push(cycle.tracks);
                    totalDuration += cycle.duration;

                    // Log how many tracks we were able to add in this cycle
                    const totalTracksInCycle = cycle.tracks.length;
                    const totalRequestedTracks = sources.reduce((sum, source) => sum + source.trackCount, 0);
                    console.log(`Added ${totalTracksInCycle}/${totalRequestedTracks} requested tracks to cycle ${i}`);

                    // If we got less than 50% of the requested tracks, log a warning but continue
                    if (totalTracksInCycle < totalRequestedTracks / 2) {
                        console.log(`Warning: Cycle ${i} is less than 50% full (${totalTracksInCycle}/${totalRequestedTracks}), but continuing to next cycle`);
                    }
                } else {
                    console.log(`No tracks available for cycle ${i}, stopping at ${i - 1} cycles`);
                    break;
                }
            }

            console.log(`Created ${tracksByCycle.length} cycles with a total duration of ${Math.floor(totalDuration / 60)}m ${Math.floor(totalDuration % 60)}s`);

            // If no tracks were found, return an error
            if (tracksByCycle.length === 0 || tracksByCycle[0].length === 0) {
                return { success: false, error: 'No tracks found matching the specified tags' };
            }

            // Insert tracks into the playlist
            await new Promise((res, rej) => {
                currentDB.serialize(() => {
                    currentDB.run('BEGIN TRANSACTION', (err_begin) => {
                        if (err_begin) {
                            console.error('BEGIN TRANSACTION error:', err_begin.message);
                            return rej(new Error(`Begin TX failed: ${err_begin.message}`));
                        }
                    });

                    // Prepare values for bulk insert
                    const allValues = [];
                    const allPlaceholders = [];
                    position = 1; // Reset position counter for inserting tracks

                    // Add tracks from each cycle
                    for (let cycleNumber = 1; cycleNumber <= tracksByCycle.length; cycleNumber++) {
                        const cycleTracks = tracksByCycle[cycleNumber - 1];

                        for (const track of cycleTracks) {
                            // Add values for this track
                            allValues.push(
                                newPlaylist.id,
                                track.trackId,
                                position++,
                                null, // source_playlist_id is null for tag-based playlists
                                cycleNumber
                            );

                            // Add placeholder for this row
                            allPlaceholders.push('(?, ?, ?, ?, ?)');
                        }
                    }

                    // Check if we have any tracks to insert
                    if (allPlaceholders.length === 0) {
                        console.error('No tracks to insert into playlist!');
                        currentDB.run('ROLLBACK', (err_rb) => {
                            if (err_rb) console.error('Rollback error:', err_rb.message);
                            return rej(new Error('No tracks to insert into playlist'));
                        });
                        return;
                    }

                    // Create a single insert query with all values
                    const insertSQL = `INSERT INTO playlist_tracks
                        (playlist_id, track_id, position, source_playlist_id, cycle)
                        VALUES ${allPlaceholders.join(', ')}`;

                    console.log(`Inserting ${allPlaceholders.length} tracks into playlist ${newPlaylist.id}`);

                    // Execute the insert query with individual inserts instead of bulk insert
                    // This approach is more reliable for ensuring all tracks are properly inserted
                    let insertedCount = 0;
                    let insertErrors = 0;

                    // Use a prepared statement for better performance and reliability
                    const stmt = currentDB.prepare('INSERT INTO playlist_tracks (playlist_id, track_id, position, source_playlist_id, cycle) VALUES (?, ?, ?, ?, ?)');

                    // Process each track individually
                    const processTrack = (index) => {
                        if (index >= allValues.length / 5) {
                            // All tracks processed, finalize and commit
                            stmt.finalize((err_finalize) => {
                                if (err_finalize) {
                                    console.error('Error finalizing statement:', err_finalize.message);
                                    currentDB.run('ROLLBACK', (err_rb) => {
                                        if (err_rb) console.error('Rollback error after finalize failure:', err_rb.message);
                                        return rej(new Error(`Failed to finalize statement: ${err_finalize.message}`));
                                    });
                                    return;
                                }

                                if (insertErrors > 0) {
                                    console.error(`Encountered ${insertErrors} errors while inserting tracks`);
                                    currentDB.run('ROLLBACK', (err_rb) => {
                                        if (err_rb) console.error('Rollback error after insert errors:', err_rb.message);
                                        return rej(new Error(`Failed to insert all tracks (${insertErrors} errors)`));
                                    });
                                    return;
                                }

                                // Commit the transaction
                                currentDB.run('COMMIT', (err_commit) => {
                                    if (err_commit) {
                                        console.error('COMMIT error:', err_commit.message);
                                        currentDB.run('ROLLBACK', (err_rb) => {
                                            if (err_rb) console.error('Rollback error after commit failure:', err_rb.message);
                                        });
                                        return rej(new Error(`COMMIT failed: ${err_commit.message}`));
                                    }

                                    console.log(`Successfully committed ${insertedCount} tracks to playlist ${newPlaylist.id}`);

                                    // Verify that tracks were actually inserted
                                    currentDB.get('SELECT COUNT(*) as count FROM playlist_tracks WHERE playlist_id = ?',
                                        [newPlaylist.id], (err_count, countRow) => {
                                            if (err_count) {
                                                console.error('Error verifying track count:', err_count.message);
                                            } else {
                                                console.log(`Verified ${countRow.count} tracks inserted into playlist ${newPlaylist.id}`);
                                                if (countRow.count === 0) {
                                                    console.warn('No tracks were inserted despite successful transaction!');
                                                } else if (countRow.count !== insertedCount) {
                                                    console.warn(`Track count mismatch: expected ${insertedCount}, found ${countRow.count}`);
                                                }
                                            }
                                            res();
                                        }
                                    );
                                });
                            });
                            return;
                        }

                        // Extract values for this track
                        const baseIndex = index * 5;
                        const playlistId = allValues[baseIndex];
                        const trackId = allValues[baseIndex + 1];
                        const position = allValues[baseIndex + 2];
                        const sourcePlaylistId = allValues[baseIndex + 3];
                        const cycle = allValues[baseIndex + 4];

                        // Insert this track
                        stmt.run(playlistId, trackId, position, sourcePlaylistId, cycle, function (err_run) {
                            if (err_run) {
                                console.error(`Error inserting track ${trackId} at position ${position} in cycle ${cycle}:`, err_run.message);
                                insertErrors++;
                            } else {
                                if (this.changes > 0) {
                                    insertedCount++;
                                }
                            }

                            // Process the next track
                            processTrack(index + 1);
                        });
                    };

                    // Start processing tracks
                    processTrack(0);
                });
            });

            // Get the final playlist info
            const finalPlaylist = await new Promise((res, rej) => {
                currentDB.get('SELECT id, name, createdAt, updatedAt FROM playlists WHERE id = ?',
                    [newPlaylist.id], (e, r) => e ? rej(e) : (r ? res(r) : rej(new Error('Final playlist not found'))));
            });

            const trackCountRow = await new Promise((res, rej) => {
                currentDB.get('SELECT COUNT(*) as trackCount FROM playlist_tracks WHERE playlist_id = ?',
                    [newPlaylist.id], (e, r) => e ? rej(e) : res(r));
            });

            finalPlaylist.trackCount = trackCountRow ? trackCountRow.trackCount : 0;
            finalPlaylist.repetitions = tracksByCycle.length;

            // Calculate actual duration
            const actualTotalDuration = totalDuration;
            const actualDurationMinutes = Math.floor(actualTotalDuration / 60);
            const actualDurationSeconds = Math.floor(actualTotalDuration % 60);

            // Calculate target duration for comparison
            const targetDurationMinutes = Math.floor(targetDurationSeconds / 60);
            const targetDurationSecondsRemainder = Math.floor(targetDurationSeconds % 60);

            console.log(`Tag-based playlist generated with actual duration: ${actualDurationMinutes}m ${actualDurationSeconds}s vs target: ${targetDurationMinutes}m ${targetDurationSecondsRemainder}s`);

            return {
                success: true,
                playlist: finalPlaylist,
                totalDuration: actualTotalDuration,
                actualDurationMinutes,
                actualDurationSeconds,
                targetDurationMinutes,
                targetDurationSeconds: targetDurationSecondsRemainder,
                repetitions: tracksByCycle.length,
                targetDurationOriginal: targetDurationSeconds,
                cycleCount: tracksByCycle.length,
                tracksPerCycle: tracksByCycle.map(cycle => cycle.length)
            };
        } catch (error) {
            console.error('Error generating tag-based playlist:', error);
            return { success: false, error: error.message };
        }
    });

    // Mark playlist as generated
    ipcMain.handle('playlist:markAsGenerated', async (_, { playlistId, generatorConfig }) => {
        console.log(`Marking playlist ${playlistId} as generated`);
        return new Promise((resolve, reject) => {
            currentDB.run('UPDATE playlists SET is_generated = 1, updatedAt = CURRENT_TIMESTAMP WHERE id = ?',
                [playlistId], function (err) {
                    if (err) {
                        console.error('Error marking playlist as generated:', err.message);
                        reject(new Error(`Failed to mark playlist as generated: ${err.message}`));
                    } else {
                        console.log(`Successfully marked playlist ${playlistId} as generated`);
                        resolve({ success: true });
                    }
                });
        });
    });

    // Step Builder Handlers
    ipcMain.handle('step-builder:generateEntry', async (_, { playlistId, primaryTag, secondaryTags, trackCount, sourcePlaylistId, excludeTrackIds }) => {
        console.log(`Generating step builder entry for playlist ${playlistId} with primary tag: ${primaryTag}`);
        try {
            // Build the query to find matching tracks
            let query = `
                SELECT DISTINCT t.id, t.title, t.artist, t.duration, t.tags, t.path
                FROM tracks t
            `;
            let params = [];
            let whereConditions = [];

            // If source playlist is specified, join with playlist_tracks
            if (sourcePlaylistId) {
                query += ` JOIN playlist_tracks pt ON t.id = pt.track_id`;
                whereConditions.push('pt.playlist_id = ?');
                params.push(sourcePlaylistId);
            }

            // Add tag conditions - more flexible tag matching
            const tagConditions = [
                't.tags LIKE ?',  // tag in middle: ,tag,
                't.tags LIKE ?',  // tag at start: tag,
                't.tags LIKE ?',  // tag at end: ,tag
                't.tags = ?',     // exact match: tag
                't.tags IS NULL OR t.tags = ""'  // also include tracks with no tags for debugging
            ];
            whereConditions.push(`(${tagConditions.join(' OR ')})`);
            params.push(`%,${primaryTag},%`, `${primaryTag},%`, `%,${primaryTag}`, primaryTag);

            // Add secondary tag conditions if specified
            if (secondaryTags && secondaryTags.length > 0) {
                const secondaryConditions = secondaryTags.map(() =>
                    '(t.tags LIKE ? OR t.tags LIKE ? OR t.tags LIKE ? OR t.tags = ?)'
                ).join(' AND ');
                whereConditions.push(`(${secondaryConditions})`);

                secondaryTags.forEach(tag => {
                    params.push(`%,${tag},%`, `${tag},%`, `%,${tag}`, tag);
                });
            }

            // Exclude already generated tracks
            if (excludeTrackIds && excludeTrackIds.length > 0) {
                const placeholders = excludeTrackIds.map(() => '?').join(',');
                whereConditions.push(`t.id NOT IN (${placeholders})`);
                params.push(...excludeTrackIds);
            }

            // Exclude tracks already in the playlist
            whereConditions.push(`t.id NOT IN (SELECT track_id FROM playlist_tracks WHERE playlist_id = ?)`);
            params.push(playlistId);

            if (whereConditions.length > 0) {
                query += ` WHERE ${whereConditions.join(' AND ')}`;
            }

            query += ` ORDER BY RANDOM() LIMIT ?`;
            params.push(trackCount);

            console.log('Step builder query:', query);
            console.log('Step builder params:', params);

            const availableTracks = await new Promise((res, rej) => {
                currentDB.all(query, params, (e, r) => e ? rej(e) : res(r || []));
            });

            if (availableTracks.length === 0) {
                return { success: false, error: 'No matching tracks found for this combination' };
            }

            // Add tracks to playlist
            const addedTracks = [];
            const maxPosition = await new Promise((res, rej) => {
                currentDB.get('SELECT COALESCE(MAX(position), 0) as maxPos FROM playlist_tracks WHERE playlist_id = ?',
                    [playlistId], (e, r) => e ? rej(e) : res(r?.maxPos || 0));
            });

            let position = maxPosition + 1;

            for (const track of availableTracks) {
                await new Promise((res, rej) => {
                    const stmt = currentDB.prepare('INSERT INTO playlist_tracks (playlist_id, track_id, position, cycle) VALUES (?, ?, ?, 1)');
                    stmt.run(playlistId, track.id, position, (e) => e ? rej(e) : res());
                    stmt.finalize();
                });

                addedTracks.push({
                    id: track.id,
                    title: track.title,
                    artist: track.artist,
                    duration: track.duration,
                    position: position
                });

                position++;
            }

            // Update playlist timestamp
            await new Promise((res, rej) => {
                currentDB.run('UPDATE playlists SET updatedAt = CURRENT_TIMESTAMP WHERE id = ?',
                    [playlistId], (e) => e ? rej(e) : res());
            });

            return {
                success: true,
                tracks: addedTracks,
                message: `Added ${addedTracks.length} tracks to playlist`
            };

        } catch (error) {
            console.error('Error in step builder generation:', error);
            return { success: false, error: error.message };
        }
    });

    ipcMain.handle('step-builder:replaceEntry', async (_, { playlistId, primaryTag, secondaryTags, trackCount, sourcePlaylistId, excludeTrackIds }) => {
        console.log(`Replacing step builder entry for playlist ${playlistId} with primary tag: ${primaryTag}`);
        try {
            // Find tracks in the playlist that match the primary tag
            let findQuery = `
                SELECT pt.track_id, pt.position, t.tags
                FROM playlist_tracks pt
                JOIN tracks t ON pt.track_id = t.id
                WHERE pt.playlist_id = ? AND (t.tags LIKE ? OR t.tags LIKE ? OR t.tags LIKE ? OR t.tags = ?)
                ORDER BY pt.position
                LIMIT ?
            `;

            const findParams = [
                playlistId,
                `%,${primaryTag},%`, `${primaryTag},%`, `%,${primaryTag}`, primaryTag,
                trackCount
            ];

            const tracksToReplace = await new Promise((res, rej) => {
                currentDB.all(findQuery, findParams, (e, r) => e ? rej(e) : res(r || []));
            });

            if (tracksToReplace.length === 0) {
                return { success: false, error: 'No tracks found to replace with this tag combination' };
            }

            // Find replacement tracks using the same logic as generateEntry
            let query = `
                SELECT DISTINCT t.id, t.title, t.artist, t.duration, t.tags, t.path
                FROM tracks t
            `;
            let params = [];
            let whereConditions = [];

            if (sourcePlaylistId) {
                query += ` JOIN playlist_tracks pt ON t.id = pt.track_id`;
                whereConditions.push('pt.playlist_id = ?');
                params.push(sourcePlaylistId);
            }

            whereConditions.push('(t.tags LIKE ? OR t.tags LIKE ? OR t.tags LIKE ? OR t.tags = ?)');
            params.push(`%,${primaryTag},%`, `${primaryTag},%`, `%,${primaryTag}`, primaryTag);

            if (secondaryTags && secondaryTags.length > 0) {
                const secondaryConditions = secondaryTags.map(() =>
                    '(t.tags LIKE ? OR t.tags LIKE ? OR t.tags LIKE ? OR t.tags = ?)'
                ).join(' AND ');
                whereConditions.push(`(${secondaryConditions})`);

                secondaryTags.forEach(tag => {
                    params.push(`%,${tag},%`, `${tag},%`, `%,${tag}`, tag);
                });
            }

            // Exclude current tracks and already generated tracks
            const allExcludeIds = [...(excludeTrackIds || []), ...tracksToReplace.map(t => t.track_id)];
            if (allExcludeIds.length > 0) {
                const placeholders = allExcludeIds.map(() => '?').join(',');
                whereConditions.push(`t.id NOT IN (${placeholders})`);
                params.push(...allExcludeIds);
            }

            // Exclude other tracks already in the playlist
            whereConditions.push(`t.id NOT IN (SELECT track_id FROM playlist_tracks WHERE playlist_id = ? AND track_id NOT IN (${tracksToReplace.map(() => '?').join(',')}))`);
            params.push(playlistId, ...tracksToReplace.map(t => t.track_id));

            if (whereConditions.length > 0) {
                query += ` WHERE ${whereConditions.join(' AND ')}`;
            }

            query += ` ORDER BY RANDOM() LIMIT ?`;
            params.push(tracksToReplace.length);

            const replacementTracks = await new Promise((res, rej) => {
                currentDB.all(query, params, (e, r) => e ? rej(e) : res(r || []));
            });

            if (replacementTracks.length === 0) {
                return { success: false, error: 'No replacement tracks found' };
            }

            // Replace tracks
            for (let i = 0; i < Math.min(tracksToReplace.length, replacementTracks.length); i++) {
                const oldTrack = tracksToReplace[i];
                const newTrack = replacementTracks[i];

                await new Promise((res, rej) => {
                    currentDB.run('UPDATE playlist_tracks SET track_id = ? WHERE playlist_id = ? AND track_id = ? AND position = ?',
                        [newTrack.id, playlistId, oldTrack.track_id, oldTrack.position], (e) => e ? rej(e) : res());
                });
            }

            // Update playlist timestamp
            await new Promise((res, rej) => {
                currentDB.run('UPDATE playlists SET updatedAt = CURRENT_TIMESTAMP WHERE id = ?',
                    [playlistId], (e) => e ? rej(e) : res());
            });

            return {
                success: true,
                message: `Replaced ${Math.min(tracksToReplace.length, replacementTracks.length)} tracks`
            };

        } catch (error) {
            console.error('Error in step builder replacement:', error);
            return { success: false, error: error.message };
        }
    });

    console.log('Database-related IPC Handlers Initialized in dbOps');
}

module.exports = {
    setupDatabase,
    getDB,
    initializeDbIpcHandlers
};
