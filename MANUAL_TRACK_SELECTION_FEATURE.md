# Manual Track Selection Feature

## Overview

This document describes the implementation of the new "Manual Track Selection" feature for the Replace Track functionality in tag-generated playlists.

## Feature Description

The Manual Track Selection feature extends the existing Replace Track modal by adding a fourth replacement option that allows users to manually search and select a specific track from their music library.

## Implementation Details

### 1. User Interface Changes

#### HTML Structure (index.html)
- Added a new replacement option section with `data-mode="manual"`
- Includes search input field with path inclusion checkbox
- Results container for displaying search results
- Selected track information display
- "Select Manually" and "Replace with Selected Track" buttons

#### CSS Styling (styles/style.css)
- Added comprehensive styling for the manual track selection interface
- Consistent with existing application design patterns
- Responsive design for search results and track selection
- Visual feedback for selected tracks

### 2. JavaScript Implementation (scripts/renderer.js)

#### New Variables
- `selectedTrackForReplacement`: Currently selected track for replacement
- `manualSearchResults`: Array of search results
- DOM element references for the manual selection interface

#### Key Functions

##### `resetManualTrackSelection()`
- Resets all manual selection state and UI elements
- Called when opening/closing the replacement popup

##### `initializeManualTrackSelection()`
- Sets up event listeners for the manual selection interface
- Called during popup initialization

##### `showManualTrackSelection()`
- Shows the manual selection interface when "Select Manually" is clicked
- Hides the selection button and shows the replace button

##### `handleManualSearch()`
- Performs real-time search as user types
- Reuses existing `filterTracksForManualSearch()` logic
- Limits results to 50 tracks for performance

##### `filterTracksForManualSearch()`
- Filters tracks based on title, artist, tags, and optionally file path
- Consistent with main application search logic

##### `renderManualSearchResults()`
- Renders search results in a scrollable list
- Displays track title, artist, duration, and tags
- Adds click event listeners for track selection

##### `selectTrackForReplacement()`
- Handles track selection from search results
- Updates UI to show selected track information
- Enables the replace button

##### `handleManualTrackReplacement()`
- Performs the actual track replacement via IPC
- Shows loading state and handles errors
- Refreshes playlist view on success

### 3. Backend Implementation

#### Database Operations (scripts/database-operations.js)

##### New IPC Handler: `playlist:replace-track-manually`
- Validates input parameters (playlist ID, old track ID, new track ID)
- Checks that the new track exists in the database
- Prevents adding duplicate tracks to the same playlist
- Updates the playlist_tracks table with the new track
- Returns success/error status with relevant information

#### Preload Script (scripts/preload.js)
- Added `replaceTrackManually` to the playlist context bridge
- Added `getTracks` to the library context bridge for search functionality

## User Workflow

1. User clicks the "Replace" button on a track in a tag-generated playlist
2. Replace Track modal opens with four options:
   - Exact Tag Match
   - Partial Tag Match
   - Custom Tag Selection
   - **Manual Track Selection** (new)
3. User clicks "Select Manually" for the new option
4. Search interface appears with input field and path inclusion checkbox
5. User types to search for tracks in real-time
6. Search results appear in a scrollable list showing track details
7. User clicks on a track to select it
8. Selected track information is displayed
9. "Replace with Selected Track" button becomes enabled
10. User clicks replace button to perform the replacement
11. Modal closes and playlist view refreshes

## Technical Features

### Search Functionality
- Real-time search as user types
- Searches title, artist, tags, and optionally file paths
- Case-insensitive matching
- Consistent with main application search logic
- Limited to 50 results for performance

### Error Handling
- Validates all required parameters
- Prevents duplicate tracks in playlists
- Handles database errors gracefully
- Provides user-friendly error messages

### Performance Optimizations
- Debounced search to avoid excessive database queries
- Limited search results (50 tracks maximum)
- Efficient DOM manipulation for search results

### Accessibility
- Keyboard navigation support
- Clear visual feedback for selected items
- Consistent with existing UI patterns

## Integration Points

### Existing Systems
- Reuses existing search logic from main application
- Integrates with existing IPC communication patterns
- Follows established database operation patterns
- Maintains consistency with existing UI/UX

### Database Schema
- No changes to existing database schema required
- Uses existing playlist_tracks table structure
- Maintains referential integrity

## Testing Considerations

### Manual Testing Scenarios
1. Search functionality with various terms
2. Track selection and deselection
3. Replace button state management
4. Error handling for invalid selections
5. Integration with existing replacement options
6. Modal open/close behavior
7. Playlist refresh after replacement

### Edge Cases
- Empty search results
- Duplicate track prevention
- Network path handling
- Special characters in search terms
- Large result sets

## Future Enhancements

### Potential Improvements
1. Advanced search filters (by genre, year, etc.)
2. Bulk track replacement
3. Preview functionality before replacement
4. Search result sorting options
5. Recently selected tracks history

### Performance Optimizations
1. Virtual scrolling for large result sets
2. Search result caching
3. Incremental search loading

## Conclusion

The Manual Track Selection feature provides users with precise control over track replacement in tag-generated playlists, complementing the existing automated replacement options. The implementation maintains consistency with the existing codebase while providing a robust and user-friendly interface for manual track selection.
