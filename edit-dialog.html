<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <title>Edit Track Information</title>
    <meta http-equiv="Content-Security-Policy"
        content="default-src 'self'; script-src 'self'; style-src 'self' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com" />
    <meta http-equiv="X-Content-Security-Policy"
        content="default-src 'self'; script-src 'self'; style-src 'self' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com" />
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

        :root {
            --primary-color: #6366f1;
            --primary-hover: #4f46e5;
            --primary-light: rgba(99, 102, 241, 0.1);
            --primary-focus: rgba(99, 102, 241, 0.25);
            --primary-gradient: linear-gradient(135deg, #6366f1, #8b5cf6);
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --text-tertiary: #9ca3af;
            --bg-primary: #ffffff;
            --bg-secondary: #f9fafb;
            --bg-tertiary: #f3f4f6;
            --border-color: #e5e7eb;
            --border-hover: #d1d5db;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --radius-sm: 0.25rem;
            --radius-md: 0.375rem;
            --radius-lg: 0.5rem;
            --radius-xl: 0.75rem;
            --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
            --transition-normal: 200ms cubic-bezier(0.4, 0, 0.2, 1);
            --transition-slow: 300ms cubic-bezier(0.4, 0, 0.2, 1);
        }

        body.dark-theme {
            --primary-color: #818cf8;
            --primary-hover: #6366f1;
            --primary-light: rgba(129, 140, 248, 0.1);
            --primary-focus: rgba(129, 140, 248, 0.25);
            --primary-gradient: linear-gradient(135deg, #818cf8, #a78bfa);
            --text-primary: #f9fafb;
            --text-secondary: #9ca3af;
            --text-tertiary: #6b7280;
            --bg-primary: #111827;
            --bg-secondary: #1f2937;
            --bg-tertiary: #374151;
            --border-color: #374151;
            --border-hover: #4b5563;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background-color: var(--bg-primary);
            color: var(--text-primary);
            line-height: 1.5;
            transition: background-color var(--transition-normal), color var(--transition-normal);
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            padding: 1.5rem;
        }

        .dialog-header {
            margin-bottom: 1.5rem;
            text-align: center;
        }

        .dialog-header h2 {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .dialog-header p {
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        .form-group {
            margin-bottom: 1.25rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: var(--text-primary);
        }

        .form-control {
            width: 100%;
            padding: 0.75rem 1rem;
            border-radius: var(--radius-lg);
            border: 2px solid var(--border-color);
            background-color: var(--bg-primary);
            color: var(--text-primary);
            font-size: 1rem;
            transition: all var(--transition-normal);
            box-shadow: var(--shadow-sm);
        }

        .form-control:hover {
            border-color: var(--border-hover);
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px var(--primary-focus);
        }

        .form-actions {
            display: flex;
            justify-content: flex-end;
            gap: 1rem;
            margin-top: 1.5rem;
        }

        .btn {
            display: flex;
            align-items: center;
            gap: 0.625rem;
            padding: 0.75rem 1.375rem;
            border-radius: var(--radius-lg);
            font-size: 0.9rem;
            font-weight: 500;
            cursor: pointer;
            transition: all var(--transition-normal);
            position: relative;
            overflow: hidden;
            box-shadow: var(--shadow-sm);
        }

        .btn.primary {
            background: var(--primary-gradient);
            color: white;
            border: none;
        }

        .btn.primary:hover {
            background-image: linear-gradient(135deg, var(--primary-hover), #7c3aed);
            transform: translateY(-2px);
            box-shadow: var(--shadow-md), 0 0 0 3px var(--primary-light);
        }

        .btn.primary:active {
            transform: translateY(0);
            box-shadow: var(--shadow-sm);
        }

        .btn.secondary {
            background-color: var(--bg-tertiary);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
        }

        .btn.secondary:hover {
            background-color: var(--bg-secondary);
            border-color: var(--primary-color);
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .btn.secondary:active {
            transform: translateY(0);
            box-shadow: var(--shadow-sm);
        }
    </style>
</head>

<body>
    <div class="dialog-container">
        <div class="dialog-header">
            <h2>Edit Track Information</h2>
            <p>Modify the track details below</p>
        </div>
        <form id="edit-form">
            <input type="hidden" id="file-path">
            <div class="form-group">
                <label for="title">Titolo:</label>
                <input type="text" id="title" class="form-control" placeholder="Titolo della canzone">
            </div>
            <div class="form-group">
                <label for="artist">Artista:</label>
                <input type="text" id="artist" class="form-control" placeholder="Nome dell'artista">
            </div>
            <div class="form-group">
                <label for="tags">Tags:</label>
                <input type="text" id="tags" class="form-control"
                    placeholder="Inserisci tags separati da virgola (es: hit, nuovo, popolare)">
            </div>
            <div class="form-actions">
                <button type="button" id="cancel-btn" class="btn secondary">Cancel</button>
                <button type="submit" id="save-btn" class="btn primary">Save</button>
            </div>
        </form>
    </div>

    <script>
        // Get form elements
        const form = document.getElementById('edit-form');
        const filePathInput = document.getElementById('file-path');
        const titleInput = document.getElementById('title');
        const artistInput = document.getElementById('artist');
        const tagsInput = document.getElementById('tags');
        const saveBtn = document.getElementById('save-btn');
        const cancelBtn = document.getElementById('cancel-btn');

        // Check if the editDialog API is available
        console.log('Edit dialog window loaded');
        console.log('editDialog API available:', !!window.editDialog);

        // Function to ensure inputs are editable
        function ensureInputsAreEditable() {
            // Explicitly make all inputs editable
            [titleInput, artistInput, tagsInput].forEach(input => {
                // Create a new input element to replace the existing one
                const newInput = document.createElement('input');
                newInput.type = 'text';
                newInput.id = input.id;
                newInput.className = input.className;
                newInput.placeholder = input.placeholder;
                newInput.value = input.value;

                // Replace the old input with the new one
                input.parentNode.replaceChild(newInput, input);

                // Update the reference to the new input
                if (input.id === 'title') titleInput = newInput;
                if (input.id === 'artist') artistInput = newInput;
                if (input.id === 'tags') tagsInput = newInput;
            });

            // Re-add event listeners to the new inputs
            addInputEventListeners();

            console.log('Inputs recreated and made editable');
        }

        // Function to add event listeners to inputs
        function addInputEventListeners() {
            [titleInput, artistInput, tagsInput].forEach(input => {
                // Click handler
                input.addEventListener('click', () => {
                    // Try to force focus and selection
                    input.focus();
                    input.select();
                });

                // Focus handler
                input.addEventListener('focus', () => {
                    // Nothing needed here, just ensure the input is focused
                });

                // Input handler
                input.addEventListener('input', () => {
                    // Nothing needed here, just ensure the input works
                });
            });
        }

        if (window.editDialog) {
            // Listen for track data from main process
            console.log('Setting up onLoadData listener');
            window.editDialog.onLoadData((data) => {
                console.log('Received track data:', data);

                // Reset the form first
                form.reset();

                // Set values
                filePathInput.value = data.filePath || '';
                titleInput.value = data.title || '';
                artistInput.value = data.artist || '';
                tagsInput.value = data.tags || '';

                // Ensure inputs are editable by recreating them
                ensureInputsAreEditable();

                // Focus on the title input
                setTimeout(() => {
                    titleInput.focus();
                    console.log('Title input focused');

                    // Test if the input is actually editable by trying to type something
                    const testValue = titleInput.value;
                    titleInput.value = testValue + 'test';

                    // If the value didn't change, recreate the inputs again
                    if (titleInput.value !== testValue + 'test') {
                        console.log('Input not editable, recreating again');
                        ensureInputsAreEditable();
                        titleInput.focus();
                    } else {
                        // Restore the original value
                        titleInput.value = testValue;
                        titleInput.select();
                    }
                }, 100);
            });
            console.log('onLoadData listener set up');
        } else {
            console.error('editDialog API not available!');
            document.body.innerHTML = '<h1>Error: editDialog API not available</h1>';
        }

        // Handle form submission
        form.addEventListener('submit', (event) => {
            event.preventDefault();

            // Format tags (comma-separated with spaces)
            let tags = tagsInput.value.trim();
            if (tags) {
                tags = tags.split(',')
                    .map(tag => tag.trim())
                    .filter(tag => tag)
                    .join(', ');
            }

            // Send data back to main process
            window.editDialog.saveData({
                filePath: filePathInput.value,
                title: titleInput.value,
                artist: artistInput.value,
                tags: tags
            });
        });

        // Handle cancel button
        cancelBtn.addEventListener('click', () => {
            window.editDialog.cancel();
        });

        // Add initial event listeners to inputs
        addInputEventListeners();

        // Ensure inputs are editable when window gains focus
        window.addEventListener('focus', () => {
            // Check if inputs are working by trying to focus on the title input
            const activeElement = document.activeElement;
            titleInput.focus();

            // If focus didn't work, recreate the inputs
            if (document.activeElement !== titleInput) {
                console.log('Inputs not working, recreating them');
                ensureInputsAreEditable();
            } else if (activeElement) {
                // Restore focus to the previously active element
                activeElement.focus();
            }
        });

        // Add a periodic check to ensure inputs are working
        setInterval(() => {
            // Try to focus on an input
            const activeElement = document.activeElement;
            titleInput.focus();

            // If focus didn't work, recreate the inputs
            if (document.activeElement !== titleInput) {
                console.log('Periodic check: inputs not working, recreating them');
                ensureInputsAreEditable();
            } else if (activeElement) {
                // Restore focus to the previously active element
                activeElement.focus();
            }
        }, 2000); // Check every 2 seconds
    </script>
</body>

</html>