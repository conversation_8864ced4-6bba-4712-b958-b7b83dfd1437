@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap");

:root {
  --primary-color: #6366f1;
  --primary-hover: #4f46e5;
  --primary-light: rgba(99, 102, 241, 0.1);
  --primary-focus: rgba(99, 102, 241, 0.25);
  --primary-gradient: linear-gradient(135deg, #6366f1, #8b5cf6);
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --text-tertiary: #9ca3af;
  --bg-primary: #ffffff;
  --bg-secondary: #f9fafb;
  --bg-tertiary: #f3f4f6;
  --border-color: #e5e7eb;
  --border-hover: #d1d5db;
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 200ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

body.dark-theme {
  --primary-color: #818cf8;
  --primary-hover: #6366f1;
  --primary-light: rgba(129, 140, 248, 0.1);
  --primary-focus: rgba(129, 140, 248, 0.25);
  --primary-gradient: linear-gradient(135deg, #818cf8, #a78bfa);
  --text-primary: #f9fafb;
  --text-secondary: #9ca3af;
  --text-tertiary: #6b7280;
  --bg-primary: #111827;
  --bg-secondary: #1f2937;
  --bg-tertiary: #374151;
  --border-color: #374151;
  --border-hover: #4b5563;
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family:
    "Inter",
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    Roboto,
    Oxygen,
    Ubuntu,
    Cantarell,
    sans-serif;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  line-height: 1.5;
  transition:
    background-color var(--transition-normal),
    color var(--transition-normal);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
  position: sticky;
  top: 0;
  z-index: 10;
  transition:
    background-color var(--transition-normal),
    border-color var(--transition-normal);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.logo {
  font-size: 1.375rem;
  font-weight: 700;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  letter-spacing: -0.025em;
  position: relative;
  padding-left: 0.5rem;
}

.logo::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 0.25rem;
  height: 1.25rem;
  background: var(--primary-gradient);
  border-radius: 1rem;
}

.theme-toggle {
  background: none;
  border: none;
  color: var(--text-primary);
  cursor: pointer;
  padding: 0.5rem;
  border-radius: var(--radius-md);
  transition:
    background-color var(--transition-fast),
    transform var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
}

.theme-toggle:hover {
  background-color: var(--primary-light);
  transform: scale(1.05);
}

.theme-toggle:active {
  transform: scale(0.95);
}

/* Icon visibility management */
.sun-icon,
.moon-icon {
  width: 22px;
  height: 22px;
  transition:
    opacity var(--transition-normal),
    transform var(--transition-normal);
}

body:not(.dark-theme) .moon-icon {
  opacity: 0;
  position: absolute;
  transform: rotate(-90deg);
}

body.dark-theme .sun-icon {
  opacity: 0;
  position: absolute;
  transform: rotate(90deg);
}

body.dark-theme .moon-icon,
body:not(.dark-theme) .sun-icon {
  opacity: 1;
  transform: rotate(0);
}

.app-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 1.5rem 1.75rem 3.5rem;
  animation: fadeIn 0.5s ease-out;
  min-height: calc(100vh - 36px); /* Account for status bar height */
  display: flex;
  flex-direction: column;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.spin {
  animation: spin 1.5s linear infinite;
}

.app-layout {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  flex: 1;
  height: 100%;
}

.main-content {
  display: grid;
  grid-template-columns: 30% 70%;
  gap: 1.5rem;
  flex: 1;
  min-height: 0; /* Important for proper flex behavior */
}

/* Enhanced Responsive layout adjustments */
@media (max-width: 1200px) {
  .main-content {
    grid-template-columns: 32% 68%;
    gap: 1.25rem;
  }
}

@media (max-width: 1024px) {
  .main-content {
    grid-template-columns: 35% 65%;
    gap: 1rem;
  }

  .playlist-header {
    padding: 0.625rem 1rem;
  }

  .playlist-header-actions {
    gap: 0.375rem;
  }
}

@media (max-width: 768px) {
  .main-content {
    grid-template-columns: 1fr;
    grid-template-rows: auto 1fr;
    gap: 1rem;
  }

  .playlist-section {
    margin-bottom: 1rem;
    order: 1;
  }

  .library-section {
    order: 2;
  }

  .playlists-container,
  .track-list,
  .playlist-tracks-container {
    max-height: 350px;
    overflow-y: auto;
  }

  .playlist-header {
    padding: 0.75rem 1rem;
    flex-wrap: wrap;
    gap: 0.75rem;
  }

  .playlist-header h2 {
    font-size: 1.125rem;
  }

  .playlist-header-actions {
    flex-wrap: wrap;
    gap: 0.5rem;
  }
}

@media (max-width: 640px) {
  .playlist-header {
    flex-direction: column;
    align-items: stretch;
    text-align: center;
  }

  .playlist-header-actions {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .app-content {
    padding: 1rem 1rem 3rem;
  }

  .control-panel {
    padding: 1rem;
  }

  .playlists-container,
  .track-list,
  .playlist-tracks-container {
    max-height: 300px;
  }

  .playlist-header {
    padding: 0.625rem 0.875rem;
  }

  .playlist-item {
    padding: 0.75rem 1rem;
  }
}

.library-section {
  text-align: center;
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
  height: 100%;
  min-height: 0; /* Important for proper flex behavior */
  overflow: hidden; /* Prevent overflow from child elements */
}

/* Playlist Section Styles */
.playlist-section {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
  height: 100%;
}

.playlist-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-primary) 100%);
  padding: 1rem 1.5rem;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-color);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.playlist-header::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--primary-gradient);
  opacity: 0.8;
}

.playlist-header:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-1px);
}

.playlist-header-actions {
  display: flex;
  gap: 0.625rem;
  align-items: center;
}

.playlist-header-actions .btn {
  position: relative;
  overflow: hidden;
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-sm);
}

.playlist-header-actions .btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.playlist-header-actions .btn:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

.hashtag-icon {
  font-size: 1.25rem;
  font-weight: 700;
  color: #fbbf24; /* Yellow color to match the magic icon */
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  transition: all var(--transition-normal);
}

.btn:hover .hashtag-icon {
  transform: scale(1.1);
  filter: drop-shadow(0 2px 4px rgba(251, 191, 36, 0.3));
}

.playlist-header h2 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.playlist-header h2::before {
  content: "";
  display: inline-block;
  width: 22px;
  height: 22px;
  background-color: var(--primary-color);
  mask-image: url("../assets/music-note.svg");
  -webkit-mask-image: url("../assets/music-note.svg");
  mask-size: contain;
  -webkit-mask-size: contain;
  mask-position: center;
  -webkit-mask-position: center;
  mask-repeat: no-repeat;
  -webkit-mask-repeat: no-repeat;
  opacity: 0.9;
}

.playlists-container {
  background: linear-gradient(145deg, var(--bg-secondary) 0%, var(--bg-primary) 100%);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
  border: 1px solid var(--border-color);
  min-height: 300px;
  max-height: 600px; /* Set a reasonable max height */
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden; /* Hide overflow to control scrolling */
  position: relative;
}

.playlists-container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: var(--primary-gradient);
  opacity: 0.6;
  z-index: 1;
}

.playlists-container:hover {
  box-shadow: var(--shadow-lg);
}

/* Custom scrollbar styling for playlists container */
.playlists-container {
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: var(--primary-color) var(--bg-tertiary);
}

.playlists-container::-webkit-scrollbar {
  width: 6px;
}

.playlists-container::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
  border-radius: 3px;
}

.playlists-container::-webkit-scrollbar-thumb {
  background: var(--primary-color);
  border-radius: 3px;
  transition: background var(--transition-normal);
}

.playlists-container::-webkit-scrollbar-thumb:hover {
  background: var(--primary-hover);
}

.playlist-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--border-color);
  transition: all var(--transition-normal);
  cursor: pointer;
  position: relative;
  overflow: hidden;
  background-color: var(--bg-primary);
  border-radius: 0;
  /* Make items keyboard focusable */
  tabindex: 0;
}

.playlist-item:first-child {
  border-top-left-radius: var(--radius-xl);
  border-top-right-radius: var(--radius-xl);
}

.playlist-item:last-child {
  border-bottom-left-radius: var(--radius-xl);
  border-bottom-right-radius: var(--radius-xl);
  border-bottom: none;
}

.playlist-item.drag-over {
  background: linear-gradient(135deg, var(--primary-light) 0%, rgba(99, 102, 241, 0.15) 100%);
  border: 2px dashed var(--primary-color);
  transform: scale(1.02);
  box-shadow: var(--shadow-md);
}

.playlist-item::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 4px;
  background: var(--primary-gradient);
  opacity: 0;
  transition: all var(--transition-normal);
  transform: scaleY(0);
  transform-origin: center;
}

.playlist-item::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, transparent 0%, var(--primary-light) 100%);
  opacity: 0;
  transition: opacity var(--transition-normal);
  pointer-events: none;
}

.playlist-item:hover {
  background-color: var(--bg-secondary);
  transform: translateX(4px);
  box-shadow: var(--shadow-md);
  z-index: 2;
}

.playlist-item:hover::before {
  opacity: 1;
  transform: scaleY(1);
}

.playlist-item:hover::after {
  opacity: 0.3;
}

.playlist-item:active {
  transform: translateX(2px);
  box-shadow: var(--shadow-sm);
}

.playlist-info {
  display: flex;
  flex-direction: column;
  gap: 0.375rem;
  text-align: left;
  flex: 1;
  min-width: 0; /* Prevents overflow in flex containers */
  overflow: hidden;
  position: relative;
  z-index: 1;
}

/* Enhanced mobile responsiveness */
@media (max-width: 768px) {
  .playlist-item {
    padding: 0.875rem 1.25rem;
    transform: none; /* Disable transform on mobile for better performance */
  }

  .playlist-item:hover {
    transform: none;
    background-color: var(--primary-light);
  }

  .playlist-name {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 0.95rem;
  }

  .playlist-meta {
    font-size: 0.7rem;
  }

  .playlist-badge {
    min-width: 28px;
    height: 28px;
    font-size: 0.75rem;
  }

  .playlist-item:hover .playlist-badge {
    transform: scale(1.05);
  }
}

/* Touch-friendly improvements */
@media (hover: none) and (pointer: coarse) {
  .playlist-item {
    padding: 1.125rem 1.5rem; /* Larger touch targets */
  }

  .playlist-header-actions .btn {
    min-height: 44px; /* iOS recommended touch target size */
    min-width: 44px;
  }

  .playlist-item:hover {
    transform: none;
  }

  .playlist-item:active {
    background-color: var(--primary-light);
    transform: scale(0.98);
  }
}

.playlist-name {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 1.05rem;
  display: flex;
  align-items: center;
  gap: 0.625rem;
  transition: all var(--transition-normal);
  position: relative;
}

.playlist-name::before {
  content: "";
  display: inline-block;
  width: 18px;
  height: 18px;
  background-color: var(--primary-color);
  mask-image: url("../assets/music-note.svg");
  -webkit-mask-image: url("../assets/music-note.svg");
  mask-size: contain;
  -webkit-mask-size: contain;
  mask-position: center;
  -webkit-mask-position: center;
  mask-repeat: no-repeat;
  -webkit-mask-repeat: no-repeat;
  opacity: 0.8;
  transition: all var(--transition-normal);
  flex-shrink: 0;
}

/* Enhanced styling for generated playlists */
.playlist-item.generated {
  background: linear-gradient(135deg, var(--bg-secondary) 0%, rgba(251, 191, 36, 0.05) 100%);
  border-left: 4px solid #fbbf24;
  position: relative;
}

.playlist-item.generated::before {
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
}

.playlist-item.generated .playlist-name {
  color: var(--text-primary);
}

.playlist-item.generated .playlist-name::before {
  mask-image: url("../assets/magic-icon.svg");
  -webkit-mask-image: url("../assets/magic-icon.svg");
  background-color: #fbbf24;
  filter: drop-shadow(0 1px 2px rgba(251, 191, 36, 0.3));
}

.playlist-item.generated:hover {
  background: linear-gradient(135deg, var(--bg-secondary) 0%, rgba(251, 191, 36, 0.1) 100%);
  box-shadow: 0 4px 12px rgba(251, 191, 36, 0.15), var(--shadow-md);
}

.playlists-separator {
  padding: 1rem 1.5rem;
  font-weight: 700;
  color: var(--text-secondary);
  background: linear-gradient(135deg, var(--bg-tertiary) 0%, var(--bg-secondary) 100%);
  border-bottom: 1px solid var(--border-color);
  font-size: 0.85rem;
  text-transform: uppercase;
  letter-spacing: 1px;
  position: relative;
  transition: all var(--transition-normal);
}

.playlists-separator::before {
  content: "";
  position: absolute;
  left: 1.5rem;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  background-color: var(--primary-color);
  mask-image: url("../assets/magic-icon.svg");
  -webkit-mask-image: url("../assets/magic-icon.svg");
  mask-size: contain;
  -webkit-mask-size: contain;
  mask-position: center;
  -webkit-mask-position: center;
  mask-repeat: no-repeat;
  -webkit-mask-repeat: no-repeat;
  opacity: 0.6;
}

.playlists-separator {
  padding-left: 3.5rem; /* Make room for the icon */
}

.playlist-item:hover .playlist-name::before {
  transform: scale(1.15) rotate(5deg);
  opacity: 1;
}

.playlist-item.generated:hover .playlist-name::before {
  transform: scale(1.15) rotate(-5deg);
  filter: drop-shadow(0 2px 4px rgba(251, 191, 36, 0.4));
}

.playlist-meta {
  font-size: 0.8rem;
  color: var(--text-secondary);
  display: flex;
  gap: 1rem;
  align-items: center;
  transition: all var(--transition-normal);
}

.playlist-track-count {
  font-weight: 600;
  color: var(--primary-color);
  background-color: var(--primary-light);
  padding: 0.125rem 0.375rem;
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
}

.playlist-badge {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  height: 32px;
  background: var(--primary-gradient);
  color: white;
  font-size: 0.8rem;
  font-weight: 700;
  border-radius: 16px;
  padding: 0 0.625rem;
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.playlist-badge::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 100%);
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.playlist-item:hover .playlist-badge {
  transform: scale(1.15) rotate(5deg);
  box-shadow: var(--shadow-md);
}

.playlist-item:hover .playlist-badge::before {
  opacity: 1;
}

.playlist-item.generated .playlist-badge {
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  box-shadow: 0 2px 8px rgba(251, 191, 36, 0.3);
}

.playlist-item.generated:hover .playlist-badge {
  transform: scale(1.15) rotate(-5deg);
  box-shadow: 0 4px 12px rgba(251, 191, 36, 0.4);
}

/* Accessibility and Focus States */
.playlist-item:focus-visible {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
  background-color: var(--primary-light);
}

.playlist-header-actions .btn:focus-visible {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
  box-shadow: 0 0 0 4px var(--primary-focus);
}

/* Smooth scrolling for playlist container */
.playlists-container {
  scroll-behavior: smooth;
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  .playlist-item,
  .playlist-badge,
  .playlist-name::before,
  .playlist-header,
  .playlists-container {
    transition: none;
  }

  .playlist-item:hover {
    transform: none;
  }

  .playlist-item:hover .playlist-badge,
  .playlist-item:hover .playlist-name::before {
    transform: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .playlist-item {
    border: 2px solid var(--text-primary);
  }

  .playlist-item.generated {
    border-left: 4px solid #fbbf24;
    border-top: 2px solid var(--text-primary);
    border-right: 2px solid var(--text-primary);
    border-bottom: 2px solid var(--text-primary);
  }

  .playlist-badge {
    border: 2px solid white;
  }
}

.playlist-tracks-container {
  background-color: var(--bg-secondary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
  border: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 300px;
  max-height: 600px; /* Set a reasonable max height */
  overflow-y: auto; /* Add vertical scrollbar when content exceeds height */
}

.playlist-tracks-container.hidden {
  display: none;
}

.playlist-tracks-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.875rem 1.25rem;
  border-bottom: 1px solid var(--border-color);
  background: var(--primary-gradient);
  color: white;
}

.playlist-tracks-header h3 {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: white;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.playlist-tracks-header h3::before {
  content: "";
  display: inline-block;
  width: 18px;
  height: 18px;
  background-color: white;
  mask-image: url("../assets/music-note.svg");
  -webkit-mask-image: url("../assets/music-note.svg");
  mask-size: contain;
  -webkit-mask-size: contain;
  mask-position: center;
  -webkit-mask-position: center;
  mask-repeat: no-repeat;
  -webkit-mask-repeat: no-repeat;
  transition: transform var(--transition-normal);
}

.playlist-actions {
  display: flex;
  gap: 0.5rem;
}

.btn.small {
  padding: 0.5rem 0.75rem;
  font-size: 0.8rem;
}

.playlist-tracks {
  min-height: 200px;
  flex: 1;
  overflow-y: auto;
  background-color: var(--bg-primary);
}

/* Track subset styles */
.track-subset {
  margin-bottom: 1rem;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  overflow: hidden;
  background-color: var(--bg-secondary);
  transition: all var(--transition-normal);
  position: relative;
}

.track-subset:hover {
  box-shadow: var(--shadow-md);
}

.track-subset.dragging {
  opacity: 0.7;
  box-shadow: var(--shadow-lg);
  cursor: grabbing;
  z-index: 100;
  transform: scale(1.02);
}

.track-subset.drag-over {
  border: 2px dashed var(--primary-color);
  background-color: var(--primary-light);
  position: relative;
}

.track-subset.drag-over::before,
.track-subset.drag-over::after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  height: 10px;
  background-color: var(--primary-color);
  opacity: 0.5;
  z-index: 10;
  pointer-events: none;
}

.track-subset.drag-over::before {
  top: 0;
  border-top-left-radius: var(--radius-lg);
  border-top-right-radius: var(--radius-lg);
}

.track-subset.drag-over::after {
  bottom: 0;
  border-bottom-left-radius: var(--radius-lg);
  border-bottom-right-radius: var(--radius-lg);
}

.track-subset-header {
  padding: 0.75rem 1rem;
  background-color: var(--bg-tertiary);
  border-bottom: 1px solid var(--border-color);
  cursor: grab;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all var(--transition-normal);
  position: relative;
}

.track-subset-header:hover {
  background-color: var(--primary-light);
}

.track-subset-header::before {
  content: '⋮⋮';
  position: absolute;
  left: 0.5rem;
  font-size: 1.2rem;
  color: var(--text-secondary);
  opacity: 0.5;
}

.track-subset-header:hover::before {
  opacity: 1;
  color: var(--primary-color);
}

.track-subset-title {
  font-weight: 600;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding-left: 1.5rem; /* Add padding to accommodate the drag handle */
}

.cycle-number {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background-color: var(--primary-color);
  color: white;
  border-radius: 50%;
  font-size: 0.8rem;
  font-weight: 700;
}

.track-subset-tracks {
  padding: 0.5rem 0;
}

.playlist-track-item {
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem;
  border-bottom: 1px solid var(--border-color);
  transition: all var(--transition-normal);
  position: relative;
  cursor: grab;
  background-color: var(--bg-primary);
}

.track-number {
  min-width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.85rem;
  color: var(--primary-color);
  background-color: var(--primary-light);
  border-radius: 50%;
  margin-right: 0.5rem;
  transition: all var(--transition-normal);
}

.playlist-track-item:hover .track-number {
  transform: scale(1.1);
  color: white;
  background-color: var(--primary-color);
}

.drag-handle {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: grab;
  padding: 0.25rem;
  border-radius: var(--radius-md);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.drag-handle::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--primary-light);
  opacity: 0;
  transition: opacity var(--transition-normal);
  border-radius: var(--radius-md);
  z-index: -1;
}

.drag-handle:hover {
  background-color: var(--bg-tertiary);
}

.drag-handle:hover::before {
  opacity: 0.3;
}

.playlist-track-item:hover .drag-handle::before {
  opacity: 0.15;
}

.drag-handle .icon {
  width: 16px;
  height: 16px;
  color: var(--text-secondary);
  transition: transform var(--transition-normal), color var(--transition-normal);
}

.drag-handle:hover .icon {
  color: var(--primary-color);
  transform: scale(1.1);
}

.playlist-track-item.dragging {
  opacity: 0.5;
  background-color: var(--primary-light);
  box-shadow: var(--shadow-md);
  cursor: grabbing;
  border: 1px dashed var(--primary-color);
}

.playlist-track-item.drag-over {
  border-top: 2px solid var(--primary-color);
  margin-top: -1px;
  position: relative;
  z-index: 1;
  background-color: var(--primary-light);
  opacity: 1;
}

.playlist-tracks.drag-over {
  background-color: var(--primary-light);
  border: 2px dashed var(--primary-color);
}

.playlist-track-item::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 3px;
  background: var(--primary-gradient);
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.playlist-track-item:hover {
  background-color: var(--primary-light);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
  z-index: 1;
}

.playlist-track-item:hover::before {
  opacity: 1;
}

.playlist-track-info {
  display: flex;
  flex-direction: column;
  gap: 0.15rem;
  text-align: left;
  flex: 1;
  margin-left: 0.5rem;
  min-width: 0;
  overflow: hidden;
}

.playlist-track-main-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.playlist-track-title {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.playlist-track-artist {
  font-size: 0.8rem;
  color: var(--text-secondary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.playlist-track-duration {
  font-size: 0.7rem;
  color: var(--primary-color);
  font-weight: 600;
  background-color: var(--primary-light);
  padding: 0.15rem 0.4rem;
  border-radius: var(--radius-sm);
  transition: all var(--transition-normal);
}

.playlist-track-item:hover .playlist-track-duration {
  transform: scale(1.05);
}

.playlist-track-source {
  display: flex;
  align-items: center;
  gap: 0.3rem;
  font-size: 0.75rem;
  color: var(--text-secondary);
  margin-top: 0.2rem;
}

.source-label {
  opacity: 0.7;
}

.source-playlist-name {
  color: var(--primary-color);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-normal);
  position: relative;
}

.source-playlist-name:hover {
  text-decoration: underline;
  opacity: 0.9;
}

.source-playlist-name::before {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 1px;
  background-color: var(--primary-color);
  transition: width var(--transition-normal);
}

.source-playlist-name:hover::before {
  width: 100%;
}

.playlist-track-actions {
  display: flex;
  gap: 0.4rem;
  margin-left: auto;
}

.replace-track-button .icon {
  color: #f59e0b; /* Amber color for the replace button */
}

.replace-track-button:hover .icon {
  color: #d97706; /* Darker amber on hover */
}

.track-up-button .icon,
.track-down-button .icon {
  color: #6b7280; /* Gray color for the move buttons */
}

.track-up-button:hover .icon {
  color: #3b82f6; /* Blue color on hover */
}

.track-down-button:hover .icon {
  color: #3b82f6; /* Blue color on hover */
}

/* Spinning animation for loading state */
.icon.spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.playlist-selection-container {
  margin-bottom: 1.5rem;
}

.playlist-options {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-top: 1rem;
  max-height: 300px;
  overflow-y: auto;
}

.playlist-option {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-color);
  background-color: var(--bg-primary);
  cursor: pointer;
  transition: all var(--transition-normal);
}

.playlist-option:hover {
  background-color: var(--primary-light);
  border-color: var(--primary-color);
  transform: translateY(-2px);
}

.playlist-option.selected {
  background-color: var(--primary-light);
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px var(--primary-focus);
}

.playlist-option-name {
  font-weight: 500;
  color: var(--text-primary);
  margin-left: 0.5rem;
}

/* Control panel styles */
.control-panel {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 1.25rem 1.25rem 0 1.25rem;
  background: var(--primary-gradient);
  border-radius: var(--radius-xl);
  margin-bottom: 1rem;
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
  border: none;
  position: relative;
  overflow: hidden;
  padding-bottom: 0;
}

.control-panel::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.1);
  z-index: 0;
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.control-panel:hover::before {
  opacity: 1;
}

.control-panel-top {
  display: flex;
  align-items: center;
  gap: 1rem;
  width: 100%;
  z-index: 1;
}

@media (max-width: 767px) {
  .control-panel-top {
    flex-direction: column;
    align-items: stretch;
  }

  .actions {
    flex-wrap: wrap;
    justify-content: center;
    margin-top: 0.75rem;
  }

  .search-section {
    width: 100%;
  }

  .actions-section {
    justify-content: center;
  }

  .tag-filter-bar {
    padding: 0.75rem;
  }

  .tag-filters-container {
    max-height: 6rem;
  }

  .search-results-count {
    padding: 0.6rem 1rem;
  }
}

@media (max-width: 480px) {
  .btn.compact span {
    display: none;
  }

  .btn.compact {
    padding: 0.5rem;
  }

  .btn.compact .icon {
    margin: 0;
  }

  .tag-filter-bar {
    padding: 0.6rem;
  }

  .tag-filter-header {
    margin-bottom: 0.5rem;
    padding-bottom: 0.4rem;
  }

  .tag-filters-container {
    max-height: 5rem;
  }

  .search-results-count {
    padding: 0.5rem 0.75rem;
    font-size: 0.8rem;
  }

  .search-results-count::before {
    width: 14px;
    height: 14px;
    margin-right: 0.5rem;
  }
}

.search-section {
  flex: 1;
  width: 100%;
  z-index: 1;
}

.actions-section {
  display: flex;
  justify-content: flex-end;
  z-index: 1;
  white-space: nowrap;
}

.stats-info {
  display: flex;
  gap: 1.5rem;
  font-size: 0.875rem;
  background-color: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-xl);
  padding: 0.875rem 1.25rem;
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.stats-info::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: var(--primary-gradient);
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.stats-info:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--primary-color);
}

.stats-info:hover::before {
  opacity: 1;
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  text-align: center;
  position: relative;
  padding: 0 0.75rem;
}

.stat-item:not(:last-child)::after {
  content: "";
  position: absolute;
  right: -0.75rem;
  top: 0.25rem;
  bottom: 0.25rem;
  width: 1px;
  background-color: var(--border-color);
}

.stat-label {
  color: var(--text-secondary);
  font-size: 0.7rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-weight: 600;
}

.stat-value {
  font-size: 1.125rem;
  font-weight: 700;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  transition: transform var(--transition-normal);
}

.stats-info:hover .stat-value {
  transform: scale(1.05);
}

.actions {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.btn {
  display: flex;
  align-items: center;
  gap: 0.625rem;
  padding: 0.75rem 1.375rem;
  border-radius: var(--radius-lg);
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.btn::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  background: rgba(255, 255, 255, 0.5);
  opacity: 0;
  border-radius: 100%;
  transform: scale(1, 1) translate(-50%);
  transform-origin: 50% 50%;
}

.btn:active::after {
  animation: ripple 0.6s ease-out;
}

@keyframes ripple {
  0% {
    transform: scale(0, 0);
    opacity: 0.5;
  }

  100% {
    transform: scale(20, 20);
    opacity: 0;
  }
}

.btn.primary {
  background: var(--primary-gradient);
  color: white;
  border: none;
}

.btn.primary:hover {
  background-image: linear-gradient(135deg, var(--primary-hover), #7c3aed);
  transform: translateY(-2px);
  box-shadow:
    var(--shadow-md),
    0 0 0 3px var(--primary-light);
}

.btn.primary:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

.btn.compact {
  padding: 0 0.75rem;
  font-size: 0.85rem;
  border-radius: var(--radius-lg);
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn.compact .icon {
  width: 18px;
  height: 18px;
}

.btn.compact span {
  font-weight: 600;
  margin-left: 0.25rem;
}

/* Add spacing for the playlist buttons */
#create-playlist-btn {
  margin-left: 0.5rem;
}

#generate-playlist-btn {
  margin-right: 0.25rem;
}

#generate-playlist-btn img.icon {
  filter: brightness(1.2) drop-shadow(0 0 2px rgba(255, 255, 255, 0.5));
  width: 18px;
  height: 18px;
}

.btn.secondary {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.btn.secondary:hover {
  background-color: var(--bg-secondary);
  border-color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.btn.secondary:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

.btn.white-outline {
  background-color: transparent;
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.5);
  height: 40px;
  padding: 0 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn.white-outline:hover {
  background-color: rgba(255, 255, 255, 0.1);
  border-color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.btn.white-outline:active {
  transform: translateY(0);
  box-shadow: none;
}

.btn.white-outline .icon {
  opacity: 0.9;
}

.btn.white-outline:hover .icon {
  opacity: 1;
}

.btn.danger {
  background: linear-gradient(135deg, #ef4444, #b91c1c);
  color: white;
  border: none;
}

.btn.danger:hover {
  background: linear-gradient(135deg, #dc2626, #991b1b);
  transform: translateY(-2px);
  box-shadow:
    var(--shadow-md),
    0 0 0 3px rgba(239, 68, 68, 0.2);
}

.btn.danger:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

.btn.info {
  background: linear-gradient(135deg, #0ea5e9, #0369a1);
  color: white;
  border: none;
}

.btn.info:hover {
  background: linear-gradient(135deg, #0284c7, #075985);
  transform: translateY(-2px);
  box-shadow:
    var(--shadow-md),
    0 0 0 3px rgba(14, 165, 233, 0.2);
}

.btn.info:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

.btn .icon {
  transition: transform var(--transition-normal);
}

.btn:hover .icon {
  transform: scale(1.1);
}

/* Status Bar */
.status-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 36px;
  background-color: var(--bg-secondary);
  border-top: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 0 1.5rem;
  font-size: 0.8rem;
  color: var(--text-secondary);
  z-index: 100;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  overflow-x: auto;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0 1rem;
  height: 100%;
  border-right: 1px solid var(--border-color);
  white-space: nowrap;
}

.status-item:last-child {
  border-right: none;
  margin-left: auto;
}

@media (max-width: 768px) {
  .status-bar {
    padding: 0 0.75rem;
  }

  .status-item {
    padding: 0 0.5rem;
  }

  .status-label {
    display: none;
  }

  .folder-status {
    min-width: auto;
  }
}

@media (max-width: 480px) {
  .status-bar {
    height: 40px;
  }
}

.status-icon {
  width: 14px;
  height: 14px;
  color: var(--primary-color);
  opacity: 0.8;
}

.status-label {
  font-weight: 600;
  color: var(--text-secondary);
}

.status-value {
  font-weight: 700;
  color: var(--primary-color);
  max-width: 150px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.folder-status {
  min-width: 200px;
}

.folder-status .status-icon {
  color: var(--primary-color);
}

.folder-status.hidden {
  display: none;
}

/* Add padding to main content to account for status bar */
.app-content {
  padding-bottom: 3rem;
}

/* Search styles */
.search-container {
  width: 100%;
  max-width: 100%;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  transition: all var(--transition-normal);
}

.search-input-wrapper:focus-within {
  transform: scale(1.01);
}

.search-icon {
  position: absolute;
  left: 12px;
  width: 16px;
  height: 16px;
  color: rgba(255, 255, 255, 0.7);
  transition: all var(--transition-normal);
}

.search-input-wrapper:focus-within .search-icon {
  color: white;
  transform: scale(1.1);
}

.search-input {
  width: 100%;
  padding: 0.6rem 5rem 0.6rem 2.5rem;
  border-radius: var(--radius-xl);
  border: none;
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  font-size: 0.9rem;
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-sm);
  font-weight: 500;
  letter-spacing: 0.01em;
  backdrop-filter: blur(5px);
  height: 40px;
}

.search-input:hover {
  background-color: rgba(255, 255, 255, 0.25);
  box-shadow: var(--shadow-md);
}

.search-input:focus {
  outline: none;
  background-color: rgba(255, 255, 255, 0.3);
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.2);
}

.search-input::placeholder {
  color: rgba(255, 255, 255, 0.7);
  font-weight: 400;
}

.clear-search-btn {
  position: absolute;
  right: 8px;
  background: none;
  border: none;
  cursor: pointer;
  color: rgba(255, 255, 255, 0.7);
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all var(--transition-normal);
  width: 24px;
  height: 24px;
  z-index: 2;
  top: 50%;
  transform: translateY(-50%);
}

.clear-search-btn svg {
  width: 16px;
  height: 16px;
  transition: transform var(--transition-fast);
}

.clear-search-btn:hover {
  color: white;
  background-color: rgba(255, 255, 255, 0.2);
}

.clear-search-btn:hover svg {
  transform: scale(1.1);
}

.clear-search-btn:active svg {
  transform: scale(0.9);
}

.clear-search-btn.hidden {
  display: none;
}

.search-include-path-label {
  position: absolute;
  right: 42px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  user-select: none;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  transition: all var(--transition-normal);
  background-color: rgba(255, 255, 255, 0.1);
  border: 2px solid transparent;
  z-index: 2;
  top: 50%;
  transform: translateY(-50%);
}

.search-include-path-label:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.search-include-path-label .checkbox-input {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

.path-search-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.path-search-icon {
  width: 16px;
  height: 16px;
  color: rgba(255, 255, 255, 0.7);
  transition: all var(--transition-normal);
}

.search-include-path-label:hover .path-search-icon {
  color: white;
}

/* Stile quando il checkbox è selezionato */
.checkbox-input:checked ~ .path-search-wrapper .path-search-icon {
  color: #ffeb3b;
  filter: drop-shadow(0 0 3px rgba(255, 235, 59, 0.7));
}

.checkbox-input:checked ~ .path-search-wrapper {
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 50%;
}

.checkbox-input:checked ~ .search-include-path-label {
  background-color: rgba(255, 255, 255, 0.3);
  border-color: #ffeb3b;
  box-shadow: 0 0 8px rgba(255, 235, 59, 0.5);
}



.search-container {
  position: relative;
  margin-bottom: 0;
  width: 100%;
  flex: 1;
}

.search-section {
  padding: 0;
  margin: 0;
  width: 100%;
}

/* Removed duplicate control-panel styles that were overriding the main ones */

.actions-section {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  flex-wrap: wrap;
  gap: 0.5rem;
  min-width: max-content;
}

.search-results-count {
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.95);
  text-align: left;
  padding: 0.75rem 1.25rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  animation: fadeIn 0.3s ease-out;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  border-radius: 0 0 var(--radius-xl) var(--radius-xl);
  background-color: rgba(0, 0, 0, 0.25);
  width: 100%;
  margin-top: 1rem;
  margin-bottom: 0;
  margin-left: -1.25rem;
  margin-right: -1.25rem;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(5px);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.search-results-count::before {
  content: "";
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-right: 0.75rem;
  background-color: white;
  mask-image: url("../assets/search-results-icon.svg");
  -webkit-mask-image: url("../assets/search-results-icon.svg");
  mask-size: contain;
  -webkit-mask-size: contain;
  mask-position: center;
  -webkit-mask-position: center;
  mask-repeat: no-repeat;
  -webkit-mask-repeat: no-repeat;
}

#results-count {
  font-weight: 700;
  color: white;
  background-color: var(--primary-color);
  padding: 0.1rem 0.4rem;
  border-radius: var(--radius-sm);
  margin: 0 0.3rem;
}

.search-results-count.hidden {
  display: none;
}

.highlight {
  background-color: var(--primary-focus);
  padding: 0.1rem 0.2rem;
  border-radius: var(--radius-sm);
  font-weight: 600;
  color: var(--primary-color);
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  user-select: none;
  font-size: 0.9rem;
  color: var(--text-secondary);
  transition: color var(--transition-normal);
}

.checkbox-label:hover {
  color: var(--text-primary);
}

.checkbox-input {
  appearance: none;
  -webkit-appearance: none;
  width: 1.125rem;
  height: 1.125rem;
  border: 2px solid var(--border-color);
  border-radius: var(--radius-sm);
  background-color: var(--bg-primary);
  cursor: pointer;
  position: relative;
  transition: all var(--transition-normal);
}

.checkbox-input:checked {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.checkbox-input:checked::after {
  content: "";
  position: absolute;
  left: 4px;
  top: 1px;
  width: 5px;
  height: 9px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.checkbox-input:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--primary-focus);
}

/* Track list styles */
.track-list {
  background-color: var(--bg-secondary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  transition:
    background-color var(--transition-normal),
    box-shadow var(--transition-normal);
  border: 1px solid var(--border-color);
  flex: 1;
  min-height: 300px;
  max-height: 600px; /* Set a reasonable max height */
  overflow-y: auto; /* Ensure scrollbar appears when needed */
}

.track-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 1.125rem;
  border-bottom: 1px solid var(--border-color);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
  cursor: grab;
}

.track-select {
  margin-right: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.track-checkbox {
  appearance: none;
  -webkit-appearance: none;
  width: 1.125rem;
  height: 1.125rem;
  border: 2px solid var(--border-color);
  border-radius: var(--radius-sm);
  background-color: var(--bg-primary);
  cursor: pointer;
  position: relative;
  transition: all var(--transition-normal);
}

.track-checkbox:checked {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.track-checkbox:checked::after {
  content: "";
  position: absolute;
  left: 4px;
  top: 1px;
  width: 5px;
  height: 9px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.track-checkbox:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--primary-focus);
}

.track-item.selected, .playlist-track-item.selected {
  background-color: var(--primary-light);
  border-left: 3px solid var(--primary-color);
}

.track-item.dragging {
  opacity: 0.5;
  background-color: var(--primary-light);
  box-shadow: var(--shadow-md);
  cursor: grabbing;
}

.track-item.drag-over {
  border-top: 2px solid var(--primary-color);
}

.drag-handle {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.75rem;
  opacity: 0.5;
  cursor: grab;
  transition: opacity var(--transition-normal);
}

.track-item:hover .drag-handle,
.playlist-track-item:hover .drag-handle {
  opacity: 0.8;
}

.track-item.dragging .drag-handle,
.playlist-track-item.dragging .drag-handle {
  opacity: 1;
  cursor: grabbing;
}

.track-item::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 3px;
  background: var(--primary-gradient);
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.track-item:hover {
  background-color: var(--primary-light);
}

.track-item:hover::before {
  opacity: 1;
}

.track-item:last-child {
  border-bottom: none;
}

.track-info {
  flex: 1;
  text-align: left;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  padding-left: 0.5rem;
  min-width: 0; /* Prevents overflow in flex containers */
  overflow: hidden;
}

@media (max-width: 768px) {
  .track-item {
    padding: 0.875rem 1rem;
  }

  .track-title,
  .track-artist,
  .track-path {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.track-main-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.track-title-container {
  display: flex;
  align-items: center;
}

.track-title {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.95rem;
  transition: color var(--transition-normal);
  display: flex;
  align-items: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.track-title::before {
  content: "";
  display: inline-block;
  width: 14px;
  height: 14px;
  margin-right: 0.5rem;
  background-color: var(--primary-color);
  mask-image: url("../assets/music-note.svg");
  -webkit-mask-image: url("../assets/music-note.svg");
  mask-size: contain;
  -webkit-mask-size: contain;
  mask-position: center;
  -webkit-mask-position: center;
  mask-repeat: no-repeat;
  -webkit-mask-repeat: no-repeat;
  opacity: 0.7;
  transition:
    opacity var(--transition-normal),
    transform var(--transition-normal);
}

.track-item:hover .track-title::before {
  transform: scale(1.1);
  opacity: 1;
}

.track-artist {
  font-size: 0.8rem;
  color: var(--text-secondary);
  font-weight: 500;
  transition: color var(--transition-normal);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.track-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.3rem;
  margin-top: 0.15rem;
}

.tag {
  font-size: 0.65rem;
  color: var(--primary-color);
  font-weight: 600;
  background-color: var(--primary-light);
  padding: 0.15rem 0.4rem;
  border-radius: var(--radius-sm);
  transition: all var(--transition-normal);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.track-item:hover .tag {
  transform: scale(1.05);
}

.new-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 0.6rem;
  font-weight: 700;
  color: #111827;
  background-color: #fbbf24; /* Colore giallo */
  padding: 0.1rem 0.3rem;
  border-radius: var(--radius-sm);
  margin-left: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(251, 191, 36, 0.4);
  }
  70% {
    box-shadow: 0 0 0 4px rgba(251, 191, 36, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(251, 191, 36, 0);
  }
}

.track-details {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-top: 0.15rem;
  font-size: 0.75rem;
}

.track-duration {
  color: var(--primary-color);
  font-weight: 600;
  background-color: var(--primary-light);
  padding: 0.15rem 0.4rem;
  border-radius: var(--radius-sm);
  transition: all var(--transition-normal);
  font-size: 0.7rem;
}

.track-item:hover .track-duration {
  transform: scale(1.05);
}

.track-path {
  font-size: 0.75rem;
  color: var(--text-tertiary);
  opacity: 0.8;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  transition: color var(--transition-normal);
}

.track-item:hover .track-path {
  opacity: 1;
  color: var(--text-secondary);
}

/* Playlist information in track details */
.track-playlists {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  margin-top: 0.15rem;
}

.track-playlists-label {
  font-size: 0.7rem;
  color: var(--text-secondary);
  font-weight: 600;
}

.track-playlists-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.3rem;
}

.track-playlist {
  font-size: 0.7rem;
  color: var(--primary-color);
  font-weight: 600;
  background-color: var(--primary-light);
  padding: 0.15rem 0.4rem;
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: all var(--transition-normal);
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.track-playlist::before {
  content: "";
  display: inline-block;
  width: 8px;
  height: 8px;
  background-color: var(--primary-color);
  mask-image: url("../assets/music-note.svg");
  -webkit-mask-image: url("../assets/music-note.svg");
  mask-size: contain;
  -webkit-mask-size: contain;
  mask-position: center;
  -webkit-mask-position: center;
  mask-repeat: no-repeat;
  -webkit-mask-repeat: no-repeat;
  opacity: 0.7;
  transition:
    opacity var(--transition-normal),
    transform var(--transition-normal);
}

.track-playlist:hover {
  transform: scale(1.05);
  background-color: var(--primary-focus);
}

.track-playlist:hover::before {
  opacity: 1;
  transform: scale(1.1);
}

.track-actions {
  display: flex;
  gap: 0.4rem;
}

.track-button {
  padding: 0.4rem;
  background: none;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  color: var(--text-primary);
  cursor: pointer;
  transition: all var(--transition-normal);
  display: flex;
  align-items: center;
  justify-content: center;
}

.track-button:hover {
  background-color: var(--primary-light);
  border-color: var(--primary-color);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
  color: var(--primary-color);
}

.track-button:active {
  transform: translateY(0);
}

.track-button svg {
  width: 16px;
  height: 16px;
  transition: transform var(--transition-fast);
}

.track-button:hover svg {
  transform: scale(1.1);
}

.empty-state {
  text-align: center;
  padding: 3.5rem 2rem;
  color: var(--text-secondary);
  font-size: 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}

/* Edit Tags Popup Styles */
.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
  opacity: 1;
  visibility: visible;
  transition:
    opacity 0.3s ease,
    visibility 0.3s ease;
  overflow-y: auto;
  padding: 20px 0;
}

/* Classe per mostrare il popup */
.popup-overlay:not(.hidden):not(.fully-hidden) {
  display: flex;
  visibility: visible;
  opacity: 1;
  pointer-events: auto;
}

/* Assicuriamoci che il popup sia nascosto quando ha la classe hidden */
.popup-overlay.hidden {
  opacity: 0;
  visibility: hidden;
  pointer-events: none;
  transition:
    opacity 0.3s ease,
    visibility 0.3s ease;
}

/* Classe per nascondere completamente un elemento */
.fully-hidden {
  display: none !important;
}

/* General hidden class for any element */
.hidden {
  display: none !important;
}

.popup-container {
  background-color: var(--bg-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  width: 90%;
  max-width: 500px;
  overflow: hidden;
  border: 1px solid var(--border-color);
  transform: scale(1);
  opacity: 1;
  transition:
    transform 0.3s ease,
    opacity 0.3s ease;
}

@media (max-width: 480px) {
  .popup-container {
    width: 95%;
  }

  .popup-header {
    padding: 1rem;
  }

  .popup-content {
    padding: 1rem;
  }

  .form-actions {
    flex-direction: column;
    gap: 0.5rem;
  }

  .form-actions button {
    width: 100%;
  }
}

.popup-overlay.hidden .popup-container {
  transform: scale(0.95);
  opacity: 0;
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.25rem 1.5rem;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--bg-secondary);
}

.popup-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
}

.close-btn {
  background: none;
  border: none;
  cursor: pointer;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem;
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
}

.close-btn:hover {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
}

.close-btn svg {
  width: 20px;
  height: 20px;
}

.popup-content {
  padding: 1.5rem;
}

.form-group {
  margin-bottom: 1.25rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text-primary);
}

.form-control {
  width: 100%;
  padding: 0.75rem 1rem;
  border-radius: var(--radius-lg);
  border: 2px solid var(--border-color);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-size: 1rem;
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-sm);
}

.form-control:hover {
  border-color: var(--border-hover);
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px var(--primary-focus);
}

.form-control:focus-visible {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px var(--primary-focus);
}

.form-help {
  display: block;
  margin-top: 0.25rem;
  font-size: 0.8rem;
  color: var(--text-tertiary);
  font-style: italic;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1.5rem;
}

.form-actions button {
  flex: 1;
  min-width: 120px;
}

@media (max-width: 480px) {
  .form-actions {
    flex-direction: column;
    gap: 0.5rem;
  }

  .form-actions button {
    width: 100%;
  }
}

/* Default Tags Styles */
.default-tags-section {
  margin-top: 1rem;
  border-top: 1px solid var(--border-color);
  padding-top: 1rem;
}

.default-tags-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.default-tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.default-tag-item {
  display: inline-flex;
  align-items: center;
  padding: 0.3rem 0.6rem;
  background-color: var(--primary-light);
  color: var(--primary-color);
  border-radius: var(--radius-sm);
  font-size: 0.8rem;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-normal);
  border: 1px solid transparent;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.default-tag-item:hover {
  transform: scale(1.05);
  background-color: var(--primary-color);
  color: white;
}

.default-tag-item.active {
  background-color: var(--primary-color);
  color: white;
  border: 1px solid var(--primary-hover);
}

/* Classi per i colori dei tag predefiniti */
.tag-color {
  color: white !important;
}

.tag-color-red {
  background-color: #ef4444 !important;
}

.tag-color-orange {
  background-color: #f97316 !important;
}

.tag-color-yellow {
  background-color: #eab308 !important;
}

.tag-color-green {
  background-color: #22c55e !important;
}

.tag-color-blue {
  background-color: #3b82f6 !important;
}

.tag-color-purple {
  background-color: #a855f7 !important;
}

.tag-color-pink {
  background-color: #ec4899 !important;
}

.tag-color-gray {
  background-color: #6b7280 !important;
}

.tag-color-black {
  background-color: #1f2937 !important;
}

/* Default Tags Management Popup */
.default-tags-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.default-tags-list-header h4 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
}

.default-tags-list {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background-color: var(--bg-primary);
  margin-bottom: 1rem;
}

.default-tag-list-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--border-color);
  transition: background-color var(--transition-normal);
}

.default-tag-list-item:last-child {
  border-bottom: none;
}

.default-tag-list-item:hover {
  background-color: var(--bg-tertiary);
}

.default-tag-name {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
}

.default-tag-color {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 1px solid var(--border-color);
  background-color: var(--primary-color);
}

/* Classi per i colori dei cerchi dei tag nella lista di gestione */
.tag-dot-red {
  background-color: #ef4444 !important;
}

.tag-dot-orange {
  background-color: #f97316 !important;
}

.tag-dot-yellow {
  background-color: #eab308 !important;
}

.tag-dot-green {
  background-color: #22c55e !important;
}

.tag-dot-blue {
  background-color: #3b82f6 !important;
}

.tag-dot-purple {
  background-color: #a855f7 !important;
}

.tag-dot-pink {
  background-color: #ec4899 !important;
}

.tag-dot-gray {
  background-color: #6b7280 !important;
}

.tag-dot-black {
  background-color: #1f2937 !important;
}

.default-tag-actions {
  display: flex;
  gap: 0.5rem;
}

.tag-action-btn {
  background: none;
  border: none;
  cursor: pointer;
  color: var(--text-secondary);
  padding: 0.25rem;
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
}

.tag-action-btn:hover {
  color: var(--primary-color);
  background-color: var(--primary-light);
}

.tag-action-btn.delete:hover {
  color: #ef4444;
  background-color: rgba(239, 68, 68, 0.1);
}

.tag-action-btn svg {
  width: 18px;
  height: 18px;
}

.color-picker {
  height: 40px;
  padding: 0.25rem;
  border-radius: var(--radius-md);
  cursor: pointer;
}

/* Playlist Generator Styles */
.generator-playlists {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-top: 0.5rem;
  max-height: 300px;
  overflow-y: auto;
  padding: 0.5rem;
  background-color: var(--bg-secondary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-color);
}

.generator-playlist-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 1rem;
  background-color: var(--bg-primary);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-color);
  transition: all var(--transition-normal);
}

.generator-playlist-item:hover {
  background-color: var(--primary-light);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.generator-playlist-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex: 1;
}

.generator-playlist-name {
  font-weight: 500;
  color: var(--text-primary);
}

.generator-playlist-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.generator-playlist-toggle {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 22px;
}

.generator-playlist-toggle input {
  opacity: 0;
  width: 0;
  height: 0;
}

.generator-playlist-toggle .slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--bg-tertiary);
  transition: 0.4s;
  border-radius: 34px;
  border: 1px solid var(--border-color);
}

.generator-playlist-toggle .slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 3px;
  bottom: 2px;
  background-color: white;
  transition: 0.4s;
  border-radius: 50%;
}

.generator-playlist-toggle input:checked + .slider {
  background-color: var(--primary-color);
}

.generator-playlist-toggle input:focus + .slider {
  box-shadow: 0 0 1px var(--primary-color);
}

.generator-playlist-toggle input:checked + .slider:before {
  transform: translateX(18px);
}

.generator-playlist-count {
  display: flex;
  align-items: center;
}

/* Tag Filter Bar */
.tag-filter-bar {
  width: 100%;
  padding: 0.75rem 1rem;
  background-color: rgba(0, 0, 0, 0.15);
  border-radius: var(--radius-lg);
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1);
  z-index: 1;
  margin-top: 0.75rem;
  border: 1px solid rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(5px);
}

.tag-filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
  font-size: 0.9rem;
  color: white;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding-bottom: 0.5rem;
}

.tag-clear-btn {
  background-color: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all var(--transition-normal);
}

.tag-clear-btn:hover {
  background-color: rgba(255, 255, 255, 0.25);
  transform: translateY(-1px);
}

.tag-clear-btn .icon {
  width: 14px;
  height: 14px;
  opacity: 0.9;
}

.tag-filter-title {
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  letter-spacing: 0.02em;
  display: flex;
  align-items: center;
}

.tag-filter-title::before {
  content: "";
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-right: 0.5rem;
  background-color: white;
  mask-image: url("../assets/tag-icon.svg");
  -webkit-mask-image: url("../assets/tag-icon.svg");
  mask-size: contain;
  -webkit-mask-size: contain;
  mask-position: center;
  -webkit-mask-position: center;
  mask-repeat: no-repeat;
  -webkit-mask-repeat: no-repeat;
}

.tag-filters-container {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  max-height: 8rem;
  overflow-y: auto;
  padding: 0.25rem 0;
  width: 100%;
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
}

.tag-filter {
  font-size: 0.75rem;
  color: rgba(0, 0, 0, 0.8);
  background-color: rgba(255, 255, 255, 0.85);
  border: 1px solid rgba(255, 255, 255, 0.5);
  padding: 0.3rem 0.6rem;
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: all var(--transition-normal);
  user-select: none;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  font-weight: 600;
}

.tag-filter:hover {
  background-color: white;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
  color: var(--primary-color);
}

.tag-filter.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.tag-filter .tag-count {
  margin-left: 0.3rem;
  font-size: 0.7rem;
  opacity: 0.8;
  font-weight: 600;
  display: inline-block;
  background-color: rgba(0, 0, 0, 0.1);
  padding: 0.1rem 0.3rem;
  border-radius: 10px;
  min-width: 18px;
  text-align: center;
}

.generator-playlist-count input {
  width: 50px;
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-md);
  border: 1px solid var(--border-color);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  text-align: center;
  font-size: 0.9rem;
}

.generator-playlist-count input:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Tag-based playlist generator styles */
.tag-generator-available-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  max-height: 150px;
  overflow-y: auto;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background-color: var(--bg-primary);
  margin-bottom: 1rem;
}

.tag-generator-schema {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  max-height: 300px;
  overflow-y: auto;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background-color: var(--bg-primary);
  margin-bottom: 0.5rem;
}

.tag-schema-entry {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  padding: 1rem;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background-color: var(--bg-secondary);
  position: relative;
}

.tag-schema-entry-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.tag-schema-entry-title {
  font-weight: 600;
  font-size: 0.9rem;
  color: var(--text-primary);
}

.tag-schema-remove-btn {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: var(--radius-sm);
  transition: all var(--transition-normal);
  display: flex;
  align-items: center;
  justify-content: center;
}

.tag-schema-remove-btn:hover {
  color: #ef4444;
  background-color: rgba(239, 68, 68, 0.1);
}

.tag-schema-primary-tag {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.tag-schema-primary-tag-label {
  font-size: 0.8rem;
  font-weight: 600;
  color: var(--text-secondary);
}

.tag-schema-primary-tag-select {
  padding: 0.5rem;
  border-radius: var(--radius-md);
  border: 1px solid var(--border-color);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-size: 0.9rem;
}

.tag-schema-secondary-tags {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.tag-schema-secondary-tags-label {
  font-size: 0.8rem;
  font-weight: 600;
  color: var(--text-secondary);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tag-schema-secondary-tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  padding: 0.5rem;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background-color: var(--bg-primary);
  min-height: 42px;
}

.tag-schema-track-count {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.tag-schema-track-count-label {
  font-size: 0.8rem;
  font-weight: 600;
  color: var(--text-secondary);
}

.tag-schema-track-count-input {
  width: 60px;
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-md);
  border: 1px solid var(--border-color);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  text-align: center;
  font-size: 0.9rem;
}

.tag-option {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-sm);
  background-color: var(--primary-light);
  color: var(--primary-color);
  font-size: 0.75rem;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-normal);
  user-select: none;
}

.tag-option:hover {
  background-color: var(--primary-focus);
  transform: translateY(-1px);
}

.tag-option.selected {
  background-color: var(--primary-color);
  color: white;
}

.tag-option.primary-selected {
  background-color: #fbbf24;
  color: #7c2d12;
}

.tag-option.secondary-selected {
  background-color: #a3e635;
  color: #365314;
}

.mt-2 {
  margin-top: 0.5rem;
}

.generator-schema {
  margin-top: 0.5rem;
  padding: 1rem;
  background-color: var(--bg-secondary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-color);
}

.generator-schema-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  padding: 0.5rem;
  background-color: var(--bg-primary);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-color);
}

.generator-schema-count {
  font-weight: 600;
  color: var(--primary-color);
  background-color: var(--primary-light);
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-sm);
}

.generator-schema-name {
  font-weight: 500;
  color: var(--text-primary);
}

/* Track Subset Styles */
.track-subset {
  margin-bottom: 16px;
  border-radius: 6px;
  overflow: hidden;
  border: 1px solid var(--border-color);
  background-color: var(--bg-primary);
}

.track-subset:last-child {
  margin-bottom: 0;
}

.track-subset-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background-color: var(--bg-tertiary);
  border-bottom: 1px solid var(--border-color);
  cursor: grab;
}

.track-subset-header:active {
  cursor: grabbing;
}

.track-subset-header.drag-over {
  border: 2px dashed var(--primary-color);
  background-color: var(--primary-light);
}

/* Cycle controls for up/down buttons */
.cycle-controls {
  display: flex;
  align-items: center;
  gap: 4px;
}

.cycle-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border-radius: var(--radius-md);
  background-color: var(--bg-primary);
  border: 1px solid var(--border-color);
  color: var(--text-primary);
  cursor: pointer;
  transition: all var(--transition-normal);
  padding: 0;
}

.cycle-button:hover {
  background-color: var(--primary-light);
  border-color: var(--primary-color);
  color: var(--primary-color);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.cycle-button:active {
  transform: translateY(0);
}

.cycle-button .icon {
  width: 16px;
  height: 16px;
  transition: transform var(--transition-fast);
}

.cycle-button:hover .icon {
  transform: scale(1.1);
}

.track-subset-title {
  font-weight: bold;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 8px;
}

.track-subset-title .cycle-number {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: var(--primary-color);
  color: white;
  font-size: 0.9em;
}

.track-subset-tracks {
  background-color: var(--bg-primary);
}

.track-subset .playlist-track-item {
  border-bottom: 1px solid var(--border-color);
}

.track-subset .playlist-track-item:last-child {
  border-bottom: none;
}

.track-subset.drag-over {
  border: 2px dashed var(--primary-color);
  background-color: var(--primary-light);
}

/* Stili per le informazioni aggiuntive delle tracce nelle playlist */
.playlist-track-source,
.playlist-track-tags,
.playlist-track-path {
  font-size: 0.75rem;
  color: var(--text-secondary);
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
  margin-top: 0.25rem;
}

.source-label,
.tags-label,
.path-label {
  font-weight: 600;
  color: var(--text-tertiary);
  min-width: 40px;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
  max-width: calc(100% - 50px);
}

.playlist-track-tag {
  background-color: var(--primary-light);
  color: var(--primary-color);
  padding: 0.1rem 0.4rem;
  border-radius: var(--radius-sm);
  font-size: 0.7rem;
  font-weight: 600;
  white-space: nowrap;
}

.path-value {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: calc(100% - 50px);
  font-family: monospace;
  font-size: 0.7rem;
  background-color: var(--bg-tertiary);
  padding: 0.1rem 0.4rem;
  border-radius: var(--radius-sm);
}

/* Track replacement popup styles */
.replacement-options {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.replacement-option {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background-color: var(--bg-primary);
}

.option-header {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.option-header h5 {
  margin: 0;
  font-size: 1rem;
  color: var(--primary-color);
}

.option-header p {
  margin: 0;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

/* Manual track selection styles */
.manual-track-selection {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 0.5rem;
}

.manual-search-container {
  position: relative;
}

.manual-search-input {
  width: 100%;
  padding: 0.6rem 2.5rem 0.6rem 2.5rem;
  border-radius: var(--radius-lg);
  border: 2px solid var(--border-color);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-size: 0.9rem;
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-sm);
}

.manual-search-input:hover {
  border-color: var(--border-hover);
}

.manual-search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px var(--primary-focus);
}

.manual-search-input::placeholder {
  color: var(--text-tertiary);
}

.manual-search-include-path-label {
  position: absolute;
  right: 0.5rem;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: var(--radius-md);
  transition: all var(--transition-normal);
}

.manual-search-include-path-label:hover {
  background-color: var(--bg-tertiary);
}

.manual-search-include-path-label .checkbox-input {
  display: none;
}

.manual-search-include-path-label .path-search-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  color: var(--text-secondary);
  transition: color var(--transition-normal);
}

.manual-search-include-path-label .checkbox-input:checked + .path-search-wrapper {
  color: var(--primary-color);
}

.path-search-icon {
  width: 18px;
  height: 18px;
}

.manual-search-results {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  background-color: var(--bg-primary);
}

.manual-search-results .empty-state {
  padding: 2rem 1rem;
  text-align: center;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.manual-track-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--border-color);
  cursor: pointer;
  transition: all var(--transition-normal);
}

.manual-track-item:last-child {
  border-bottom: none;
}

.manual-track-item:hover {
  background-color: var(--bg-secondary);
}

.manual-track-item.selected {
  background-color: var(--primary-light);
  border-color: var(--primary-color);
}

.manual-track-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.manual-track-title {
  font-weight: 500;
  color: var(--text-primary);
  font-size: 0.9rem;
}

.manual-track-details {
  display: flex;
  gap: 0.5rem;
  font-size: 0.8rem;
  color: var(--text-secondary);
}

.manual-track-artist {
  font-weight: 400;
}

.manual-track-duration {
  font-weight: 400;
}

.manual-track-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
  margin-top: 0.25rem;
}

.manual-track-tag {
  background-color: var(--bg-tertiary);
  color: var(--text-secondary);
  padding: 0.125rem 0.375rem;
  border-radius: var(--radius-sm);
  font-size: 0.7rem;
  font-weight: 500;
}

.selected-track-info {
  padding: 0.75rem;
  background-color: var(--bg-secondary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-color);
}

.selected-track-info h6 {
  margin: 0 0 0.5rem 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
}

.selected-track-details {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.selected-track-name {
  font-weight: 500;
  color: var(--text-primary);
  font-size: 0.9rem;
}

.selected-track-meta {
  font-size: 0.8rem;
  color: var(--text-secondary);
}
  color: var(--text-secondary);
}

.custom-tag-selection {
  margin-top: 0.5rem;
  padding: 0.5rem;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  background-color: var(--bg-secondary);
  max-height: 150px;
  overflow-y: auto;
}

.available-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.replacement-tag {
  font-size: 0.75rem;
  color: rgba(0, 0, 0, 0.8);
  background-color: rgba(255, 255, 255, 0.85);
  border: 1px solid rgba(255, 255, 255, 0.5);
  padding: 0.3rem 0.6rem;
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: all var(--transition-normal);
  user-select: none;
}

.replacement-tag:hover {
  background-color: white;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
}

.replacement-tag.selected {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.mt-3 {
  margin-top: 1rem;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 1rem;
  color: var(--text-secondary);
  font-style: italic;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.spin {
  animation: spin 1.5s linear infinite;
}
