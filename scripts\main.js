const { app, BrowserWindow, ipcMain, nativeTheme, dialog, shell } = require('electron/main')
const path = require('node:path')
const fs = require('fs/promises')
const fsSync = require('fs')
const crypto = require('crypto')
const sqlite3 = require('sqlite3').verbose()
const musicMetadata = require('music-metadata')
const NodeID3 = require('node-id3')

const settingsManager = require('./settings-manager');
const fileOps = require('./file-operations');
const dbOps = require('./database-operations');
const ipcHandlerModule = require('./ipc-handlers'); // Added

let mainWindow
let selectedDirectory = null
let appSettings; // This will store the loaded settings object

// ---- Core Application Functions (to be passed via appContext) ----
function createWindow() {
    console.log('Creating main window');
    mainWindow = new BrowserWindow({
        width: 1200,
        height: 800,
        webPreferences: {
            preload: path.join(__dirname, 'preload.js'),
            nodeIntegration: false,
            contextIsolation: true,
        }
    });

    mainWindow.loadFile('index.html');

    // Open DevTools only if not in production
    if (process.env.NODE_ENV !== 'production') {
        mainWindow.webContents.openDevTools();
    }

    mainWindow.on('closed', () => {
        mainWindow = null;
    });
}

async function scanDirectory(directory, isRescan = false) {
    if (!mainWindow) {
        console.log('Main window not found, creating one for scanDirectory.');
        createWindow();
    }
    if (mainWindow && mainWindow.webContents) {
        if (isRescan) {
            mainWindow.webContents.send('library:scan-started', { directory, isRescan: true });
        } else {
            mainWindow.webContents.send('library:scan-started', { directory });
        }
    }
    console.log(`Scanning directory: ${directory}, isRescan: ${isRescan}`);
    const db = dbOps.getDB();
    const allFiles = [];
    const stats = { total: 0, new: 0, updated: 0, skipped: 0, errors: 0, unchanged: 0 };
    const failedFiles = []; // Array to store detailed information about failed imports

    // Function to categorize errors for better user understanding
    function categorizeError(error) {
        const errorMessage = error.message.toLowerCase();

        if (errorMessage.includes('enoent') || errorMessage.includes('no such file')) {
            return 'File not found or access denied';
        } else if (errorMessage.includes('eacces') || errorMessage.includes('permission denied')) {
            return 'Permission denied';
        } else if (errorMessage.includes('metadata') || errorMessage.includes('parse')) {
            return 'Metadata extraction failed';
        } else if (errorMessage.includes('unsupported') || errorMessage.includes('format')) {
            return 'Unsupported file format';
        } else if (errorMessage.includes('network') || errorMessage.includes('unc')) {
            return 'Network path access error';
        } else if (errorMessage.includes('database') || errorMessage.includes('sqlite')) {
            return 'Database error';
        } else if (errorMessage.includes('timeout')) {
            return 'Operation timeout';
        } else {
            return 'Unknown error';
        }
    }

    async function processFile(filePath) {
        const fileName = path.basename(filePath);
        const fileExt = path.extname(filePath).toLowerCase();
        const supportedExtensions = ['.mp3', '.m4a', '.aac', '.flac', '.wav', '.ogg'];

        if (!supportedExtensions.includes(fileExt)) {
            stats.skipped++;
            return;
        }

        try {
            let metadata = {};
            let duration = null;

            // Check if this is a network path (UNC path)
            const isNetworkPath = filePath.startsWith('\\\\');

            try {
                if (isNetworkPath) {
                    console.log(`Processing network path: ${filePath}`);
                    try {
                        // Try with normalized path first
                        const normalizedPath = filePath.replace(/\\\\/g, '\\');
                        console.log(`Trying normalized path for metadata: ${normalizedPath}`);
                        const meta = await musicMetadata.parseFile(normalizedPath, { duration: true, skipCovers: true });
                        metadata = meta.common;
                        duration = meta.format.duration;
                        console.log(`Successfully extracted metadata from normalized network path`);
                    } catch (normalizeErr) {
                        console.log(`Normalized path failed for metadata, trying original path`);
                        // If normalized path fails, try original path
                        const meta = await musicMetadata.parseFile(filePath, { duration: true, skipCovers: true });
                        metadata = meta.common;
                        duration = meta.format.duration;
                        console.log(`Successfully extracted metadata from original network path`);
                    }
                } else {
                    // For local files, use the standard approach
                    const meta = await musicMetadata.parseFile(filePath, { duration: true, skipCovers: true });
                    metadata = meta.common;
                    duration = meta.format.duration;
                }
            } catch (err) {
                console.warn(`Could not parse metadata for ${filePath}: ${err.message}`);
                // Use filename as title if metadata parsing fails
                metadata.title = fileName;

                // For network paths, try NodeID3 as a fallback for MP3 files
                if (isNetworkPath && fileExt.toLowerCase() === '.mp3') {
                    try {
                        console.log(`Trying NodeID3 for MP3 network file: ${filePath}`);
                        const tags = NodeID3.read(filePath);
                        if (tags) {
                            if (tags.title) metadata.title = tags.title;
                            if (tags.artist) metadata.artist = tags.artist;
                            if (tags.TLEN) {
                                const lengthMs = parseInt(tags.TLEN);
                                if (!isNaN(lengthMs) && lengthMs > 0) {
                                    duration = lengthMs / 1000;
                                    console.log(`Got duration from ID3 TLEN tag: ${duration} seconds`);
                                }
                            }
                        }
                    } catch (id3Error) {
                        console.error(`Error reading ID3 tags for network path: ${id3Error.message}`);
                    }
                }
            }

            const uniqueId = await fileOps.generateUniqueTrackId(filePath, duration);
            // First check if track already exists by path (most reliable)
            // Also check for normalized path variations to handle path separator differences
            const normalizedPath = filePath.replace(/\\\\/g, '\\').replace(/\//g, '\\');
            const existingTrack = await new Promise((resolve, reject) => {
                db.get('SELECT id, path, title, artist, unique_id, tags, updatedAt FROM tracks WHERE path = ? OR path = ?',
                    [filePath, normalizedPath], (err, row) => {
                        if (err) reject(err);
                        else resolve(row);
                    });
            });

            const fileTags = fileOps.extractHashtagsFromFilename(fileName, filePath);

            if (existingTrack) {
                let updated = false;
                const updateFields = {};
                if (existingTrack.path !== filePath) {
                    updateFields.path = filePath;
                    console.log(`Path updated for ${existingTrack.name} to ${filePath}`);
                    updated = true;
                }
                if (!existingTrack.unique_id && uniqueId) {
                    updateFields.unique_id = uniqueId;
                    updated = true;
                }
                if (duration && existingTrack.duration !== duration) { // Assuming duration column exists
                    updateFields.duration = duration;
                    updated = true;
                }
                // Merge tags intelligently
                const mergedTags = fileOps.mergeTagsWithExisting(existingTrack.tags, fileTags);
                if (mergedTags !== existingTrack.tags) {
                    updateFields.tags = mergedTags;
                    updated = true;
                }
                // Update title/artist only if new metadata is substantially different or old one is placeholder
                if (metadata.title && metadata.title !== fileName && metadata.title !== existingTrack.title) {
                    updateFields.title = metadata.title;
                    updated = true;
                }
                if (metadata.artist && metadata.artist !== existingTrack.artist) {
                    updateFields.artist = metadata.artist;
                    updated = true;
                }


                if (updated) {
                    // Use the file's modification time for updatedAt instead of current time
                    try {
                        let fileStats;
                        if (isNetworkPath) {
                            try {
                                // Try with normalized path first
                                const normalizedPath = filePath.replace(/\\\\/g, '\\');
                                fileStats = await fs.stat(normalizedPath);
                            } catch (normalizeErr) {
                                // If normalized path fails, try original path
                                fileStats = await fs.stat(filePath);
                            }
                        } else {
                            fileStats = await fs.stat(filePath);
                        }
                        updateFields.updatedAt = fileStats.mtime.toISOString();
                    } catch (statErr) {
                        console.warn(`Could not get file stats for ${filePath}: ${statErr.message}`);
                        // Use current time as fallback
                        updateFields.updatedAt = new Date().toISOString();
                    }
                    const fieldNames = Object.keys(updateFields);
                    const valuePlaceholders = fieldNames.map(() => '?').join(', ');
                    const setClauses = fieldNames.map(name => `${name} = ?`).join(', ');
                    const values = fieldNames.map(name => updateFields[name]);

                    await new Promise((resolve, reject) => {
                        db.run(`UPDATE tracks SET ${setClauses} WHERE id = ?`, [...values, existingTrack.id], function (err) {
                            if (err) reject(err);
                            else resolve();
                        });
                    });
                    stats.updated++;
                    console.log(`Track updated in DB: ${filePath}`);
                } else {
                    stats.unchanged++;
                }
            } else {
                // Double-check for duplicates using unique_id as a fallback
                const duplicateCheck = await new Promise((resolve, reject) => {
                    db.get('SELECT id FROM tracks WHERE unique_id = ?', [uniqueId], (err, row) => {
                        if (err) reject(err);
                        else resolve(row);
                    });
                });

                if (duplicateCheck) {
                    console.log(`Duplicate track detected by unique_id for ${filePath}, skipping insertion`);
                    stats.unchanged++;
                    if (mainWindow && mainWindow.webContents) {
                        mainWindow.webContents.send('library:scan-progress', { processed: allFiles.length, total: stats.total, currentFile: fileName });
                    }
                    return; // Skip insertion
                }

                // Get file stats for creation and modification times
                let createdAt, updatedAt;
                try {
                    let fileStats;
                    if (isNetworkPath) {
                        try {
                            // Try with normalized path first
                            const normalizedPath = filePath.replace(/\\\\/g, '\\');
                            fileStats = await fs.stat(normalizedPath);
                        } catch (normalizeErr) {
                            // If normalized path fails, try original path
                            fileStats = await fs.stat(filePath);
                        }
                    } else {
                        fileStats = await fs.stat(filePath);
                    }
                    createdAt = fileStats.birthtime.toISOString();
                    updatedAt = fileStats.mtime.toISOString();
                } catch (statErr) {
                    console.warn(`Could not get file stats for ${filePath}: ${statErr.message}`);
                    // Use current time as fallback
                    const now = new Date().toISOString();
                    createdAt = now;
                    updatedAt = now;
                }

                await new Promise((resolve, reject) => {
                    db.run('INSERT INTO tracks (path, name, extension, title, artist, unique_id, duration, tags, createdAt, updatedAt) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
                        [filePath, fileName, fileExt, metadata.title || fileName, metadata.artist, uniqueId, duration, fileTags, createdAt, updatedAt], function (err) {
                            if (err) reject(err);
                            else resolve();
                        });
                });
                stats.new++;
                console.log(`Track added to DB: ${filePath}`);
            }
            if (mainWindow && mainWindow.webContents) {
                mainWindow.webContents.send('library:scan-progress', { processed: allFiles.length, total: stats.total, currentFile: fileName });
            }
        } catch (err) {
            console.error(`Error processing file ${filePath}: ${err.message}`);
            stats.errors++;

            // Collect detailed error information
            const errorInfo = {
                filePath: filePath,
                fileName: fileName,
                error: err.message,
                errorType: categorizeError(err)
            };
            failedFiles.push(errorInfo);
        }
    }

    async function walk(dir) {
        const entries = await fs.readdir(dir, { withFileTypes: true });
        for (const entry of entries) {
            const fullPath = path.join(dir, entry.name);
            if (entry.isDirectory()) {
                await walk(fullPath);
            } else if (entry.isFile()) {
                stats.total++;
                allFiles.push(fullPath);
            }
        }
    }

    try {
        await walk(directory);
        for (let i = 0; i < allFiles.length; i++) {
            await processFile(allFiles[i]);
        }
    } catch (error) {
        console.error(`Error scanning directory ${directory}:`, error);
        stats.errors++;
    }

    console.log('Scan complete:', stats);
    if (failedFiles.length > 0) {
        console.log('Failed files:', failedFiles);
    }
    if (mainWindow && mainWindow.webContents) {
        mainWindow.webContents.send('library:scan-complete', { stats, directory, failedFiles });
    }
    return { success: true, stats, directory, failedFiles };
}

// Function to clean up duplicate tracks in the database
async function cleanupDuplicateTracks() {
    const db = dbOps.getDB();
    console.log('Starting duplicate track cleanup...');

    try {
        // Find duplicates by path
        const pathDuplicates = await new Promise((resolve, reject) => {
            db.all(`
                SELECT path, COUNT(*) as count, GROUP_CONCAT(id) as ids
                FROM tracks
                GROUP BY path
                HAVING COUNT(*) > 1
            `, [], (err, rows) => {
                if (err) reject(err);
                else resolve(rows);
            });
        });

        let removedCount = 0;
        for (const duplicate of pathDuplicates) {
            const ids = duplicate.ids.split(',').map(id => parseInt(id));
            // Keep the first one, remove the rest
            const idsToRemove = ids.slice(1);

            for (const idToRemove of idsToRemove) {
                await new Promise((resolve, reject) => {
                    db.run('DELETE FROM tracks WHERE id = ?', [idToRemove], (err) => {
                        if (err) reject(err);
                        else resolve();
                    });
                });
                removedCount++;
                console.log(`Removed duplicate track with id ${idToRemove} for path: ${duplicate.path}`);
            }
        }

        console.log(`Duplicate cleanup complete. Removed ${removedCount} duplicate tracks.`);
        return { success: true, removedCount };
    } catch (error) {
        console.error('Error during duplicate cleanup:', error);
        return { success: false, error: error.message };
    }
}

// Other large functions (findNewTracksInDirectory, updateTrackPathsIfMoved, getLibraryStats) would go here
// For brevity, they are omitted but assumed to be here for appContext if needed by other handlers.

// ---- End Core Application Functions ----

app.whenReady().then(async () => {
    try {
        const userDataPath = app.getPath('userData');
        console.log('Application user data path:', userDataPath);

        appSettings = await settingsManager.loadSettings();
        if (appSettings && appSettings.lastSelectedDirectory) {
            selectedDirectory = appSettings.lastSelectedDirectory;
            console.log('Restored last selected directory from settings:', selectedDirectory);
        }

        await dbOps.setupDatabase();
        console.log('Database setup process initiated via dbOps.');
        dbOps.initializeDbIpcHandlers(); // Initialize DB-specific IPC handlers

        createWindow(); // Initial window creation

        if (mainWindow && mainWindow.webContents) {
            mainWindow.webContents.send('theme:set', nativeTheme.shouldUseDarkColors ? 'dark' : 'light');
        }

        // ---- Initialize IPC Handlers ----
        const appContext = {
            getMainWindow: () => mainWindow,
            getSelectedDirectory: () => selectedDirectory,
            setSelectedDirectory: (dir) => {
                selectedDirectory = dir;
                if (appSettings) {
                    appSettings.lastSelectedDirectory = selectedDirectory;
                    // Use settingsManager to save
                    settingsManager.saveSettings(appSettings).catch(err => {
                        console.error('Error saving selected directory via appContext:', err);
                    });
                } else {
                    console.warn('appSettings not available in appContext.setSelectedDirectory');
                }
            },
            getAppSettings: () => appSettings,
            scanDirectory,
            cleanupDuplicateTracks,
            createWindow, // Provide createWindow to context
            getAppPath: () => app.getAppPath(),
            getPreloadPath: () => path.join(__dirname, 'preload.js'), // Assuming preload.js is in the same dir as main.js
        };

        const electronModules = { dialog, shell, nativeTheme, BrowserWindow, app, fs, fsSync, path, NodeID3 }; // Corrected: fs is already fs/promises
        const localModules = { dbOps, fileOps, settingsManager };

        ipcHandlerModule.initialize(appContext, electronModules, localModules);
        console.log('Custom IPC handlers initialized.');
        // ---- End Initialize IPC Handlers ----

    } catch (error) {
        console.error('Failed during app.whenReady:', error);
        dialog.showErrorBox(
            'Application Error',
            `Failed to initialize the application. Please check logs. Error: ${error.message}`
        );
        app.quit();
    }
});

app.on('window-all-closed', () => {
    const dbInstance = dbOps.getDB();
    if (dbInstance) {
        dbInstance.close((err) => {
            if (err) {
                console.error('Error closing database', err.message);
            }
        });
    }
    if (process.platform !== 'darwin') {
        app.quit()
    }
})

// ---- REMOVED IPC HANDLERS (MOVED TO ipc-handlers.js) ----
// ipcMain.handle('dark-mode:onUpdate', ...);
// ipcMain.handle('database:open', ...);
// ipcMain.handle('playlist:export-m3u8', ...);
// ipcMain.handle('track:edit-dialog', ...);
// ipcMain.handle('track:update-tags', ...);
// ipcMain.handle('library:select-folder', ...);
// ipcMain.handle('library:rescan-selected-folder', ...);
// ---- End REMOVED IPC HANDLERS ----


// Remaining IPC Handlers (to be moved or refactored later)
// Example: Playlist CRUD, Default Tags, Playlist Generator handlers are still here
// Handler to get all playlists containing a specific track
// ipcMain.handle('track:getPlaylists', ...) // This and other DB handlers will be moved

// ---- Functions for library management (some parts might be refactored to dbOps or fileOps) ----
// ... existing code ...