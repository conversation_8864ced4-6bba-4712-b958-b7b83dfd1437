const path = require('node:path');
const fsPromises = require('fs/promises'); // Renamed to avoid conflict if fs is also used
const fsSync = require('fs'); // For synchronous operations if needed by moved functions
const crypto = require('crypto');
const musicMetadata = require('music-metadata');
const NodeID3 = require('node-id3');
const { app } = require('electron/main'); // Needed for app.getPath('temp') in generateUniqueTrackId

// Function to extract hashtags from a filename and its path
function extractHashtagsFromFilename(filename, fullPath = null) {
    const hashtagRegex = /#([a-zA-Z0-9_]+)/g;
    let allTags = new Set();

    // Extract hashtags from filename
    const filenameMatches = filename.match(hashtagRegex);
    if (filenameMatches) {
        filenameMatches.forEach(tag => allTags.add(tag.substring(1).toLowerCase())); // Convert to lowercase
    }

    // If fullPath is provided, extract hashtags from directory names
    if (fullPath) {
        // Get directory path without the filename
        const dirPath = path.dirname(fullPath);
        // Split path into components
        const pathComponents = dirPath.split(path.sep);

        // Extract hashtags from each directory name
        pathComponents.forEach(component => {
            const dirMatches = component.match(hashtagRegex);
            if (dirMatches) {
                dirMatches.forEach(tag => allTags.add(tag.substring(1).toLowerCase())); // Convert to lowercase
            }
        });
    }

    const tags = Array.from(allTags);
    if (tags.length === 0) return null;

    console.log(`Extracted hashtags from path: ${tags.join(', ')}`);
    return tags.join(', ');
}

// Function to merge new tags with existing tags
function mergeTagsWithExisting(existingTags, newTags) {
    if (!newTags) return existingTags;
    if (!existingTags) return newTags;

    // Split existing tags by comma and trim whitespace
    const existingTagsArray = existingTags.split(',').map(tag => tag.trim());
    // Split new tags by comma and trim whitespace
    const newTagsArray = newTags.split(',').map(tag => tag.trim());

    // Create a case-insensitive Set to track which tags we've already added
    const tagSet = new Set();
    const uniqueTags = [];

    // Process all tags (existing and new)
    [...existingTagsArray, ...newTagsArray].forEach(tag => {
        if (!tag) return; // Skip empty tags

        // Convert to lowercase for comparison
        const lowerTag = tag.toLowerCase();

        // If we haven't seen this tag yet (case-insensitive check)
        if (!tagSet.has(lowerTag)) {
            tagSet.add(lowerTag);
            uniqueTags.push(tag); // Keep the original case for display
        }
    });

    // Join with comma + space
    const mergedTags = uniqueTags.join(', ');

    console.log(`Merged tags: ${mergedTags}`);
    return mergedTags;
}

// Funzione per generare un ID univoco per una traccia audio
// Utilizza solo proprietà intrinseche del file che non cambiano quando il file viene rinominato
async function generateUniqueTrackId(filePath, duration = null) {
    try {
        // Sanitize file path for logging to avoid console errors with special characters
        const sanitizedPath = filePath.replace(/[^\\x20-\\x7E]/g, '?');

        // Check if this is a network path (UNC path)
        const isNetworkPath = filePath.startsWith('\\\\');
        if (isNetworkPath) {
            console.log(`Network path detected for ${sanitizedPath}, using special handling`);
        }

        // Ottieni le statistiche del file
        let stats;
        try {
            // For network paths, try a different approach to get file stats
            if (isNetworkPath) {
                try {
                    // Try to normalize the path first
                    const normalizedPath = filePath.replace(/\\\\/g, '\\');
                    console.log(`Trying normalized path for stats: ${normalizedPath}`);
                    stats = await fsPromises.stat(normalizedPath);
                } catch (normalizeError) {
                    console.log(`Normalized path failed, trying original path: ${filePath}`);
                    stats = await fsPromises.stat(filePath);
                }
            } else {
                stats = await fsPromises.stat(filePath);
            }
        } catch (statError) {
            console.error(`Error generating unique ID for ${sanitizedPath}:`, statError);

            const cryptoHash = crypto.createHash('sha256');
            const fileName = path.basename(filePath);
            cryptoHash.update(fileName);
            cryptoHash.update(filePath.length.toString());
            if (duration) cryptoHash.update(duration.toString());

            console.log(`Using fallback hash method for ${sanitizedPath} due to stat error`);
            return cryptoHash.digest('hex');
        }

        const sizeInKB = Math.round(stats.size / 1024);
        let trackDuration = duration;

        if (!trackDuration) {
            // Try to extract metadata for both local and network paths
            try {
                // For network paths, try with a normalized path first
                if (isNetworkPath) {
                    console.log(`Trying to extract metadata for network path: ${sanitizedPath}`);
                    try {
                        // Try with normalized path
                        const normalizedPath = filePath.replace(/\\\\/g, '\\');
                        console.log(`Trying normalized path for metadata: ${normalizedPath}`);
                        const metadata = await musicMetadata.parseFile(normalizedPath, { duration: true });
                        trackDuration = metadata.format.duration;
                        console.log(`Successfully extracted duration from normalized path: ${trackDuration} seconds`);
                    } catch (normalizeErr) {
                        console.log(`Normalized path failed for metadata, trying original path`);
                        // If normalized path fails, try original path
                        const metadata = await musicMetadata.parseFile(filePath, { duration: true });
                        trackDuration = metadata.format.duration;
                        console.log(`Successfully extracted duration from original path: ${trackDuration} seconds`);
                    }
                } else {
                    // For local files, use the standard approach
                    const metadata = await musicMetadata.parseFile(filePath, { duration: true });
                    trackDuration = metadata.format.duration;
                }
            } catch (err) {
                console.log(`Couldn't extract duration for ${sanitizedPath}:`, err.message);

                // For MP3 files, try NodeID3 as a fallback
                const ext = path.extname(filePath).toLowerCase();
                if (ext === '.mp3') {
                    try {
                        console.log(`Trying NodeID3 for MP3 file: ${sanitizedPath}`);
                        const tags = NodeID3.read(filePath);
                        if (tags && tags.TLEN) {
                            const lengthMs = parseInt(tags.TLEN);
                            if (!isNaN(lengthMs) && lengthMs > 0) {
                                trackDuration = lengthMs / 1000;
                                console.log(`Got duration from ID3 TLEN tag: ${trackDuration} seconds`);
                            }
                        }
                    } catch (id3Error) {
                        console.error(`Error reading ID3 tags for ${sanitizedPath}:`, id3Error.message);
                    }
                }

                // If still no duration, estimate based on file size
                if (!trackDuration) {
                    try {
                        const estimatedDuration = Math.round((stats.size / 1024 / 1024) * 60);
                        trackDuration = estimatedDuration > 0 ? estimatedDuration : 180;
                        console.log(`Estimated duration for ${sanitizedPath} based on file size: ${trackDuration} seconds`);
                    } catch (estimateErr) {
                        console.error(`Error estimating duration for ${sanitizedPath}:`, estimateErr.message);
                        trackDuration = 180;
                    }
                }
            }

            if (!trackDuration && app) { // Check if app is available (it should be)
                try {
                    console.log(`Trying to copy file locally to read metadata for network path: ${sanitizedPath}`);
                    const tempDir = path.join(app.getPath('temp'), 'playlist-maker-temp-metadata');
                    if (!fsSync.existsSync(tempDir)) {
                        fsSync.mkdirSync(tempDir, { recursive: true });
                    }
                    const ext = path.extname(filePath).toLowerCase();
                    const tempFilename = `temp-meta-${Date.now()}${ext}`;
                    const tempFilePath = path.join(tempDir, tempFilename);
                    console.log(`Copying ${filePath} to ${tempFilePath}`);
                    fsSync.copyFileSync(filePath, tempFilePath);
                    try {
                        const localMetadata = await musicMetadata.parseFile(tempFilePath, { duration: true });
                        if (localMetadata && localMetadata.format && localMetadata.format.duration) {
                            trackDuration = localMetadata.format.duration;
                            console.log(`Successfully read duration from local copy: ${trackDuration} seconds`);
                        }
                    } catch (localMetadataError) {
                        console.error(`Error reading metadata from local copy ${tempFilePath}:`, localMetadataError.message);
                    }
                    try {
                        fsSync.unlinkSync(tempFilePath);
                        console.log(`Deleted temporary metadata file: ${tempFilePath}`);
                    } catch (cleanupError) {
                        console.error(`Error cleaning up temp metadata file ${tempFilePath}:`, cleanupError.message);
                    }
                } catch (copyError) {
                    console.error(`Error copying file to temp location for metadata:`, copyError.message);
                }
            }


            if (!trackDuration) {
                try {
                    const ext = path.extname(filePath).toLowerCase();
                    let bitrate = 128;
                    if (ext === '.mp3') bitrate = 192;
                    else if (ext === '.flac') bitrate = 900;
                    else if (ext === '.wav') bitrate = 1411;
                    else if (ext === '.m4a') bitrate = 256;
                    const estimatedDuration = Math.round(stats.size / (bitrate * 125));
                    if (estimatedDuration > 0 && estimatedDuration < 7200) {
                        trackDuration = estimatedDuration;
                        console.log(`Estimated duration for network path ${sanitizedPath} based on file size and ${bitrate}kbps: ${trackDuration} seconds`);
                    } else {
                        const conservativeEstimate = Math.round((stats.size / 1024 / 1024) * 60);
                        if (conservativeEstimate > 0 && conservativeEstimate < 1800) {
                            trackDuration = conservativeEstimate;
                            console.log(`Using conservative duration estimate for ${sanitizedPath}: ${trackDuration} seconds`);
                        } else {
                            trackDuration = 180; // Use fixed fallback duration instead of random
                            console.log(`Using fixed fallback duration: ${trackDuration} seconds`);
                        }
                    }
                } catch (estimateErr) {
                    console.error(`Error estimating duration for network path ${sanitizedPath}:`, estimateErr.message);
                    trackDuration = 180; // Use fixed fallback duration instead of random
                    console.log(`Using fixed fallback duration: ${trackDuration} seconds`);
                }
            }
        }

        const roundedDuration = trackDuration ? Math.round(trackDuration) : 'unknown';
        let contentHashValue;

        try {
            if (isNetworkPath) {
                // For network paths, try to read a small portion of the file if possible
                try {
                    // Try with normalized path first
                    const normalizedPath = filePath.replace(/\\\\/g, '\\');
                    console.log(`Trying to read content from normalized network path: ${normalizedPath}`);
                    const fileHandle = await fsPromises.open(normalizedPath, 'r');
                    const buffer = Buffer.alloc(65536);
                    await fileHandle.read(buffer, 0, buffer.length, 0);
                    await fileHandle.close();
                    contentHashValue = crypto.createHash('sha256').update(buffer).digest('hex');
                    console.log(`Successfully read content from normalized network path for hash`);
                } catch (normalizeReadError) {
                    console.log(`Failed to read from normalized path, trying original path`);
                    try {
                        // If normalized path fails, try original path
                        const fileHandle = await fsPromises.open(filePath, 'r');
                        const buffer = Buffer.alloc(65536);
                        await fileHandle.read(buffer, 0, buffer.length, 0);
                        await fileHandle.close();
                        contentHashValue = crypto.createHash('sha256').update(buffer).digest('hex');
                        console.log(`Successfully read content from original network path for hash`);
                    } catch (originalReadError) {
                        // If both fail, use the stable hash method
                        console.log(`Failed to read from both paths, using stable hash method`);
                        contentHashValue = crypto.createHash('sha256')
                            .update(`${path.basename(filePath)}|${stats.size}|${filePath}`)
                            .digest('hex');
                        console.log(`Using stable simplified content hash for network path ${sanitizedPath}`);
                    }
                }
            } else {
                // For local files, use the standard approach
                const fileHandle = await fsPromises.open(filePath, 'r');
                const buffer = Buffer.alloc(65536);
                await fileHandle.read(buffer, 0, buffer.length, 0);
                await fileHandle.close();
                contentHashValue = crypto.createHash('sha256').update(buffer).digest('hex');
            }
        } catch (readError) {
            console.error(`Error reading file content for ${sanitizedPath}:`, readError);
            const fileName = path.basename(filePath);
            contentHashValue = crypto.createHash('sha256')
                .update(`${fileName}|${stats.size}|${filePath}`)
                .digest('hex');
        }

        const uniqueString = `${sizeInKB}|${roundedDuration}|${contentHashValue.substring(0, 20)}`;
        console.log(`Generating unique ID from: ${uniqueString}`);
        return crypto.createHash('sha256').update(uniqueString).digest('hex');

    } catch (error) {
        console.error(`Error generating unique ID for ${filePath}:`, error);
        const sanitizedPathForError = filePath.replace(/[^\\x20-\\x7E]/g, '?');
        try {
            const stats = await fsPromises.stat(filePath);
            const fileName = path.basename(filePath);
            const fallbackString = `${fileName}|${stats.size}|${filePath}`;
            console.log(`Using stable size-based fallback for ${sanitizedPathForError}: ${fallbackString}`);
            return crypto.createHash('sha256').update(fallbackString).digest('hex');
        } catch (err) {
            const fileName = path.basename(filePath);
            const lastFallbackString = `${fileName}|${filePath}`;
            console.log(`Using stable last-resort fallback for ${sanitizedPathForError}: ${lastFallbackString}`);
            return crypto.createHash('sha256').update(lastFallbackString).digest('hex');
        }
    }
}

module.exports = {
    extractHashtagsFromFilename,
    mergeTagsWithExisting,
    generateUniqueTrackId
};