// Test file for escapeHtml function
// This can be run in the browser console to verify the function works correctly

function testEscapeHtml() {
    // Test cases for the escapeHtml function
    const testCases = [
        {
            input: 'Normal text',
            expected: 'Normal text',
            description: 'Normal text without special characters'
        },
        {
            input: '<script>alert("XSS")</script>',
            expected: '&lt;script&gt;alert("XSS")&lt;/script&gt;',
            description: 'Script tag should be escaped'
        },
        {
            input: 'Artist & Band',
            expected: 'Artist &amp; Band',
            description: 'Ampersand should be escaped'
        },
        {
            input: 'Song "Title" with quotes',
            expected: 'Song "Title" with quotes',
            description: 'Double quotes should be escaped'
        },
        {
            input: "Song 'Title' with single quotes",
            expected: "Song 'Title' with single quotes",
            description: 'Single quotes should be escaped'
        },
        {
            input: '<div>HTML content</div>',
            expected: '&lt;div&gt;HTML content&lt;/div&gt;',
            description: 'HTML tags should be escaped'
        },
        {
            input: '',
            expected: '',
            description: 'Empty string should remain empty'
        },
        {
            input: null,
            expected: null,
            description: 'Null should be returned as-is'
        },
        {
            input: undefined,
            expected: undefined,
            description: 'Undefined should be returned as-is'
        },
        {
            input: 123,
            expected: 123,
            description: 'Numbers should be returned as-is'
        }
    ];

    console.log('Testing escapeHtml function...');
    
    let passed = 0;
    let failed = 0;

    testCases.forEach((testCase, index) => {
        try {
            const result = escapeHtml(testCase.input);
            
            if (result === testCase.expected) {
                console.log(`✅ Test ${index + 1} PASSED: ${testCase.description}`);
                passed++;
            } else {
                console.log(`❌ Test ${index + 1} FAILED: ${testCase.description}`);
                console.log(`   Input: ${JSON.stringify(testCase.input)}`);
                console.log(`   Expected: ${JSON.stringify(testCase.expected)}`);
                console.log(`   Got: ${JSON.stringify(result)}`);
                failed++;
            }
        } catch (error) {
            console.log(`💥 Test ${index + 1} ERROR: ${testCase.description}`);
            console.log(`   Error: ${error.message}`);
            failed++;
        }
    });

    console.log(`\nTest Results: ${passed} passed, ${failed} failed`);
    
    if (failed === 0) {
        console.log('🎉 All tests passed! The escapeHtml function is working correctly.');
    } else {
        console.log('⚠️ Some tests failed. Please check the implementation.');
    }
}

// Instructions for running the test:
console.log('To test the escapeHtml function:');
console.log('1. Open the application');
console.log('2. Open browser developer tools (F12)');
console.log('3. Go to the Console tab');
console.log('4. Copy and paste this entire file content');
console.log('5. Run: testEscapeHtml()');
