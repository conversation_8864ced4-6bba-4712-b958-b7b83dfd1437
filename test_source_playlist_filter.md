# Test: Source Playlist Filter for Tag-Based Playlist Generation

## Funzionalità Implementata

È stato aggiunto un filtro opzionale per playlist sorgente nel generatore di playlist basato sui tag. Questa funzionalità permette di limitare la ricerca dei brani a quelli appartenenti a una playlist manuale specifica.

## Modifiche Apportate

### 1. HTML (index.html)

- Aggiunto un nuovo campo `<select>` con ID `tag-generator-source-playlist`
- Posizionato tra il campo "Durata Target" e lo "Schema Generazione"
- Include un'opzione predefinita "None - Search all tracks"
- Aggiunto testo di aiuto esplicativo

### 2. JavaScript Frontend (renderer.js)

- Aggiunto riferimento DOM per `tagGeneratorSourcePlaylist`
- Creata funzione `populateTagGeneratorSourcePlaylists()` per popolare il dropdown
- Modificata `openTagPlaylistGeneratorPopup()` per caricare le playlist
- Aggiornata `generateTagPlaylist()` per passare `sourcePlaylistId` al backend

### 3. Backend (database-operations.js)

- Modificato handler `tag-playlist-generator:generate` per accettare `sourcePlaylistId`
- Implementata logica di filtraggio a cascata:
  1. Prima cerca nella playlist sorgente (se specificata)
  2. Poi espande la ricerca a tutto il database se necessario
- Aggiornate tutte le query SQL per supportare il filtro JOIN con playlist_tracks

### 4. CSS (style.css)

- Aggiunto stile `.form-help` per il testo di aiuto

## Logica di Funzionamento

1. **Selezione Playlist Sorgente**: L'utente può selezionare una playlist manuale dal dropdown
2. **Ricerca Filtrata**: Il sistema cerca prima i brani nella playlist sorgente che soddisfano i criteri tag
3. **Fallback Automatico**: Se non trova abbastanza brani, espande automaticamente la ricerca a tutto il database
4. **Trasparenza**: La playlist generata non memorizza alcun riferimento alla playlist sorgente

## Test da Eseguire

1. **Test Base**: Generare una playlist senza filtro sorgente (comportamento originale)
2. **Test con Filtro**: Selezionare una playlist sorgente e verificare che i brani vengano presi prima da quella playlist
3. **Test Fallback**: Usare una playlist sorgente con pochi brani e verificare il fallback al database completo
4. **Test UI**: Verificare che il dropdown si popoli correttamente con le playlist manuali

## Compatibilità

- ✅ Retrocompatibile con la generazione esistente
- ✅ Non modifica lo schema del database
- ✅ Mantiene tutte le funzionalità esistenti
- ✅ Interfaccia coerente con il design esistente

## Correzioni Apportate per il Problema dei Cicli

### Problema Identificato

Il sistema di generazione playlist basata sui tag con filtro playlist sorgente aveva un problema nella logica di popolamento dei cicli:

- I cicli non venivano popolati correttamente secondo lo schema specificato
- Il sistema faceva fallback al database generale troppo presto
- I cache per playlist sorgente e database generale erano mescolati

### Soluzioni Implementate

1. **Cache Separati per Sorgente e Database Generale**

   - `sourcePlaylistCacheKey`: Cache per brani dalla playlist sorgente
   - `generalCacheKey`: Cache per brani dal database generale
   - Evita mescolamento di brani da fonti diverse

2. **Logica di Selezione Migliorata**

   - **Passo 1**: Cerca prima nella playlist sorgente per il numero richiesto di brani
   - **Passo 2**: Solo se necessario, cerca nel database generale per completare
   - **Passo 3**: Evita duplicati controllando sia `usedTrackIds` che `selectedTracks`

3. **Logging Dettagliato**
   - Log separati per ogni fase della selezione
   - Tracciamento del numero di brani trovati in ogni fonte
   - Indicazione chiara quando avviene il fallback

### Codice Chiave Modificato

```javascript
// Step 1: Try to get tracks from source playlist first (if specified)
if (sourcePlaylistId && availableTracksByTag[sourcePlaylistCacheKey]) {
  const availableSourceTracks = availableTracksByTag[
    sourcePlaylistCacheKey
  ].filter((track) => !usedTrackIds.has(track.id));

  const tracksFromSource = Math.min(
    availableSourceTracks.length,
    source.trackCount
  );
  if (tracksFromSource > 0) {
    selectedTracks = availableSourceTracks.slice(0, tracksFromSource);
    tracksAdded = tracksFromSource;
  }
}

// Step 2: If we still need more tracks, get them from the general database
if (tracksAdded < source.trackCount && availableTracksByTag[generalCacheKey]) {
  const remainingCount = source.trackCount - tracksAdded;
  const availableGeneralTracks = availableTracksByTag[generalCacheKey].filter(
    (track) =>
      !usedTrackIds.has(track.id) &&
      !selectedTracks.some((selected) => selected.id === track.id)
  );

  const tracksFromGeneral = Math.min(
    availableGeneralTracks.length,
    remainingCount
  );
  if (tracksFromGeneral > 0) {
    const additionalTracks = availableGeneralTracks.slice(0, tracksFromGeneral);
    selectedTracks = [...selectedTracks, ...additionalTracks];
    tracksAdded += tracksFromGeneral;
  }
}
```

## Note Tecniche

- Il filtro usa `DISTINCT` nelle query per evitare duplicati
- Le query JOIN sono ottimizzate per performance
- Il caching dei brani per tag funziona correttamente anche con il filtro
- I log sono dettagliati per debugging
- **NUOVO**: Cache separati garantiscono che la playlist sorgente sia completamente utilizzata prima del fallback
- **NUOVO**: Logica di selezione sequenziale evita mescolamento prematuro delle fonti
