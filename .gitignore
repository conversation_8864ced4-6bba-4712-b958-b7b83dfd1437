# Dependencies
node_modules/
package-lock.json
yarn.lock

# Build outputs
dist/
build/
coverage/
*.tgz

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# IDE and editor files
.idea/
.vscode/
*.swp
*.swo
.DS_Store
.env

# Test coverage
.nyc_output/
coverage/

# Temporary files
temp/
tmp/
*.tmp

# Debug files
.debug/

# Misc
.cache/
.env.local
.env.development.local
.env.test.local
.env.production.local