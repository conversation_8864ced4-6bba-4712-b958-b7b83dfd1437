const { contextBridge, ipc<PERSON>enderer } = require('electron/renderer')

contextBridge.exposeInMainWorld('darkMode', {
    isDark: () => ipcRenderer.invoke('dark-mode:isDark'),
    toggle: () => ipcRenderer.invoke('dark-mode:toggle'),
    system: () => ipcRenderer.invoke('dark-mode:system'),
    onUpdate: (callback) => {
        // Register for theme updates from main process
        ipcRenderer.invoke('dark-mode:onUpdate');
        // Set up the event listener for theme updates
        ipcRenderer.on('dark-mode:updated', (event, isDark) => {
            callback(event, isDark);
        });
    }
})

contextBridge.exposeInMainWorld('library', {
    selectFolder: () => ipcRenderer.invoke('library:select-folder'),
    scan: () => ipcRenderer.invoke('library:scan'),
    selectAndScanFolder: () => ipcRenderer.invoke('library:select-and-scan-folder'),
    scanDirectory: (data) => ipcRenderer.invoke('library:scanDirectory', data),
    load: () => ipcRenderer.invoke('library:load'),
    clear: () => ipcRenderer.invoke('library:clear'),
    cleanupDuplicates: () => ipcRenderer.invoke('library:cleanup-duplicates'),
    getLastDirectory: () => ipcRenderer.invoke('library:get-last-directory'),
    getTracks: (data) => ipcRenderer.invoke('library:get-tracks', data),
    onScanStarted: (callback) => {
        ipcRenderer.on('library:scan-started', (event, data) => {
            callback(data);
        });
    },
    onScanProgress: (callback) => {
        ipcRenderer.on('library:scan-progress', (event, data) => {
            callback(data);
        });
    },
    onScanComplete: (callback) => {
        ipcRenderer.on('library:scan-complete', (event, data) => {
            callback(data);
        });
    }
})

contextBridge.exposeInMainWorld('database', {
    open: () => ipcRenderer.invoke('database:open')
})

contextBridge.exposeInMainWorld('track', {
    updateTags: (data) => ipcRenderer.invoke('track:update-tags', data),
    getPlaylists: (data) => ipcRenderer.invoke('track:getPlaylists', data),
    getAllTags: () => ipcRenderer.invoke('track:getAllTags'),
    get: (data) => ipcRenderer.invoke('track:get', data)
})

// Expose default tags functions
contextBridge.exposeInMainWorld('defaultTags', {
    getAll: () => ipcRenderer.invoke('default-tags:get-all'),
    add: (data) => ipcRenderer.invoke('default-tags:add', data),
    update: (data) => ipcRenderer.invoke('default-tags:update', data),
    delete: (data) => ipcRenderer.invoke('default-tags:delete', data)
})

// Expose playlist-related functions
contextBridge.exposeInMainWorld('playlist', {
    create: (data) => ipcRenderer.invoke('playlist:create', data),
    getAll: () => ipcRenderer.invoke('playlist:getAll'),
    get: (data) => ipcRenderer.invoke('playlist:get', data),
    addTracks: (data) => ipcRenderer.invoke('playlist:addTracks', data),
    removeTracks: (data) => ipcRenderer.invoke('playlist:removeTracks', data),
    delete: (data) => ipcRenderer.invoke('playlist:delete', data),
    rename: (data) => ipcRenderer.invoke('playlist:rename', data),
    reorderTracks: (data) => ipcRenderer.invoke('playlist:reorderTracks', data),
    reorderCycles: (data) => ipcRenderer.invoke('playlist:reorderCycles', data),
    swapTracksWithCycles: (data) => ipcRenderer.invoke('playlist:swapTracksWithCycles', data),
    exportM3u8: (data) => ipcRenderer.invoke('playlist:export-m3u8', data),
    replaceTrack: (data) => ipcRenderer.invoke('playlist:replace-track', data),
    replaceTrackByTags: (data) => ipcRenderer.invoke('playlist:replace-track-by-tags', data),
    replaceTrackManually: (data) => ipcRenderer.invoke('playlist:replace-track-manually', data)
})

// Expose playlist generator functions
contextBridge.exposeInMainWorld('playlistGenerator', {
    create: (data) => ipcRenderer.invoke('playlist-generator:create', data),
    getAll: () => ipcRenderer.invoke('playlist-generator:getAll'),
    addSource: (data) => ipcRenderer.invoke('playlist-generator:addSource', data),
    removeSource: (data) => ipcRenderer.invoke('playlist-generator:removeSource', data),
    getSources: (data) => ipcRenderer.invoke('playlist-generator:getSources', data),
    generate: (data) => ipcRenderer.invoke('playlist-generator:generate', data)
})

// Expose tag-based playlist generator functions
contextBridge.exposeInMainWorld('tagPlaylistGenerator', {
    create: (data) => ipcRenderer.invoke('tag-playlist-generator:create', data),
    addSource: (data) => ipcRenderer.invoke('tag-playlist-generator:addSource', data),
    generate: (data) => ipcRenderer.invoke('tag-playlist-generator:generate', data)
})

// Expose the editDialog API for the edit-dialog.html window
contextBridge.exposeInMainWorld('editDialog', {
    onLoadData: (callback) => {
        ipcRenderer.on('track:load-data', (event, data) => {
            callback(data);
        });
    },
    saveData: (data) => {
        ipcRenderer.send('track:save-data', data);
    },
    cancel: () => {
        ipcRenderer.send('track:cancel-edit');
    }
})
