<!DOCTYPE html>
<html>

<head>
  <meta charset="UTF-8">
  <title>Playlist Maker</title>
  <meta http-equiv="Content-Security-Policy"
    content="default-src 'self'; script-src 'self'; style-src 'self' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com" />
  <meta http-equiv="X-Content-Security-Policy"
    content="default-src 'self'; script-src 'self'; style-src 'self' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com" />
  <link rel="stylesheet" type="text/css" href="./styles/style.css">
  <link rel="icon" type="image/svg+xml" href="./assets/music-icon.svg">
</head>

<body>
  <header class="app-header">
    <div class="logo">Playlist Maker</div>
    <div class="theme-controls">
      <button id="theme-toggle" class="theme-toggle" aria-label="Toggle theme">
        <svg class="sun-icon" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
          stroke="currentColor" stroke-width="2">
          <circle cx="12" cy="12" r="5"></circle>
          <line x1="12" y1="1" x2="12" y2="3"></line>
          <line x1="12" y1="21" x2="12" y2="23"></line>
          <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
          <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
          <line x1="1" y1="12" x2="3" y2="12"></line>
          <line x1="21" y1="12" x2="23" y2="12"></line>
          <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
          <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
        </svg>
        <svg class="moon-icon" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
          stroke="currentColor" stroke-width="2">
          <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"></path>
        </svg>
      </button>
    </div>
  </header>

  <main class="app-content">
    <div class="app-layout">
      <!-- Control Panel (Search and Buttons) -->
      <div class="control-panel ma-4">
        <!-- Top row: Search and Buttons -->
        <div class="control-panel-top">
          <div class="search-section">
            <div class="search-container">
              <div class="search-input-wrapper">
                <svg class="search-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" stroke-width="2">
                  <circle cx="11" cy="11" r="8"></circle>
                  <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
                </svg>
                <input type="text" id="search-input" class="search-input"
                  placeholder="Cerca per titolo, artista o tags...">
                <label class="search-include-path-label" title="Include path in search">
                  <input type="checkbox" id="search-include-path" class="checkbox-input">
                  <div class="path-search-wrapper">
                    <svg class="path-search-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                      stroke="currentColor" stroke-width="2">
                      <path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"></path>
                    </svg>
                  </div>
                </label>
                <button id="clear-search-btn" class="clear-search-btn hidden">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                    stroke-width="2">
                    <line x1="18" y1="6" x2="6" y2="18"></line>
                    <line x1="6" y1="6" x2="18" y2="18"></line>
                  </svg>
                </button>
              </div>
            </div>
          </div>

          <div class="actions-section">
            <div class="actions">
              <button id="select-and-scan-btn" class="btn white-outline compact">
                <svg class="icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" stroke-width="2">
                  <path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"></path>
                  <circle cx="17" cy="17" r="3"></circle>
                  <line x1="21" y1="21" x2="19" y2="19"></line>
                </svg>
                <span>Import</span>
              </button>
              <button id="select-all-btn" class="btn white-outline compact">
                <svg class="icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" stroke-width="2">
                  <polyline points="9 11 12 14 22 4"></polyline>
                  <path d="M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11"></path>
                </svg>
                <span>Select All</span>
              </button>
              <button id="deselect-all-btn" class="btn white-outline compact">
                <svg class="icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" stroke-width="2">
                  <line x1="18" y1="6" x2="6" y2="18"></line>
                  <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
                <span>Deselect All</span>
              </button>
              <button id="add-selected-to-playlist-btn" class="btn white-outline compact requires-selection" disabled>
                <svg class="icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" stroke-width="2">
                  <line x1="12" y1="5" x2="12" y2="19"></line>
                  <line x1="5" y1="12" x2="19" y2="12"></line>
                </svg>
                <span>Add to Playlist</span>
              </button>
              <button id="clear-library-btn" class="btn white-outline compact">
                <svg class="icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" stroke-width="2">
                  <polyline points="3 6 5 6 21 6"></polyline>
                  <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                  <line x1="10" y1="11" x2="10" y2="17"></line>
                  <line x1="14" y1="11" x2="14" y2="17"></line>
                </svg>
                <span>Clear</span>
              </button>
              <button id="open-db-btn" class="btn white-outline compact">
                <svg class="icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" stroke-width="2">
                  <path
                    d="M5 8h14M5 12h14M5 16h14M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2z">
                  </path>
                </svg>
                <span>Database</span>
              </button>
            </div>
          </div>
        </div>

        <!-- Middle row: Tags -->
        <div class="tag-filter-bar">
          <div class="tag-filter-header">
            <span class="tag-filter-title">Filter by Tags:</span>
            <button id="clear-tag-filters" class="btn white-outline compact tag-clear-btn">
              <svg class="icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                stroke-width="2">
                <line x1="18" y1="6" x2="6" y2="18"></line>
                <line x1="6" y1="6" x2="18" y2="18"></line>
              </svg>
              <span>Clear</span>
            </button>
          </div>
          <div id="tag-filters-container" class="tag-filters-container">
            <div class="empty-state">No tags available</div>
          </div>
        </div>

        <!-- Bottom row: Results count as status bar -->
        <div id="search-results-count" class="search-results-count hidden">
          Trovati <span id="results-count">0</span> risultati
        </div>
      </div>

      <!-- Main Content Area (Two Columns) -->
      <div class="main-content">
        <!-- Left Column: Playlists -->
        <section class="playlist-section">
          <div class="playlist-header">
            <h2>Playlists</h2>
            <div class="playlist-header-actions">
              <button id="generate-playlist-btn" class="btn primary compact" title="Genera Playlist">
                <img src="./assets/magic-icon.svg" class="icon" alt="Shining star icon">
              </button>
              <button id="generate-tag-playlist-btn" class="btn primary compact" title="Genera Playlist da Tag">
                <span class="hashtag-icon">#</span>
              </button>
              <button id="step-builder-btn" class="btn primary compact" title="Builder Step-by-Step">
                <svg class="icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" stroke-width="2">
                  <path d="M12 2L2 7l10 5 10-5-10-5z"></path>
                  <path d="M2 17l10 5 10-5"></path>
                  <path d="M2 12l10 5 10-5"></path>
                </svg>
              </button>
              <button id="create-playlist-btn" class="btn primary compact" title="Create New Playlist">
                <svg class="icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" stroke-width="2">
                  <line x1="12" y1="5" x2="12" y2="19"></line>
                  <line x1="5" y1="12" x2="19" y2="12"></line>
                </svg>
              </button>
            </div>
          </div>

          <div id="playlists-container" class="playlists-container">
            <div class="empty-state">
              No playlists yet. Create a new playlist to get started.
            </div>
          </div>
        </section>

        <!-- Right Column: Playlist Tracks or All Tracks -->
        <section class="library-section">
          <!-- Playlist Tracks Container (shown when a playlist is selected) -->
          <div id="playlist-tracks-container" class="playlist-tracks-container hidden">
            <div class="playlist-tracks-header">
              <h3 id="current-playlist-name">Playlist Name</h3>
              <div class="playlist-actions">
                <button id="back-to-playlists-btn" class="btn white-outline compact" title="Back to playlists">
                  <svg class="icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2">
                    <path d="M15 18l-6-6 6-6"></path>
                  </svg>
                </button>
                <button id="add-tracks-to-playlist-btn" class="btn white-outline compact"
                  title="Add tracks to playlist">
                  <svg class="icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2">
                    <line x1="12" y1="5" x2="12" y2="19"></line>
                    <line x1="5" y1="12" x2="19" y2="12"></line>
                  </svg>
                </button>
                <button id="swap-tracks-btn" class="btn white-outline compact" title="Swap track positions">
                  <svg class="icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2">
                    <path d="M7 16V4m0 0L3 8m4-4l4 4"></path>
                    <path d="M17 8v12m0 0l4-4m-4 4l-4-4"></path>
                  </svg>
                </button>
                <button id="rename-playlist-btn" class="btn white-outline compact" title="Rename playlist">
                  <svg class="icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2">
                    <path d="M12 20h9"></path>
                    <path d="M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z"></path>
                  </svg>
                </button>
                <button id="delete-playlist-btn" class="btn white-outline compact" title="Delete playlist">
                  <svg class="icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2">
                    <polyline points="3 6 5 6 21 6"></polyline>
                    <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                  </svg>
                </button>
                <button id="export-playlist-m3u8-btn" class="btn white-outline compact"
                  title="Export playlist as .m3u8">
                  <svg class="icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2">
                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                    <polyline points="7 10 12 15 17 10"></polyline>
                    <line x1="12" y1="15" x2="12" y2="3"></line>
                  </svg>
                </button>
              </div>
            </div>
            <div id="playlist-tracks" class="playlist-tracks">
              <div class="empty-state">
                This playlist is empty. Add tracks from your library.
              </div>
            </div>
          </div>

          <!-- All Tracks Container (shown when no playlist is selected) -->
          <div id="track-list" class="track-list">
            <div class="empty-state">
              Select a folder and scan to view your music library
            </div>
          </div>
        </section>
      </div>
    </div>
  </main>

  <!-- Status Bar -->
  <footer class="status-bar">
    <div id="folder-status" class="status-item folder-status hidden">
      <svg class="status-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
        stroke-width="2">
        <path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"></path>
      </svg>
      <span class="status-label">Folder:</span>
      <span id="status-folder-name" class="status-value">None</span>
    </div>
    <div class="status-item">
      <svg class="status-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
        stroke-width="2">
        <path d="M9 18V5l12-2v13"></path>
        <circle cx="6" cy="18" r="3"></circle>
        <circle cx="18" cy="16" r="3"></circle>
      </svg>
      <span class="status-label">Tracks:</span>
      <span id="track-count" class="status-value">0</span>
    </div>
    <div id="selected-tracks-status" class="status-item hidden">
      <svg class="status-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
        stroke-width="2">
        <polyline points="9 11 12 14 22 4"></polyline>
        <path d="M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11"></path>
      </svg>
      <span class="status-label">Selected:</span>
      <span id="selected-count" class="status-value">0</span>
    </div>
    <div class="status-item">
      <svg class="status-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
        stroke-width="2">
        <circle cx="12" cy="12" r="10"></circle>
        <polyline points="12 6 12 12 16 14"></polyline>
      </svg>
      <span class="status-label">Last Update:</span>
      <span id="last-update" class="status-value">Never</span>
    </div>
  </footer>

  <!-- Edit Tags Popup -->
  <div id="edit-tags-popup" class="popup-overlay hidden fully-hidden">
    <div class="popup-container">
      <div class="popup-header">
        <h3>Edit Track Information</h3>
        <button id="close-popup-btn" class="close-btn">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
            stroke-width="2">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>
      <div class="popup-content">
        <form id="edit-tags-form">
          <input type="hidden" id="edit-file-path">
          <div class="form-group">
            <label for="edit-title">Titolo:</label>
            <input type="text" id="edit-title" class="form-control" placeholder="Titolo della canzone">
          </div>
          <div class="form-group">
            <label for="edit-artist">Artista:</label>
            <input type="text" id="edit-artist" class="form-control" placeholder="Nome dell'artista">
          </div>
          <div class="form-group">
            <label for="edit-tags">Tags:</label>
            <input type="text" id="edit-tags" class="form-control"
              placeholder="Inserisci tags separati da virgola (es: hit, nuovo, popolare)">

            <div class="default-tags-section">
              <div class="default-tags-header">
                <label>Tag Predefiniti:</label>
                <button type="button" id="manage-tags-btn" class="btn secondary compact">
                  <svg class="icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2">
                    <path d="M12 20h9"></path>
                    <path d="M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z"></path>
                  </svg>
                  <span>Gestisci</span>
                </button>
              </div>
              <div id="default-tags-container" class="default-tags-container">
                <!-- Default tags will be populated dynamically -->
                <div class="empty-state">Nessun tag predefinito disponibile</div>
              </div>
            </div>
          </div>
          <div class="form-actions">
            <button type="button" id="cancel-edit-btn" class="btn secondary">Cancel</button>
            <button type="submit" id="save-tags-btn" class="btn primary">Save</button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- Create Playlist Popup -->
  <div id="create-playlist-popup" class="popup-overlay hidden fully-hidden">
    <div class="popup-container">
      <div class="popup-header">
        <h3>Create New Playlist</h3>
        <button id="close-playlist-popup-btn" class="close-btn">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
            stroke-width="2">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>
      <div class="popup-content">
        <form id="create-playlist-form">
          <div class="form-group">
            <label for="playlist-name">Nome Playlist:</label>
            <input type="text" id="playlist-name" class="form-control" placeholder="Inserisci il nome della playlist"
              required>
          </div>
          <div class="form-actions">
            <button type="button" id="cancel-playlist-btn" class="btn secondary">Cancel</button>
            <button type="submit" id="save-playlist-btn" class="btn primary">Create Playlist</button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- Add to Playlist Popup -->
  <div id="add-to-playlist-popup" class="popup-overlay hidden fully-hidden">
    <div class="popup-container">
      <div class="popup-header">
        <h3>Add to Playlist</h3>
        <button id="close-add-to-playlist-btn" class="close-btn">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
            stroke-width="2">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>
      <div class="popup-content">
        <div id="playlist-selection-container" class="playlist-selection-container">
          <p>Select a playlist to add the tracks to:</p>
          <div id="playlist-options" class="playlist-options">
            <!-- Playlist options will be populated dynamically -->
            <div class="empty-state">No playlists available. Create a new playlist first.</div>
          </div>
        </div>
        <div class="form-actions">
          <button type="button" id="cancel-add-to-playlist-btn" class="btn secondary">Cancel</button>
          <button type="button" id="create-new-playlist-btn" class="btn primary">Create New Playlist</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Manage Default Tags Popup -->
  <div id="manage-default-tags-popup" class="popup-overlay hidden fully-hidden">
    <div class="popup-container">
      <div class="popup-header">
        <h3>Gestisci Tag Predefiniti</h3>
        <button id="close-default-tags-popup-btn" class="close-btn">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
            stroke-width="2">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>
      <div class="popup-content">
        <div class="default-tags-container">
          <div class="default-tags-list-header">
            <h4>Tag Disponibili</h4>
            <button id="add-default-tag-btn" class="btn primary compact">
              <svg class="icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                stroke-width="2">
                <line x1="12" y1="5" x2="12" y2="19"></line>
                <line x1="5" y1="12" x2="19" y2="12"></line>
              </svg>
              <span>Nuovo Tag</span>
            </button>
          </div>
          <div id="default-tags-list" class="default-tags-list">
            <!-- Default tags will be populated dynamically -->
            <div class="empty-state">Nessun tag predefinito disponibile. Aggiungi un nuovo tag.</div>
          </div>
        </div>
        <div class="form-actions">
          <button type="button" id="close-manage-tags-btn" class="btn primary">Chiudi</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Add/Edit Default Tag Popup -->
  <div id="edit-default-tag-popup" class="popup-overlay hidden fully-hidden">
    <div class="popup-container">
      <div class="popup-header">
        <h3 id="edit-default-tag-title">Aggiungi Tag Predefinito</h3>
        <button id="close-edit-default-tag-btn" class="close-btn">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
            stroke-width="2">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>
      <div class="popup-content">
        <form id="edit-default-tag-form">
          <input type="hidden" id="default-tag-id">
          <div class="form-group">
            <label for="default-tag-name">Nome Tag:</label>
            <input type="text" id="default-tag-name" class="form-control" placeholder="Inserisci il nome del tag"
              required>
          </div>
          <div class="form-group">
            <label for="default-tag-color">Colore (opzionale):</label>
            <input type="color" id="default-tag-color" class="form-control color-picker" value="#4a90e2">
          </div>
          <div class="form-actions">
            <button type="button" id="cancel-default-tag-btn" class="btn secondary">Annulla</button>
            <button type="submit" id="save-default-tag-btn" class="btn primary">Salva</button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- Playlist Generator Popup -->
  <div id="playlist-generator-popup" class="popup-overlay hidden fully-hidden">
    <div class="popup-container">
      <div class="popup-header">
        <h3>Genera Playlist</h3>
        <button id="close-generator-popup-btn" class="close-btn">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
            stroke-width="2">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>
      <div class="popup-content">
        <form id="playlist-generator-form">
          <div class="form-group">
            <label for="generator-playlist-name">Nome Playlist:</label>
            <input type="text" id="generator-playlist-name" class="form-control"
              placeholder="Inserisci il nome della playlist" required>
          </div>
          <div class="form-group">
            <label for="generator-duration">Durata Target (minuti):</label>
            <input type="number" id="generator-duration" class="form-control" min="1" value="90" required>
          </div>

          <div class="form-group">
            <label>Playlist Sorgenti:</label>
            <div id="generator-playlists" class="generator-playlists">
              <!-- Playlist sources will be populated dynamically -->
              <div class="empty-state">Nessuna playlist disponibile. Crea prima delle playlist.</div>
            </div>
          </div>

          <div class="form-group">
            <label>Schema Generazione:</label>
            <div id="generator-schema" class="generator-schema">
              <!-- Schema will be updated dynamically based on selected playlists -->
              <div class="empty-state">Seleziona le playlist sorgenti per visualizzare lo schema</div>
            </div>
          </div>

          <div class="form-actions">
            <button type="button" id="cancel-generator-btn" class="btn secondary">Annulla</button>
            <button type="submit" id="generate-playlist-btn" class="btn primary">Genera Playlist</button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- Swap Tracks Popup -->
  <div id="swap-tracks-popup" class="popup-overlay hidden fully-hidden">
    <div class="popup-container">
      <div class="popup-header">
        <h3>Scambia Posizione Tracce</h3>
        <button id="close-swap-tracks-btn" class="close-btn">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
            stroke-width="2">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>
      <div class="popup-content">
        <form id="swap-tracks-form">
          <div class="form-group">
            <label for="swap-track-position-1">Posizione Traccia 1:</label>
            <input type="number" id="swap-track-position-1" class="form-control" min="1" required>
          </div>
          <div class="form-group">
            <label for="swap-track-position-2">Posizione Traccia 2:</label>
            <input type="number" id="swap-track-position-2" class="form-control" min="1" required>
          </div>
          <div class="form-actions">
            <button type="button" id="cancel-swap-tracks-btn" class="btn secondary">Annulla</button>
            <button type="submit" id="confirm-swap-tracks-btn" class="btn primary">Scambia</button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- Rename Playlist Popup -->
  <div id="rename-playlist-popup" class="popup-overlay hidden fully-hidden">
    <div class="popup-container">
      <div class="popup-header">
        <h3>Rinomina Playlist</h3>
        <button id="close-rename-playlist-btn" class="close-btn">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
            stroke-width="2">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>
      <div class="popup-content">
        <form id="rename-playlist-form">
          <div class="form-group">
            <label for="new-playlist-name">Nuovo Nome:</label>
            <input type="text" id="new-playlist-name" class="form-control"
              placeholder="Inserisci il nuovo nome della playlist" required>
          </div>
          <div class="form-actions">
            <button type="button" id="cancel-rename-playlist-btn" class="btn secondary">Annulla</button>
            <button type="submit" id="confirm-rename-playlist-btn" class="btn primary">Rinomina</button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- Tag-based Playlist Generator Popup -->
  <div id="tag-playlist-generator-popup" class="popup-overlay hidden fully-hidden">
    <div class="popup-container">
      <div class="popup-header">
        <h3>Genera Playlist da Tag</h3>
        <button id="close-tag-generator-popup-btn" class="close-btn">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
            stroke-width="2">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>
      <div class="popup-content">
        <form id="tag-playlist-generator-form">
          <div class="form-group">
            <label for="tag-generator-playlist-name">Nome Playlist:</label>
            <input type="text" id="tag-generator-playlist-name" class="form-control"
              placeholder="Inserisci il nome della playlist" required>
          </div>

          <div class="form-group">
            <label for="tag-generator-duration">Durata Target (minuti):</label>
            <input type="number" id="tag-generator-duration" class="form-control" min="1" value="90" required>
          </div>

          <div class="form-group">
            <label for="tag-generator-source-playlist">Source Playlist (Optional):</label>
            <select id="tag-generator-source-playlist" class="form-control">
              <option value="">None - Search all tracks</option>
            </select>
            <small class="form-help">Limit search to tracks from a specific playlist</small>
          </div>

          <!-- Commented out Available Tags section as requested
          <div class="form-group">
            <label>Tag Disponibili:</label>
            <div id="tag-generator-available-tags" class="tag-generator-available-tags">
              <div class="empty-state">Nessun tag disponibile. Aggiungi prima dei tag ai brani.</div>
            </div>
          </div>
          -->

          <div class="form-group">
            <label>Schema Generazione:</label>
            <div id="tag-generator-schema" class="tag-generator-schema">
              <!-- Schema will be updated dynamically based on selected tags -->
              <div class="empty-state">Aggiungi combinazioni di tag per generare la playlist</div>
            </div>
            <button type="button" id="add-tag-schema-entry-btn" class="btn secondary compact mt-2">
              <svg class="icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                stroke-width="2">
                <line x1="12" y1="5" x2="12" y2="19"></line>
                <line x1="5" y1="12" x2="19" y2="12"></line>
              </svg>
              <span>Aggiungi Combinazione</span>
            </button>
          </div>

          <div class="form-actions">
            <button type="button" id="cancel-tag-generator-btn" class="btn secondary">Annulla</button>
            <button type="submit" id="generate-tag-playlist-btn" class="btn primary">Genera Playlist</button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- Step Builder Popup -->
  <div id="step-builder-popup" class="popup-overlay hidden fully-hidden">
    <div class="popup-container step-builder-container">
      <div class="popup-header">
        <h3>Builder Step-by-Step</h3>
        <button id="close-step-builder-popup-btn" class="close-btn">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
            stroke-width="2">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>
      <div class="popup-content">
        <!-- Step Navigation -->
        <div class="step-navigation">
          <div class="step-indicator" data-step="1">
            <span class="step-number">1</span>
            <span class="step-label">Configurazione</span>
          </div>
          <div class="step-indicator" data-step="2">
            <span class="step-number">2</span>
            <span class="step-label">Schema</span>
          </div>
          <div class="step-indicator" data-step="3">
            <span class="step-number">3</span>
            <span class="step-label">Generazione</span>
          </div>
        </div>

        <!-- Step 1: Configuration -->
        <div id="step-1" class="step-content active">
          <h4>Configurazione Base</h4>
          <form id="step-builder-config-form">
            <div class="form-group">
              <label for="step-builder-playlist-name">Nome Playlist:</label>
              <input type="text" id="step-builder-playlist-name" class="form-control"
                placeholder="Inserisci il nome della playlist" required>
            </div>
            <div class="form-group">
              <label for="step-builder-duration">Durata Target (minuti):</label>
              <input type="number" id="step-builder-duration" class="form-control" min="1" value="90" required>
            </div>
            <div class="step-actions">
              <button type="button" id="step-1-next" class="btn primary">Avanti</button>
            </div>
          </form>
        </div>

        <!-- Step 2: Schema Building -->
        <div id="step-2" class="step-content">
          <h4>Costruisci lo Schema</h4>
          <div class="form-group">
            <label>Combinazioni di Tag:</label>
            <div id="step-builder-schema" class="step-builder-schema">
              <div class="empty-state">Aggiungi combinazioni per costruire lo schema</div>
            </div>
            <button type="button" id="add-step-schema-entry-btn" class="btn secondary compact mt-2">
              <svg class="icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                stroke-width="2">
                <line x1="12" y1="5" x2="12" y2="19"></line>
                <line x1="5" y1="12" x2="19" y2="12"></line>
              </svg>
              <span>Aggiungi Combinazione</span>
            </button>
          </div>
          <div class="step-actions">
            <button type="button" id="step-2-prev" class="btn secondary">Indietro</button>
            <button type="button" id="step-2-next" class="btn primary">Avanti</button>
          </div>
        </div>

        <!-- Step 3: Generation -->
        <div id="step-3" class="step-content">
          <h4>Generazione Playlist</h4>
          <div id="step-builder-generation" class="step-builder-generation">
            <div class="empty-state">Torna al passo precedente per configurare lo schema</div>
          </div>
          <div class="step-actions">
            <button type="button" id="step-3-prev" class="btn secondary">Indietro</button>
            <button type="button" id="step-3-finish" class="btn primary">Completa</button>
            <button type="button" id="step-3-add-cycle" class="btn secondary">Aggiungi Ciclo</button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Track Replacement Options Popup -->
  <div id="track-replacement-popup" class="popup-overlay hidden fully-hidden">
    <div class="popup-container">
      <div class="popup-header">
        <h3>Replace Track</h3>
        <button id="close-track-replacement-btn" class="close-btn">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
            stroke-width="2">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>
      <div class="popup-content">
        <div class="replacement-options">
          <h4>Replacement Options</h4>
          <div class="replacement-option" data-mode="exact">
            <div class="option-header">
              <h5>Exact Tag Match</h5>
              <p>Replace with a track that has exactly the same tags</p>
            </div>
            <button class="btn primary replace-btn" data-mode="exact">Replace</button>
          </div>
          <div class="replacement-option" data-mode="partial">
            <div class="option-header">
              <h5>Partial Tag Match</h5>
              <p>Replace with a track that has at least one matching tag</p>
            </div>
            <button class="btn primary replace-btn" data-mode="partial">Replace</button>
          </div>
          <div class="replacement-option" data-mode="custom">
            <div class="option-header">
              <h5>Custom Tag Selection</h5>
              <p>Select specific tags to match</p>
            </div>
            <div class="custom-tag-selection hidden">
              <div id="available-tags-for-replacement" class="available-tags">
                <!-- Tags will be populated dynamically -->
              </div>
            </div>
            <button class="btn secondary select-tags-btn">Select Tags</button>
            <button class="btn primary replace-custom-btn hidden" data-mode="custom">Replace with Selected Tags</button>
          </div>
          <div class="replacement-option" data-mode="manual">
            <div class="option-header">
              <h5>Manual Track Selection</h5>
              <p>Search and manually select a track to replace with</p>
            </div>
            <div class="manual-track-selection hidden">
              <div class="manual-search-container">
                <div class="search-input-wrapper">
                  <svg class="search-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2">
                    <circle cx="11" cy="11" r="8"></circle>
                    <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
                  </svg>
                  <input type="text" id="manual-search-input" class="manual-search-input"
                    placeholder="Search by title, artist, filename...">
                  <label class="manual-search-include-path-label" title="Include path in search">
                    <input type="checkbox" id="manual-search-include-path" class="checkbox-input">
                    <div class="path-search-wrapper">
                      <svg class="path-search-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2">
                        <path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"></path>
                      </svg>
                    </div>
                  </label>
                </div>
              </div>
              <div id="manual-search-results" class="manual-search-results">
                <div class="empty-state">Start typing to search for tracks...</div>
              </div>
              <div id="selected-track-info" class="selected-track-info hidden">
                <h6>Selected Track:</h6>
                <div class="selected-track-details"></div>
              </div>
            </div>
            <button class="btn secondary select-manual-btn">Select Manually</button>
            <button class="btn primary replace-manual-btn hidden" data-mode="manual" disabled>Replace with Selected
              Track</button>
          </div>
        </div>
        <div class="form-actions mt-3">
          <button type="button" id="cancel-track-replacement-btn" class="btn secondary">Cancel</button>
        </div>
      </div>
    </div>
  </div>

  <script src="./scripts/renderer.js"></script>
</body>

</html>