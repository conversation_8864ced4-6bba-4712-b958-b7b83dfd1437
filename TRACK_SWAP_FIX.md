# Track Swap Fix for Generated Playlists

## Problem Description

The track position swapping function within generated playlists was not working correctly for cross-cycle swaps. When swapping tracks from different cycles, they would only change order within their current cycle instead of truly swapping positions between different cycles. The tracks ended up being reordered to the bottom of their cycle rather than maintaining their intended swap positions.

## Root Cause Analysis

The issue was in the `handleSwapTracks` function in `scripts/renderer.js`. The function was using the existing `reorderTracks` handler which:

1. **Preserved original cycle information** - The `reorderTracks` handler maintained each track's original cycle number
2. **Only reordered positions** - It didn't update cycle assignments when tracks were swapped between different cycles
3. **Caused tracks to return to their original cycles** - After the database update, tracks would be repositioned within their original cycles

## Solution Implementation

### 1. Enhanced Track Swap Detection

Modified `handleSwapTracks` function to:

- Extract cycle information from track elements
- Detect cross-cycle vs same-cycle swaps
- Route to appropriate handler based on swap type

```javascript
// Check if this is a cross-cycle swap in a tag-generated playlist
const isCrossCycleSwap =
  track1Cycle !== null && track2Cycle !== null && track1Cycle !== track2Cycle;
```

### 2. New Cross-Cycle Swap Handler

Created `swapTracksWithCycles` handler in `scripts/database-operations.js`:

- Specifically handles cross-cycle track swaps
- Updates both position AND cycle information
- Ensures tracks truly swap their positions and cycle assignments

```javascript
ipcMain.handle(
  "playlist:swapTracksWithCycles",
  async (
    _,
    {
      playlistId,
      track1Id,
      track1Position,
      track1Cycle,
      track2Id,
      track2Position,
      track2Cycle,
    }
  ) => {
    // Implementation that swaps both positions and cycles
  }
);
```

### 3. Backward Compatibility

Maintained existing functionality for:

- Same-cycle swaps (uses existing `reorderTracks` handler)
- Non-cycle playlists (regular manual playlists)
- Drag and drop reordering (unchanged)

## Files Modified

### `scripts/renderer.js`

- **Function**: `handleSwapTracks` (lines 5295-5404)
- **Changes**: Added cycle detection and routing logic
- **Functions**: `handleTrackUpClick` and `handleTrackDownClick` (lines 5407-5578)
- **Changes**: Fixed swap logic for up/down arrows to use simple array swapping instead of splice operations

### `scripts/database-operations.js`

- **Addition**: New `swapTracksWithCycles` handler (lines 695-778)
- **Purpose**: Handle cross-cycle swaps with proper cycle updates

### `scripts/preload.js`

- **Addition**: Exposed `swapTracksWithCycles` function (line 74)
- **Purpose**: Make the new handler available to the renderer process

## Testing

### Test Scenarios

1. **Cross-cycle swap**: Track from cycle 1 swapped with track from cycle 2

   - ✅ Should use `swapTracksWithCycles` handler
   - ✅ Should update both positions and cycle assignments

2. **Same-cycle swap**: Track from cycle 1 swapped with another track from cycle 1

   - ✅ Should use existing `reorderTracks` handler
   - ✅ Should maintain cycle assignments

3. **Regular playlist swap**: Tracks in non-generated playlists
   - ✅ Should use existing `reorderTracks` handler
   - ✅ Should work as before (no cycles involved)

### Manual Testing Steps

1. Create a tag-generated playlist with multiple cycles
2. Open the swap tracks popup (swap button in playlist toolbar)
3. Enter positions for tracks from different cycles (e.g., position 2 and position 8)
4. Confirm the swap
5. Verify that:
   - Tracks actually swap their display positions
   - Track from cycle 1 now appears in cycle 2's position
   - Track from cycle 2 now appears in cycle 1's position
   - No tracks get moved to the bottom of their original cycle

## Benefits

1. **True cross-cycle swapping** - Tracks can now be swapped between any cycles
2. **Maintains intended positions** - Tracks stay exactly where the user wants them
3. **Preserves existing functionality** - Same-cycle swaps and regular playlists work as before
4. **Database consistency** - Proper cycle and position updates in the database
5. **UI responsiveness** - Immediate visual feedback after swapping

## Technical Details

### Database Schema Impact

- Uses existing `playlist_tracks` table structure
- Updates both `position` and `cycle` columns for cross-cycle swaps
- Maintains referential integrity with transactions

### Performance Considerations

- Cross-cycle swaps require a full playlist rebuild (DELETE + INSERT)
- Same-cycle swaps use the more efficient existing logic
- Transaction-based updates ensure data consistency

### Error Handling

- Validates track positions before swapping
- Provides user feedback for success/failure
- Rolls back database changes on errors
- Maintains application stability

## Future Enhancements

1. **Bulk cross-cycle swaps** - Support for swapping multiple tracks between cycles
2. **Cycle-aware drag and drop** - Extend drag and drop to support cross-cycle moves
3. **Visual cycle indicators** - Enhanced UI to show cycle boundaries during swaps
4. **Undo functionality** - Allow users to undo recent swap operations
