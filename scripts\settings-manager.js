const { app } = require('electron/main');
const path = require('node:path');
const fs = require('fs/promises');

let appSettingsInternal = {}; // Internal cache of settings

async function loadSettings() {
    const settingsPath = path.join(app.getPath('userData'), 'settings.json');
    try {
        const data = await fs.readFile(settingsPath, 'utf8');
        appSettingsInternal = JSON.parse(data);
        console.log('Settings loaded from file:', appSettingsInternal);
    } catch (err) {
        if (err.code === 'ENOENT') {
            appSettingsInternal = { lastSelectedDirectory: null };
            console.log('No settings file found (settings.json), using default settings.');
        } else {
            console.error('Error reading settings file (settings.json):', err);
            appSettingsInternal = { lastSelectedDirectory: null }; // Fallback to defaults on other errors
        }
    }
    return appSettingsInternal; // Return the loaded (or default) settings
}

async function saveSettings(settingsToSave) {
    const settingsPath = path.join(app.getPath('userData'), 'settings.json');
    try {
        // Update internal cache with the settings being saved
        appSettingsInternal = settingsToSave;
        await fs.writeFile(settingsPath, JSON.stringify(settingsToSave, null, 2), 'utf8');
        console.log('Settings saved successfully to:', settingsPath);
    } catch (error) {
        console.error('Failed to save settings (settings.json):', error);
    }
}

// Optional: A function to get the current settings state if needed by other modules
// (though typically the main process would manage the appSettings object directly after loading)
function getAppSettings() {
    return appSettingsInternal;
}

module.exports = {
    loadSettings,
    saveSettings,
    getAppSettings
}; 