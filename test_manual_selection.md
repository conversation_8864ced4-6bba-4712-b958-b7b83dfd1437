# Manual Track Selection - Test Checklist

## Prerequisites

1. Application is running
2. Music library is loaded with tracks
3. At least one tag-generated playlist exists with tracks
4. **CRITICAL FIX VERIFICATION**: Ensure no `ReferenceError: escapeHtml is not defined` errors occur

## Test Steps

### 1. Access Manual Selection Feature

- [ ] Open a tag-generated playlist
- [ ] Click the "Replace" button (🔄) on any track
- [ ] Verify the Replace Track modal opens
- [ ] Confirm "Manual Track Selection" option is visible as the fourth option

### 2. Activate Manual Selection

- [ ] Click "Select Manually" button
- [ ] Verify the manual selection interface appears
- [ ] Confirm search input field is visible and focused
- [ ] Check that path inclusion checkbox is present
- [ ] Verify "Replace with Selected Track" button is disabled

### 3. Search Functionality

- [ ] **CRITICAL**: Open browser console (F12) and verify no JavaScript errors
- [ ] Type a search term in the input field
- [ ] Verify search results appear in real-time WITHOUT any `escapeHtml` errors
- [ ] Test search with special characters (e.g., `<`, `>`, `&`, `"`, `'`) in track names
- [ ] Test search with different terms (title, artist, tags)
- [ ] Toggle path inclusion checkbox and verify it affects results
- [ ] Verify empty search shows "Start typing to search..." message
- [ ] Test with no results and verify appropriate message

### 4. Track Selection

- [ ] Click on a track in the search results
- [ ] Verify the track is highlighted/selected
- [ ] Confirm selected track information appears below results
- [ ] Check that "Replace with Selected Track" button becomes enabled
- [ ] Select a different track and verify selection updates

### 5. Track Replacement

- [ ] With a track selected, click "Replace with Selected Track"
- [ ] Verify loading state is shown
- [ ] Confirm modal closes after successful replacement
- [ ] Check that playlist view refreshes
- [ ] Verify the track was actually replaced in the playlist

### 6. Error Handling

- [ ] Try to replace with a track already in the playlist (should show error)
- [ ] Test with invalid data (should handle gracefully)
- [ ] Cancel the operation and verify state is reset

### 7. UI/UX Verification

- [ ] Verify consistent styling with rest of application
- [ ] Check responsive behavior
- [ ] Confirm modal can be closed with X button or clicking outside
- [ ] Verify all buttons have appropriate hover states

## Expected Results

### Search Results Display

- Track title, artist, duration
- Tags displayed as small badges
- Clickable items with hover effects
- Maximum 50 results shown

### Selected Track Info

- Clear display of selected track details
- Artist and duration information
- Proper formatting and styling

### Replacement Process

- Smooth loading states
- Clear success/error feedback
- Automatic playlist refresh
- Modal closure on completion

## Common Issues to Check

1. **🔥 FIXED: escapeHtml Error**: Previously caused `ReferenceError: escapeHtml is not defined` - now resolved
2. **Search not working**: Check console for errors, verify IPC handlers
3. **Results not displaying**: Verify CSS classes and DOM structure
4. **Selection not working**: Check event listeners and click handlers
5. **Replace button not enabling**: Verify selection state management
6. **Replacement failing**: Check database operations and error handling

## Browser Console Checks

Look for these log messages:

- "Opening track replacement popup for track: [ID]"
- "Manual track selection initialized"
- "Search results: [count] tracks found"
- "Track selected for replacement: [track info]"
- "Successfully replaced track [oldId] with track [newId]"

## Success Criteria

✅ All test steps pass without errors
✅ Feature integrates seamlessly with existing functionality
✅ User experience is intuitive and consistent
✅ Performance is acceptable (search responds quickly)
✅ Error handling works properly
✅ No console errors or warnings
