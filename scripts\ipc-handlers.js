const { ipcMain } = require('electron/main');

function initialize(appContext, electronModules, localModules) {
    const { dialog, shell, nativeTheme, BrowserWindow, app, fs, fsSync, path, NodeID3 } = electronModules;
    const { dbOps, fileOps, settingsManager } = localModules;

    ipcMain.handle('dark-mode:isDark', () => {
        return nativeTheme.shouldUseDarkColors;
    });

    ipcMain.handle('dark-mode:toggle', () => {
        nativeTheme.themeSource = nativeTheme.shouldUseDarkColors ? 'light' : 'dark';
        return nativeTheme.shouldUseDarkColors;
    });

    ipcMain.handle('dark-mode:system', () => {
        nativeTheme.themeSource = 'system';
        return nativeTheme.shouldUseDarkColors;
    });

    ipcMain.handle('dark-mode:onUpdate', (event) => {
        nativeTheme.on('updated', () => {
            const isDark = nativeTheme.shouldUseDarkColors;
            event.sender.send('dark-mode:updated', isDark);
        });
    });

    ipcMain.handle('database:open', async () => {
        try {
            const dbPath = path.join(app.getPath('userData'), 'playlist-maker.sqlite');
            console.log('Opening database file at:', dbPath);
            await fs.access(dbPath);
            await shell.openPath(dbPath);
            return { success: true, path: dbPath };
        } catch (error) {
            console.error('Error opening database file:', error);
            return { success: false, error: error.message };
        }
    });

    ipcMain.handle('playlist:export-m3u8', async (_, { playlistId, playlistName }) => {
        console.log(`Exporting playlist with ID: ${playlistId} as .m3u8`);
        const db = dbOps.getDB();
        try {
            const tracks = await new Promise((resolve, reject) => {
                db.all(`
                    SELECT t.path, t.title, t.artist
                    FROM tracks t
                    JOIN playlist_tracks pt ON t.id = pt.track_id
                    WHERE pt.playlist_id = ?
                    ORDER BY pt.position, t.name COLLATE NOCASE
                `, [playlistId], (err, rows) => {
                    if (err) {
                        console.error(`Error getting tracks for playlist ${playlistId}:`, err.message);
                        reject(new Error(`Failed to get playlist tracks: ${err.message}`));
                    } else {
                        resolve(rows);
                    }
                });
            });

            if (!tracks || tracks.length === 0) {
                return { success: false, error: 'Playlist is empty' };
            }

            let m3u8Content = '#EXTM3U\n';
            for (const track of tracks) {
                const title = track.title || path.basename(track.path);
                const artist = track.artist || 'Unknown Artist';
                m3u8Content += `#EXTINF:-1,${artist} - ${title}\n`;
                m3u8Content += `${track.path}\n`;
            }

            if (!playlistName || playlistName.trim() === '') {
                playlistName = 'playlist';
            }
            let sanitizedPlaylistName = playlistName.replace(/[\\/:*?"<>|]/g, '_').trim().replace(/\s+/g, '_').replace(/[^\w\-\.]/g, '_');
            if (sanitizedPlaylistName.length > 100) {
                sanitizedPlaylistName = sanitizedPlaylistName.substring(0, 100);
            }
            const defaultFileName = `${sanitizedPlaylistName}.m3u8`;
            const musicPath = app.getPath('music');
            const fullDefaultPath = path.join(musicPath, defaultFileName);

            const result = await dialog.showSaveDialog(appContext.getMainWindow(), {
                title: 'Save Playlist as .m3u8',
                defaultPath: fullDefaultPath,
                filters: [
                    { name: 'M3U8 Playlists', extensions: ['m3u8'] },
                    { name: 'All Files', extensions: ['*'] }
                ]
            });

            if (result.canceled) {
                return { success: false, canceled: true };
            }

            let finalFilePath = result.filePath;
            if (!finalFilePath.toLowerCase().endsWith('.m3u8')) {
                finalFilePath += '.m3u8';
            }
            await fs.writeFile(finalFilePath, m3u8Content, 'utf8');
            return { success: true, filePath: finalFilePath, trackCount: tracks.length };
        } catch (error) {
            console.error('Error exporting playlist:', error);
            return { success: false, error: error.message };
        }
    });

    ipcMain.handle('track:edit-dialog', async (event, { filePath, title, artist, tags }) => {
        const preloadPath = appContext.getPreloadPath();
        const htmlPath = path.join(appContext.getAppPath(), 'edit-dialog.html');

        const editWindow = new BrowserWindow({
            parent: appContext.getMainWindow(),
            modal: true,
            width: 500,
            height: 400,
            minimizable: false,
            maximizable: false,
            resizable: false,
            webPreferences: {
                preload: preloadPath,
                nodeIntegration: false,
                contextIsolation: true,
                additionalArguments: [`--dialog-id=${Date.now()}`]
            },
            show: false
        });

        await editWindow.loadFile(htmlPath);

        editWindow.webContents.on('did-finish-load', () => {
            editWindow.webContents.send('track:load-data', { filePath, title, artist, tags });
            // Uncomment to debug the dialog
            // editWindow.webContents.openDevTools();

            // Delay showing the window to ensure it's fully loaded
            setTimeout(() => {
                editWindow.show();

                // Send a focus event to ensure the window is properly focused
                editWindow.focus();

                // Send a second load data event after a delay to ensure inputs are properly initialized
                setTimeout(() => {
                    if (!editWindow.isDestroyed()) {
                        editWindow.webContents.send('track:load-data', { filePath, title, artist, tags });
                    }
                }, 500);
            }, 200);
        });

        return new Promise((resolve) => {
            ipcMain.once('track:save-data', async (_, data) => {
                if (editWindow.isDestroyed()) return resolve({ success: false, error: 'Window closed prematurely' });
                try {
                    await fs.access(data.filePath);
                    const id3Tags = { title: data.title, artist: data.artist };
                    const writeSuccess = NodeID3.write(id3Tags, data.filePath);

                    if (writeSuccess) {
                        const db = dbOps.getDB();
                        // Get file stats for modification time
                        const fileStats = await fs.stat(data.filePath);
                        const updatedAt = fileStats.mtime.toISOString();

                        await new Promise((resolveDb, rejectDb) => {
                            const stmt = db.prepare(`UPDATE tracks SET title = ?, artist = ?, tags = ?, updatedAt = ? WHERE path = ?`);
                            stmt.run(data.title, data.artist, data.tags, updatedAt, data.filePath, function (err) {
                                if (err) rejectDb(err);
                                else resolveDb();
                            });
                            stmt.finalize();
                        });
                        if (!editWindow.isDestroyed()) editWindow.close();
                        resolve({ success: true });
                    } else {
                        if (!editWindow.isDestroyed()) editWindow.close();
                        resolve({ success: false, error: 'Failed to write ID3 tags' });
                    }
                } catch (error) {
                    console.error('Error updating ID3 tags:', error);
                    if (!editWindow.isDestroyed()) editWindow.close();
                    resolve({ success: false, error: error.message });
                }
            });

            ipcMain.once('track:cancel-edit', () => {
                if (!editWindow.isDestroyed()) editWindow.close();
                resolve({ success: false, canceled: true });
            });

            editWindow.on('closed', () => {
                // Ensure already-registered once listeners are removed if window is closed manually
                ipcMain.removeAllListeners('track:save-data');
                ipcMain.removeAllListeners('track:cancel-edit');
                resolve({ success: false, canceled: true });
            });
        });
    });

    ipcMain.handle('track:update-tags', async (_, { filePath, title, artist, tags }) => {
        try {
            await fs.access(filePath);
            const id3Tags = { title: title, artist: artist };
            const success = NodeID3.write(id3Tags, filePath);
            if (success) {
                const db = dbOps.getDB();
                // Get file stats for modification time
                const fileStats = await fs.stat(filePath);
                const updatedAt = fileStats.mtime.toISOString();

                await new Promise((resolve, reject) => {
                    const stmt = db.prepare(`UPDATE tracks SET title = ?, artist = ?, tags = ?, updatedAt = ? WHERE path = ?`);
                    stmt.run(title, artist, tags, updatedAt, filePath, function (err) {
                        if (err) reject(err);
                        else resolve();
                    });
                    stmt.finalize();
                });
                return { success: true };
            } else {
                return { success: false, error: 'Failed to write ID3 tags' };
            }
        } catch (error) {
            console.error('Error updating ID3 tags:', error);
            return { success: false, error: error.message };
        }
    });

    ipcMain.handle('library:select-folder', async () => {
        const result = await dialog.showOpenDialog(appContext.getMainWindow(), {
            properties: ['openDirectory']
        });

        if (!result.canceled && result.filePaths.length > 0) {
            const selectedPath = result.filePaths[0];
            appContext.setSelectedDirectory(selectedPath);
            // The scanDirectory function should be responsible for creating the window if needed.
            await appContext.scanDirectory(selectedPath, false);
            return { success: true, path: selectedPath };
        } else {
            return { success: false, canceled: result.canceled };
        }
    });

    ipcMain.handle('library:rescan-selected-folder', async () => {
        const currentDirectory = appContext.getSelectedDirectory();
        if (currentDirectory) {
            await appContext.scanDirectory(currentDirectory, true); // Pass true for isRescan
            return { success: true, path: currentDirectory };
        } else {
            return { success: false, error: 'No directory selected previously' };
        }
    });

    // Handler to scan the last selected directory
    ipcMain.handle('library:scan', async () => {
        console.log('Scanning last selected directory');
        const currentDirectory = appContext.getSelectedDirectory();
        if (currentDirectory) {
            const scanResult = await appContext.scanDirectory(currentDirectory, false);

            // After scanning, load all tracks to return them
            const db = dbOps.getDB();
            const tracks = await new Promise((resolve, reject) => {
                db.all('SELECT id, unique_id, path, name, extension, title, artist, duration, tags, createdAt, updatedAt FROM tracks ORDER BY artist COLLATE NOCASE, title COLLATE NOCASE', [], (err, rows) => {
                    if (err) {
                        console.error('Error loading tracks after scan:', err.message);
                        reject(new Error(`Failed to load tracks after scan: ${err.message}`));
                    } else {
                        console.log(`Loaded ${rows.length} tracks after scan`);
                        resolve(rows);
                    }
                });
            });

            // Get the last update timestamp
            const lastUpdateRow = await new Promise((resolve, reject) => {
                db.get('SELECT MAX(updatedAt) as lastUpdate FROM tracks', [], (err, row) => {
                    if (err) {
                        console.error('Error getting last update timestamp:', err.message);
                        reject(new Error(`Failed to get last update: ${err.message}`));
                    } else {
                        resolve(row);
                    }
                });
            });

            return {
                success: true,
                files: tracks,
                totalTracks: tracks.length,
                lastUpdate: lastUpdateRow ? lastUpdateRow.lastUpdate : null,
                stats: scanResult.stats,
                directory: currentDirectory
            };
        } else {
            return {
                success: false,
                error: 'No directory selected previously',
                files: [],
                totalTracks: 0,
                lastUpdate: null
            };
        }
    });

    // Handler to scan a specific directory and add tracks to a playlist
    ipcMain.handle('library:scanDirectory', async (_, { directoryPath }) => {
        console.log(`Scanning specific directory: ${directoryPath}`);
        if (!directoryPath) {
            return {
                success: false,
                error: 'No directory specified',
                newTrackIds: []
            };
        }

        // Scan the directory
        const scanResult = await appContext.scanDirectory(directoryPath, false);

        // Get the IDs of newly added tracks
        const db = dbOps.getDB();
        const newTracks = await new Promise((resolve, reject) => {
            db.all('SELECT id FROM tracks WHERE path LIKE ? ORDER BY id DESC LIMIT ?',
                [directoryPath + '%', scanResult.stats.new],
                (err, rows) => {
                    if (err) {
                        console.error('Error getting newly added track IDs:', err.message);
                        reject(new Error(`Failed to get new track IDs: ${err.message}`));
                    } else {
                        resolve(rows);
                    }
                });
        });

        const newTrackIds = newTracks.map(track => track.id);

        return {
            success: true,
            stats: scanResult.stats,
            directory: directoryPath,
            newTrackIds: newTrackIds,
            failedFiles: scanResult.failedFiles || []
        };
    });

    // Handler to load all tracks from the database
    ipcMain.handle('library:load', async () => {
        console.log('Loading all tracks from database');
        try {
            const db = dbOps.getDB();
            const tracks = await new Promise((resolve, reject) => {
                db.all('SELECT id, unique_id, path, name, extension, title, artist, duration, tags, createdAt, updatedAt FROM tracks ORDER BY artist COLLATE NOCASE, title COLLATE NOCASE', [], (err, rows) => {
                    if (err) {
                        console.error('Error loading tracks from database:', err.message);
                        reject(new Error(`Failed to load tracks: ${err.message}`));
                    } else {
                        console.log(`Loaded ${rows.length} tracks from database`);
                        resolve(rows);
                    }
                });
            });

            // Get the last update timestamp
            const lastUpdateRow = await new Promise((resolve, reject) => {
                db.get('SELECT MAX(updatedAt) as lastUpdate FROM tracks', [], (err, row) => {
                    if (err) {
                        console.error('Error getting last update timestamp:', err.message);
                        reject(new Error(`Failed to get last update: ${err.message}`));
                    } else {
                        resolve(row);
                    }
                });
            });

            return {
                files: tracks,
                totalTracks: tracks.length,
                lastUpdate: lastUpdateRow ? lastUpdateRow.lastUpdate : null
            };
        } catch (error) {
            console.error('Error in library:load handler:', error);
            return {
                files: [],
                totalTracks: 0,
                lastUpdate: null,
                error: error.message
            };
        }
    });

    // Handler to select a folder and scan it in one operation
    ipcMain.handle('library:select-and-scan-folder', async () => {
        console.log('Selecting folder and scanning in one operation');
        const result = await dialog.showOpenDialog(appContext.getMainWindow(), {
            properties: ['openDirectory']
        });

        if (!result.canceled && result.filePaths.length > 0) {
            const selectedPath = result.filePaths[0];
            appContext.setSelectedDirectory(selectedPath);

            // Notify the renderer that scanning has started
            if (appContext.getMainWindow() && appContext.getMainWindow().webContents) {
                appContext.getMainWindow().webContents.send('library:scan-started', { directory: selectedPath });
            }

            // Perform the scan
            const scanResult = await appContext.scanDirectory(selectedPath, false);

            // After scanning, load all tracks to return them
            const db = dbOps.getDB();
            const tracks = await new Promise((resolve, reject) => {
                db.all('SELECT id, unique_id, path, name, extension, title, artist, duration, tags, createdAt, updatedAt FROM tracks ORDER BY artist COLLATE NOCASE, title COLLATE NOCASE', [], (err, rows) => {
                    if (err) {
                        console.error('Error loading tracks after scan:', err.message);
                        reject(new Error(`Failed to load tracks after scan: ${err.message}`));
                    } else {
                        console.log(`Loaded ${rows.length} tracks after scan`);
                        resolve(rows);
                    }
                });
            });

            // Get the last update timestamp
            const lastUpdateRow = await new Promise((resolve, reject) => {
                db.get('SELECT MAX(updatedAt) as lastUpdate FROM tracks', [], (err, row) => {
                    if (err) {
                        console.error('Error getting last update timestamp:', err.message);
                        reject(new Error(`Failed to get last update: ${err.message}`));
                    } else {
                        resolve(row);
                    }
                });
            });

            return {
                success: true,
                files: tracks,
                totalTracks: tracks.length,
                lastUpdate: lastUpdateRow ? lastUpdateRow.lastUpdate : null,
                stats: scanResult.stats,
                directory: selectedPath,
                path: selectedPath, // Include path for compatibility with the select-folder handler
                failedFiles: scanResult.failedFiles || []
            };
        } else {
            return {
                success: false,
                canceled: result.canceled,
                files: [],
                totalTracks: 0,
                lastUpdate: null
            };
        }
    });

    // Handler to get the last selected directory
    ipcMain.handle('library:get-last-directory', async () => {
        console.log('Getting last selected directory');
        return appContext.getSelectedDirectory();
    });

    // Handler to clear the library (delete all tracks, playlists, and playlist generators, but preserve default tags)
    ipcMain.handle('library:clear', async () => {
        console.log('Clearing library (tracks, playlists, playlist generators, and tag-related data)');
        try {
            const db = dbOps.getDB();
            let totalDeleted = 0;

            await new Promise((resolve, reject) => {
                db.serialize(() => {
                    db.run('BEGIN TRANSACTION');

                    // Delete playlist tracks first (due to foreign key constraints)
                    db.run('DELETE FROM playlist_tracks', function (err) {
                        if (err) {
                            db.run('ROLLBACK');
                            return reject(new Error(`Failed to delete playlist tracks: ${err.message}`));
                        }
                        totalDeleted += this.changes;
                    });

                    // Delete playlist generator sources
                    db.run('DELETE FROM playlist_generator_sources', function (err) {
                        if (err) {
                            db.run('ROLLBACK');
                            return reject(new Error(`Failed to delete playlist generator sources: ${err.message}`));
                        }
                        totalDeleted += this.changes;
                    });

                    // Delete playlist generators
                    db.run('DELETE FROM playlist_generators', function (err) {
                        if (err) {
                            db.run('ROLLBACK');
                            return reject(new Error(`Failed to delete playlist generators: ${err.message}`));
                        }
                        totalDeleted += this.changes;
                    });

                    // Delete tag-based playlist generator sources
                    db.run('DELETE FROM playlist_tag_generator_sources', function (err) {
                        if (err) {
                            db.run('ROLLBACK');
                            return reject(new Error(`Failed to delete tag-based playlist generator sources: ${err.message}`));
                        }
                        totalDeleted += this.changes;
                    });

                    // Delete tag-based playlist generators
                    db.run('DELETE FROM playlist_tag_generators', function (err) {
                        if (err) {
                            db.run('ROLLBACK');
                            return reject(new Error(`Failed to delete tag-based playlist generators: ${err.message}`));
                        }
                        totalDeleted += this.changes;
                    });

                    // Delete playlists
                    db.run('DELETE FROM playlists', function (err) {
                        if (err) {
                            db.run('ROLLBACK');
                            return reject(new Error(`Failed to delete playlists: ${err.message}`));
                        }
                        totalDeleted += this.changes;
                    });

                    // Delete tracks
                    db.run('DELETE FROM tracks', function (err) {
                        if (err) {
                            db.run('ROLLBACK');
                            return reject(new Error(`Failed to delete tracks: ${err.message}`));
                        }
                        totalDeleted += this.changes;
                    });

                    db.run('COMMIT', (err) => {
                        if (err) {
                            db.run('ROLLBACK');
                            return reject(new Error(`Failed to commit transaction: ${err.message}`));
                        }
                        resolve();
                    });
                });
            });

            return { success: true, count: totalDeleted };
        } catch (error) {
            console.error('Error clearing library:', error);
            return { success: false, error: error.message };
        }
    });

    // Handler to clean up duplicate tracks
    ipcMain.handle('library:cleanup-duplicates', async () => {
        console.log('Cleaning up duplicate tracks');
        try {
            const result = await appContext.cleanupDuplicateTracks();
            return result;
        } catch (error) {
            console.error('Error cleaning up duplicates:', error);
            return { success: false, error: error.message };
        }
    });
}

module.exports = { initialize };